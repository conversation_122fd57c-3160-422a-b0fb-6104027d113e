#!/usr/bin/env python3
"""
Create a PNG version of the advanced report icon
"""

import os
from PIL import Image, ImageDraw

def create_advanced_report_icon():
    """Create a PNG version of the advanced report icon"""
    print("🎨 Creating Advanced Report Icon (PNG)...")
    
    try:
        # Create a 24x24 icon
        size = 24
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Colors
        primary_color = (31, 119, 180)  # Blue
        secondary_color = (255, 127, 14)  # Orange
        text_color = (0, 0, 0)  # Black
        
        # Main document background
        doc_rect = [3, 1, 15, 20]
        draw.rectangle(doc_rect, fill=(255, 255, 255), outline=primary_color, width=1)
        
        # Document header lines
        draw.rectangle([5, 3, 13, 4], fill=primary_color)
        draw.rectangle([5, 5, 11, 5], fill=(128, 128, 128))
        
        # Chart area
        chart_rect = [5, 7, 13, 12]
        draw.rectangle(chart_rect, outline=primary_color, width=1)
        
        # Bar chart bars
        bar_positions = [6, 7.5, 9, 10.5, 12]
        bar_heights = [2, 3, 1.5, 4, 2.5]
        
        for i, (pos, height) in enumerate(zip(bar_positions, bar_heights)):
            bar_color = primary_color if i % 2 == 0 else secondary_color
            draw.rectangle([pos, 12-height, pos+0.8, 12], fill=bar_color)
        
        # Data table lines
        for y in [14, 15, 16]:
            draw.line([5, y, 13, y], fill=(200, 200, 200), width=1)
        
        # Vertical table lines
        for x in [7, 9, 11]:
            draw.line([x, 13.5, x, 16.5], fill=(200, 200, 200), width=1)
        
        # Export format indicators
        # PDF circle
        draw.ellipse([16, 4, 21, 9], outline=primary_color, width=1)
        draw.text((17.5, 5.5), "PDF", fill=text_color)
        
        # HTML circle
        draw.ellipse([17, 10, 21, 14], outline=secondary_color, width=1)
        draw.text((18, 11), "HTML", fill=text_color)
        
        # Excel circle
        draw.ellipse([16, 16, 20, 20], outline=(0, 128, 0), width=1)
        draw.text((17, 17), "XLS", fill=text_color)
        
        # Export arrows
        draw.line([15, 6, 16, 6], fill=primary_color, width=1)
        draw.line([15, 12, 17, 12], fill=secondary_color, width=1)
        draw.line([15, 18, 16, 18], fill=(0, 128, 0), width=1)
        
        # Save the icon
        icon_path = "src/icons/advanced_report.png"
        img.save(icon_path, "PNG")
        
        print(f"✅ Advanced report icon created: {icon_path}")
        
        # Also create a larger version for better quality
        large_img = img.resize((48, 48), Image.LANCZOS)
        large_icon_path = "src/icons/advanced_report_48.png"
        large_img.save(large_icon_path, "PNG")
        
        print(f"✅ Large advanced report icon created: {large_icon_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create advanced report icon: {e}")
        return False

def create_simple_report_icon():
    """Create a simple fallback report icon"""
    print("📊 Creating Simple Report Icon...")
    
    try:
        size = 24
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Simple document with chart
        # Document outline
        draw.rectangle([2, 2, 18, 22], fill=(255, 255, 255), outline=(0, 0, 0), width=2)
        
        # Title bar
        draw.rectangle([4, 4, 16, 6], fill=(31, 119, 180))
        
        # Chart area
        draw.rectangle([4, 8, 16, 14], outline=(31, 119, 180), width=1)
        
        # Simple bars
        draw.rectangle([5, 12, 6, 13], fill=(31, 119, 180))
        draw.rectangle([7, 10, 8, 13], fill=(255, 127, 14))
        draw.rectangle([9, 11, 10, 13], fill=(31, 119, 180))
        draw.rectangle([11, 9, 12, 13], fill=(255, 127, 14))
        draw.rectangle([13, 11, 14, 13], fill=(31, 119, 180))
        draw.rectangle([15, 10, 16, 13], fill=(255, 127, 14))
        
        # Data lines
        for y in [16, 17, 18, 19]:
            draw.line([4, y, 16, y], fill=(200, 200, 200), width=1)
        
        # Export indicator
        draw.text((19, 10), "📊", fill=(0, 0, 0))
        
        # Save the icon
        icon_path = "src/icons/report_simple.png"
        img.save(icon_path, "PNG")
        
        print(f"✅ Simple report icon created: {icon_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create simple report icon: {e}")
        return False

def test_icon_loading():
    """Test if the icons can be loaded"""
    print("\n🔍 Testing Icon Loading...")
    
    try:
        from PySide6.QtGui import QIcon
        import os
        
        # Test different icon formats
        icon_files = [
            "src/icons/advanced_report.svg",
            "src/icons/advanced_report.png", 
            "src/icons/report_simple.png"
        ]
        
        for icon_file in icon_files:
            if os.path.exists(icon_file):
                try:
                    icon = QIcon(icon_file)
                    if not icon.isNull():
                        print(f"✅ {icon_file} loads correctly")
                    else:
                        print(f"⚠️ {icon_file} exists but QIcon is null")
                except Exception as e:
                    print(f"❌ {icon_file} failed to load: {e}")
            else:
                print(f"❌ {icon_file} does not exist")
        
        return True
        
    except Exception as e:
        print(f"❌ Icon loading test failed: {e}")
        return False

def main():
    """Main function"""
    print("🎨 DeformViz 3D Report Icon Creator")
    print("=" * 40)
    
    # Create icons
    success1 = create_advanced_report_icon()
    success2 = create_simple_report_icon()
    
    # Test loading
    success3 = test_icon_loading()
    
    print("\n" + "=" * 40)
    print("📋 SUMMARY")
    print("=" * 40)
    
    if success1 and success2:
        print("✅ Report icons created successfully")
        print("✅ Both PNG and SVG versions available")
        print("✅ Icons should now be visible in DeformViz 3D")
        
        print("\n💡 USAGE:")
        print("   • Restart DeformViz 3D to see the new icons")
        print("   • Look for the 📊 Advanced Reports button in toolbar")
        print("   • If still not visible, check console for icon loading errors")
        
        return 0
    else:
        print("❌ Some icons failed to create")
        print("⚠️ Try installing Pillow: pip install Pillow")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
