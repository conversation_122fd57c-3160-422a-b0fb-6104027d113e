from PySide6.QtWidgets import QDialog, QVBoxLayout, QDialogButtonBox, QComboBox, QLabel, QColorDialog, QPushButton, QHBoxLayout, QSpinBox, QDoubleSpinBox, QCheckBox
from PySide6.QtGui import QColor

class MeshDisplayModeDialog(QDialog):
    def __init__(self, current_mode="surface", current_colors=None, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Mesh Visualization Settings")
        layout = QVBoxLayout(self)
        layout.addWidget(QLabel("Visualization mode:"))
        self.combo = QComboBox()
        self.combo.addItems([
            "Surface",
            "Surface + Edge",
            "Wireframe",
            "Points"
        ])
        self.combo.setCurrentText(self.mode_to_text(current_mode))
        layout.addWidget(self.combo)
        # --- Color pickers and options ---
        self.wire_color_btn = QPushButton("Wireframe Color")
        self.wire_color = QColor("black")
        self.wire_color_btn.clicked.connect(self.pick_wire_color)
        self.edge_color_btn = QPushButton("Edge Color")
        self.edge_color = QColor("black")
        self.edge_color_btn.clicked.connect(self.pick_edge_color)
        self.point_color_btn = QPushButton("Point Color")
        self.point_color = QColor("red")
        self.point_color_btn.clicked.connect(self.pick_point_color)
        # Edge width
        self.edge_width_spin = QSpinBox()
        self.edge_width_spin.setRange(1, 10)
        self.edge_width_spin.setValue(1)
        # Point size
        self.point_size_spin = QSpinBox()
        self.point_size_spin.setRange(1, 20)
        self.point_size_spin.setValue(6)
        # Shading
        self.shading_cb = QCheckBox("Smooth shading (surface modes)")
        self.shading_cb.setChecked(True)
        # Layout for options
        color_layout = QVBoxLayout()
        color_layout.addWidget(self.wire_color_btn)
        color_layout.addWidget(self.edge_color_btn)
        color_layout.addWidget(self.point_color_btn)
        color_layout.addWidget(QLabel("Edge width:"))
        color_layout.addWidget(self.edge_width_spin)
        color_layout.addWidget(QLabel("Point size:"))
        color_layout.addWidget(self.point_size_spin)
        color_layout.addWidget(self.shading_cb)
        layout.addLayout(color_layout)
        # Dialog buttons
        btns = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        btns.accepted.connect(self.accept)
        btns.rejected.connect(self.reject)
        layout.addWidget(btns)
        self.combo.currentTextChanged.connect(self.update_option_visibility)
        self.update_option_visibility(self.combo.currentText())

    def update_option_visibility(self, mode_text):
        # Show/hide options based on mode
        mode = self.text_to_mode(mode_text)
        self.wire_color_btn.setVisible(mode == "wireframe")
        self.edge_color_btn.setVisible(mode == "surface+edge")
        self.edge_width_spin.setVisible(mode in ("wireframe", "surface+edge"))
        self.point_color_btn.setVisible(mode == "points")
        self.point_size_spin.setVisible(mode == "points")
        self.shading_cb.setVisible(mode in ("surface", "surface+edge"))

    def pick_wire_color(self):
        color = QColorDialog.getColor(self.wire_color, self, "Select Wireframe Color")
        if color.isValid():
            self.wire_color = color
            self.wire_color_btn.setStyleSheet(f"background: {color.name()}")

    def pick_edge_color(self):
        color = QColorDialog.getColor(self.edge_color, self, "Select Edge Color")
        if color.isValid():
            self.edge_color = color
            self.edge_color_btn.setStyleSheet(f"background: {color.name()}")

    def pick_point_color(self):
        color = QColorDialog.getColor(self.point_color, self, "Select Point Color")
        if color.isValid():
            self.point_color = color
            self.point_color_btn.setStyleSheet(f"background: {color.name()}")

    def selected_mode(self):
        text = self.combo.currentText()
        return self.text_to_mode(text)

    def get_wire_color(self):
        return self.wire_color.name()

    def get_edge_color(self):
        return self.edge_color.name()

    def get_point_color(self):
        return self.point_color.name()

    def get_edge_width(self):
        return self.edge_width_spin.value()

    def get_point_size(self):
        return self.point_size_spin.value()

    def get_shading(self):
        return "smooth" if self.shading_cb.isChecked() else "flat"

    @staticmethod
    def mode_to_text(mode):
        return {
            "surface": "Surface",
            "surface+edge": "Surface + Edge",
            "wireframe": "Wireframe",
            "points": "Points"
        }.get(mode, "Surface")

    @staticmethod
    def text_to_mode(text):
        return {
            "Surface": "surface",
            "Surface + Edge": "surface+edge",
            "Wireframe": "wireframe",
            "Points": "points"
        }[text]
