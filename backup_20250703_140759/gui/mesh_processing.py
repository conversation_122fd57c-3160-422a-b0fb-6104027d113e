from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QLabel, QDoubleSpinBox, QSpinBox, QDialogButtonBox, QPushButton, QProgressBar, QComboBox
from PySide6.QtCore import Qt

class MeshProcessingDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Mesh Processing Options")
        layout = QVBoxLayout(self)
        # Clean mesh
        self.clean_cb = QCheckBox("Clean mesh (remove duplicate points, degenerate faces)")
        self.clean_cb.setChecked(True)
        self.clean_cb.setToolTip("Removes duplicate points and degenerate faces for a clean mesh.")
        layout.addWidget(self.clean_cb)
        # Remove small components
        self.components_cb = QCheckBox("Remove small components")
        self.components_cb.setToolTip("Removes small disconnected mesh parts (noise/artifacts).")
        layout.addWidget(self.components_cb)
        # Fill small holes
        holes_layout = QHBoxLayout()
        self.holes_cb = QCheckBox("Fill small holes")
        self.holes_cb.setToolTip("Fills holes smaller than the specified area.")
        self.hole_size_spin = QDoubleSpinBox()
        self.hole_size_spin.setRange(0.01, 10000.0)
        self.hole_size_spin.setValue(100.0)
        self.hole_size_spin.setSuffix(" area")
        self.hole_size_spin.setToolTip("Maximum area of holes to fill.")
        holes_layout.addWidget(self.holes_cb)
        holes_layout.addWidget(QLabel("Max Area:"))
        holes_layout.addWidget(self.hole_size_spin)
        layout.addLayout(holes_layout)
        # Smooth mesh
        smooth_layout = QHBoxLayout()
        self.smooth_cb = QCheckBox("Smooth mesh (Laplacian)")
        self.smooth_cb.setToolTip("Applies smoothing to reduce noise.")
        self.smooth_method_combo = QComboBox()
        self.smooth_method_combo.addItems(["Laplacian", "Taubin", "HC"])
        self.smooth_method_combo.setToolTip("Choose smoothing method: Laplacian, Taubin, or HC.")
        self.smooth_iter_spin = QSpinBox()
        self.smooth_iter_spin.setRange(1, 100)
        self.smooth_iter_spin.setValue(20)
        self.smooth_iter_spin.setToolTip("Number of smoothing iterations.")
        self.smooth_relax_spin = QDoubleSpinBox()
        self.smooth_relax_spin.setRange(0.001, 1.0)
        self.smooth_relax_spin.setValue(0.01)
        self.smooth_relax_spin.setSingleStep(0.01)
        self.smooth_relax_spin.setToolTip("Relaxation factor (strength of smoothing).")
        smooth_layout.addWidget(self.smooth_cb)
        smooth_layout.addWidget(QLabel("Method:"))
        smooth_layout.addWidget(self.smooth_method_combo)
        smooth_layout.addWidget(QLabel("Iter:"))
        smooth_layout.addWidget(self.smooth_iter_spin)
        smooth_layout.addWidget(QLabel("Relax:"))
        smooth_layout.addWidget(self.smooth_relax_spin)
        layout.addLayout(smooth_layout)
        # Decimate
        decimate_layout = QHBoxLayout()
        self.decimate_cb = QCheckBox("Decimate mesh (reduce # faces)")
        self.decimate_cb.setToolTip("Reduces the number of faces for faster analysis/visualization.")
        self.decimate_algo_combo = QComboBox()
        self.decimate_algo_combo.addItems(["Quadric", "Edge Collapse"])
        self.decimate_algo_combo.setToolTip("Choose decimation algorithm: Quadric or Edge Collapse.")
        self.decimate_slider = QSpinBox()
        self.decimate_slider.setRange(1, 99)
        self.decimate_slider.setValue(50)
        self.decimate_slider.setToolTip("Target percentage of faces to keep (1-99%).")
        decimate_layout.addWidget(self.decimate_cb)
        decimate_layout.addWidget(QLabel("Algorithm:"))
        decimate_layout.addWidget(self.decimate_algo_combo)
        decimate_layout.addWidget(QLabel("Target: % faces"))
        decimate_layout.addWidget(self.decimate_slider)
        layout.addLayout(decimate_layout)

        # Plane/Slice options
        plane_layout = QHBoxLayout()
        self.plane_align_label = QLabel("Plane alignment:")
        self.plane_align_combo = QComboBox()
        self.plane_align_combo.addItems([
            "Keep as is (user-defined)",
            "Align to XY (Z normal)",
            "Align to YZ (X normal)",
            "Align to ZX (Y normal)"
        ])
        self.plane_align_combo.setToolTip("After picking 3 points, align the plane to a canonical axis (XY, YZ, ZX) or keep as user-defined.")
        plane_layout.addWidget(self.plane_align_label)
        plane_layout.addWidget(self.plane_align_combo)
        layout.addLayout(plane_layout)

        slab_layout = QHBoxLayout()
        self.slab_pos_label = QLabel("Slab position:")
        self.slab_pos_combo = QComboBox()
        self.slab_pos_combo.addItems([
            "Centered (± side)",
            "Positive side only",
            "Negative side only"
        ])
        self.slab_pos_combo.setToolTip("Choose which side of the plane the slab is kept: centered, positive, or negative.")
        slab_layout.addWidget(self.slab_pos_label)
        slab_layout.addWidget(self.slab_pos_combo)
        layout.addLayout(slab_layout)

        # Disable parameter controls unless checkbox is checked
        self.hole_size_spin.setEnabled(False)
        self.smooth_method_combo.setEnabled(False)
        self.smooth_iter_spin.setEnabled(False)
        self.smooth_relax_spin.setEnabled(False)
        self.decimate_algo_combo.setEnabled(False)
        self.decimate_slider.setEnabled(False)
        self.holes_cb.toggled.connect(self.hole_size_spin.setEnabled)
        self.smooth_cb.toggled.connect(self.smooth_method_combo.setEnabled)
        self.smooth_cb.toggled.connect(self.smooth_iter_spin.setEnabled)
        self.smooth_cb.toggled.connect(self.smooth_relax_spin.setEnabled)
        self.decimate_cb.toggled.connect(self.decimate_algo_combo.setEnabled)
        self.decimate_cb.toggled.connect(self.decimate_slider.setEnabled)

        # Preview and Undo buttons
        btn_layout = QHBoxLayout()
        self.preview_btn = QPushButton("Preview")
        self.preview_btn.setToolTip("Preview the effect of selected operations (not yet implemented)")
        self.undo_btn = QPushButton("Undo")
        self.undo_btn.setToolTip("Undo the last processing step (not yet implemented)")
        self.undo_btn.setEnabled(False)  # Enable when undo is available
        btn_layout.addWidget(self.preview_btn)
        btn_layout.addWidget(self.undo_btn)
        # --- Add Map button ---
        from gui.map_viewer import MapViewerDialog
        self.map_btn = QPushButton("Show Map")
        self.map_btn.setToolTip("Open a map viewer dialog window")
        self.map_btn.clicked.connect(self.open_map_dialog)
        btn_layout.addWidget(self.map_btn)
        layout.addLayout(btn_layout)

        # Progress bar placeholder
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # Dialog buttons
        btns = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        btns.accepted.connect(self.accept)
        btns.rejected.connect(self.reject)
        layout.addWidget(btns)

    def open_map_dialog(self):
        from gui.map_viewer import MapViewerDialog
        dlg = MapViewerDialog(parent=self)
        dlg.exec()

    def get_selected_filters(self):
        return {
            'clean': self.clean_cb.isChecked(),
            'components': self.components_cb.isChecked(),
            'holes': self.holes_cb.isChecked(),
            'hole_size': self.hole_size_spin.value(),
            'smooth': self.smooth_cb.isChecked(),
            'smooth_method': self.smooth_method_combo.currentText(),
            'smooth_iter': self.smooth_iter_spin.value(),
            'smooth_relax': self.smooth_relax_spin.value(),
            'decimate': self.decimate_cb.isChecked(),
            'decimate_algorithm': self.decimate_algo_combo.currentText(),
            'decimate_target': self.decimate_slider.value(),
            # Plane/slice options
            'plane_alignment': self.plane_align_combo.currentIndex(),  # 0: as is, 1: XY, 2: YZ, 3: ZX
            'slab_position': self.slab_pos_combo.currentIndex(),       # 0: centered, 1: positive, 2: negative
        }
