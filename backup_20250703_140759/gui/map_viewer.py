from PySide6.QtWidgets import QDialog, QVBoxLayout, QPushButton, QLabel
import folium
import os
import webbrowser

class MapViewerDialog(QDialog):
    def __init__(self, lat=41.8902, lon=12.4922, zoom=15, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Map Viewer (External Browser)")
        layout = QVBoxLayout(self)
        label = QLabel("The map will open in your default web browser.")
        layout.addWidget(label)
        self.open_btn = QPushButton("Open Map")
        self.open_btn.clicked.connect(self.open_map)
        layout.addWidget(self.open_btn)
        # Generate folium map
        self._html_path = os.path.abspath("map_temp.html")
        m = folium.Map(location=[lat, lon], zoom_start=zoom)
        folium.Marker([lat, lon], tooltip="Default Location").add_to(m)
        m.save(self._html_path)
    def open_map(self):
        webbrowser.open(f"file://{self._html_path}")
    def closeEvent(self, event):
        try:
            os.remove(self._html_path)
        except Exception:
            pass
        super().closeEvent(event)

if __name__ == "__main__":
    import sys
    from PySide6.QtWidgets import QApplication
    app = QApplication(sys.argv)
    dlg = MapViewerDialog()
    dlg.show()
    sys.exit(app.exec())
