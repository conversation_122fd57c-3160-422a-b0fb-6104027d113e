
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
            font-size: 11pt;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            border-bottom: 3px solid #1f77b4;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #1f77b4;
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .metadata {
            background: #aec7e8;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .metadata table {
            width: 100%;
            border-collapse: collapse;
        }

        .metadata td {
            padding: 8px;
            border-bottom: 1px solid rgba(255,255,255,0.3);
        }

        .metadata td:first-child {
            font-weight: bold;
            width: 150px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            color: #1f77b4;
            border-left: 4px solid #ff7f0e;
            padding-left: 15px;
            margin-bottom: 20px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .data-table th {
            background: #1f77b4;
            color: white;
            padding: 12px;
            text-align: left;
        }

        .data-table td {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }

        .data-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .image-container {
            text-align: center;
            margin: 20px 0;
        }

        .image-container img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .image-caption {
            font-style: italic;
            color: #666;
            margin-top: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border: 1px solid #aec7e8;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #1f77b4;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 0.9em;
        }

        @media print {
            body { background: white; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Test Report</h1>
        </div>

        <div class="metadata">
            <table>
                <tr><td>Author:</td><td>Test User</td></tr>
                <tr><td>Company:</td><td>Test Company</td></tr>
                <tr><td>Generated:</td><td>2025-07-06 18:59:19</td></tr>
                <tr><td>Software:</td><td>DeformViz 3D v2.2</td></tr>
            </table>
        </div>

        <div class="section">
            <h2>📐 Mesh Information</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">1,000</div>
                    <div class="stat-label">Points</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">1,800</div>
                    <div class="stat-label">Cells</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">12.56</div>
                    <div class="stat-label">Surface Area</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">4.18</div>
                    <div class="stat-label">Volume</div>
                </div>
            </div>

            <table class="data-table">
                <tr><th>Property</th><th>Value</th></tr>
                <tr><td>Bounding Box (X)</td><td>-1.000 to 1.000</td></tr>
                <tr><td>Bounding Box (Y)</td><td>-1.000 to 1.000</td></tr>
                <tr><td>Bounding Box (Z)</td><td>-0.500 to 0.500</td></tr>
            </table>
        </div>

        <div class="section">
            <h2>📊 Deformation Analysis</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">-0.001029</div>
                    <div class="stat-label">Mean Deformation</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">0.104905</div>
                    <div class="stat-label">Standard Deviation</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">-0.306697</div>
                    <div class="stat-label">Minimum</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">0.329167</div>
                    <div class="stat-label">Maximum</div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>Generated by DeformViz 3D v2.2 - Advanced 3D Deformation Analysis Platform</p>
            <p>Report generated on 2025-07-06 18:59:19</p>
        </div>
    </div>
</body>
</html>
