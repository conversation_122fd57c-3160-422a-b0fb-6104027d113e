#!/usr/bin/env python3
"""
Test the advanced reporting system
"""

import sys
import os
from pathlib import Path

def test_reporting_system():
    """Test the reporting system components"""
    print("🧪 Testing Advanced Reporting System")
    print("=" * 50)
    
    # Test imports
    try:
        from src.gui.advanced_reporting import AdvancedReportingDialog, ReportGenerator
        print("✅ Advanced reporting imports OK")
    except ImportError as e:
        print(f"❌ Advanced reporting import failed: {e}")
        return False
    
    try:
        from src.core.report_generator import ReportGenerator as CoreGenerator
        print("✅ Core report generator import OK")
    except ImportError as e:
        print(f"⚠️ Core report generator import failed: {e}")
        print("   Will use fallback implementation")
    
    # Test report generation with mock data
    try:
        # Create mock mesh viewer
        class MockMeshViewer:
            def __init__(self):
                self.mesh = None
        
        mesh_viewer = MockMeshViewer()
        generator = ReportGenerator(mesh_viewer)
        
        # Test configuration
        config = {
            'title': 'Test Report',
            'author': 'Test User',
            'company': 'Test Company',
            'format': 'HTML',
            'output_path': 'test_report.html',
            'include_mesh_info': True,
            'include_deformation': True,
            'include_measurements': True,
            'include_quality_control': True,
            'include_statistics': True,
            'include_screenshots': False,  # Skip screenshots for testing
            'include_heatmaps': False,
            'include_charts': True,
            'include_histograms': True,
            'include_raw_data': False,
            'include_summary_tables': True,
            'include_tolerance_tables': True,
            'page_size': 'A4',
            'page_orientation': 'Portrait',
            'color_scheme': 'Professional Blue',
            'font_size': 11,
            'include_logo': False,
            'chart_style': 'Modern',
            'include_3d_charts': False
        }
        
        # Test HTML report generation
        print("🔄 Testing HTML report generation...")
        success = generator.generate_html_report(config)
        
        if success:
            print("✅ HTML report generation successful")
            if os.path.exists('test_report.html'):
                print(f"✅ Report file created: test_report.html")
                # Clean up
                os.remove('test_report.html')
            else:
                print("⚠️ Report file not found")
        else:
            print("❌ HTML report generation failed")
        
        return success
        
    except Exception as e:
        print(f"❌ Report generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dependencies():
    """Test optional dependencies for enhanced reporting"""
    print("\n🔍 Testing Optional Dependencies")
    print("=" * 40)
    
    dependencies = {
        'matplotlib': 'Chart generation',
        'seaborn': 'Enhanced chart styling',
        'reportlab': 'PDF generation',
        'openpyxl': 'Excel generation',
        'python-docx': 'Word document generation',
        'python-pptx': 'PowerPoint generation',
        'weasyprint': 'HTML to PDF conversion'
    }
    
    available = []
    missing = []
    
    for dep, description in dependencies.items():
        try:
            if dep == 'python-docx':
                import docx
            elif dep == 'python-pptx':
                import pptx
            else:
                __import__(dep.replace('-', '_'))
            
            print(f"✅ {dep}: {description}")
            available.append(dep)
        except ImportError:
            print(f"❌ {dep}: {description} (not available)")
            missing.append(dep)
    
    print(f"\n📊 Summary: {len(available)}/{len(dependencies)} dependencies available")
    
    if missing:
        print("\n💡 To install missing dependencies:")
        print("pip install " + " ".join(missing))
    
    return len(available) > 0

def create_sample_report():
    """Create a sample report to demonstrate functionality"""
    print("\n🎨 Creating Sample Report")
    print("=" * 30)
    
    try:
        from src.core.report_generator import ReportGenerator
        
        # Mock data for demonstration
        class MockMeshViewer:
            def __init__(self):
                import numpy as np
                
                # Create mock mesh with some data
                class MockMesh:
                    def __init__(self):
                        self.n_points = 1000
                        self.n_cells = 1800
                        self.bounds = [-1.0, 1.0, -1.0, 1.0, -0.5, 0.5]
                        self.area = 12.56
                        self.volume = 4.18
                        self.point_data = {
                            'Deformation': np.random.normal(0, 0.1, 1000)
                        }
                
                self.mesh = MockMesh()
                
                # Mock plotter for screenshots
                class MockPlotter:
                    def screenshot(self, return_img=True, scale=1):
                        # Return a simple image array
                        import numpy as np
                        return np.random.randint(0, 255, (400, 600, 3), dtype=np.uint8)
                
                self.plotter = MockPlotter()
        
        mesh_viewer = MockMeshViewer()
        generator = ReportGenerator(mesh_viewer)
        
        # Configuration for sample report
        config = {
            'title': 'DeformViz 3D Sample Analysis Report',
            'author': 'DeformViz 3D User',
            'company': 'Sample Organization',
            'format': 'HTML',
            'output_path': 'sample_report.html',
            'image_quality': 'High (300 DPI)',
            'include_metadata': True,
            'include_mesh_info': True,
            'include_deformation': True,
            'include_measurements': True,
            'include_quality_control': True,
            'include_statistics': True,
            'include_screenshots': True,
            'include_heatmaps': True,
            'include_charts': True,
            'include_histograms': True,
            'include_raw_data': False,
            'include_summary_tables': True,
            'include_tolerance_tables': True,
            'page_size': 'A4',
            'page_orientation': 'Portrait',
            'color_scheme': 'Professional Blue',
            'font_size': 11,
            'include_logo': False,
            'chart_style': 'Modern',
            'include_3d_charts': False
        }
        
        print("🔄 Generating sample HTML report...")
        success = generator.generate_html_report(config)
        
        if success and os.path.exists('sample_report.html'):
            print("✅ Sample report created: sample_report.html")
            print("🌐 Open sample_report.html in your browser to view the report")
            return True
        else:
            print("❌ Sample report generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Sample report creation failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 DeformViz 3D Advanced Reporting Test Suite")
    print("=" * 60)
    
    # Test basic functionality
    basic_test = test_reporting_system()
    
    # Test dependencies
    deps_available = test_dependencies()
    
    # Create sample report if basic tests pass
    if basic_test:
        sample_created = create_sample_report()
    else:
        sample_created = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Basic Functionality: {'PASS' if basic_test else 'FAIL'}")
    print(f"✅ Dependencies: {'Available' if deps_available else 'Missing'}")
    print(f"✅ Sample Report: {'Created' if sample_created else 'Failed'}")
    
    if basic_test and deps_available:
        print("\n🎉 Advanced Reporting System is ready!")
        print("   • All core functionality working")
        print("   • Dependencies available for enhanced features")
        print("   • Ready for professional report generation")
    elif basic_test:
        print("\n⚠️ Advanced Reporting System partially ready")
        print("   • Core functionality working")
        print("   • Some dependencies missing for full features")
        print("   • Install missing dependencies for best experience")
    else:
        print("\n❌ Advanced Reporting System needs attention")
        print("   • Core functionality issues detected")
        print("   • Check imports and dependencies")
    
    return 0 if basic_test else 1

if __name__ == "__main__":
    sys.exit(main())
