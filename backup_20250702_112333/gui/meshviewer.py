from PySide6.QtWidgets import QWidget, QVBoxLayout
from pyvistaqt import QtInteractor
import pyvista as pv
import numpy as np
from scipy.spatial import cKDTree

class MeshViewerWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        layout = QVBoxLayout(self)
        self.plotter = QtInteractor(self)
        self.plotter.set_background('#888888')  # Medium gray background
        # Add a default light for better shading
        import pyvista as pv
        light = pv.Light()
        light.set_direction_angle(45, -45)  # Overhead, angled
        light.intensity = 1.0
        light.positional = False
        self.plotter.renderer.AddLight(light)
        layout.addWidget(self.plotter.interactor)
        self.mesh = None
        self.display_mode = "surface"
        # --- EDL shading flag ---
        self._edl_enabled = hasattr(self.plotter, 'enable_eye_dome_lighting')

        # --- Helper state flags ---
        self._bounding_box_shown = False
        self._bounding_box_actor = None
        self._bbox_label_actor = None
        self._analysis_plane_actor = None
        self._analysis_plane_visible = True

        # --- Add orientation widget ONCE ---
        self.plotter.add_orientation_widget(pv.AxesActor())

        # --- Initialize display helpers ---
        self.restore_helpers_from_state()

    def restore_helpers_from_state(self):
        """Restore all helpers according to internal state flags."""
        self.show_bounding_box(self._bounding_box_shown)

    def transfer_vertex_colors(self, src_mesh, dst_mesh, color_key):
        """Transfer per-vertex colors from src_mesh to dst_mesh using nearest neighbor mapping."""
        src_points = np.asarray(src_mesh.points)
        dst_points = np.asarray(dst_mesh.points)
        src_colors = np.asarray(src_mesh.point_data[color_key])
        tree = cKDTree(src_points)
        dists, idxs = tree.query(dst_points)
        return src_colors[idxs]

    def set_edl_enabled(self, enabled=True):
        """Enable or disable Eye Dome Lighting (EDL) if supported."""
        if hasattr(self.plotter, 'enable_eye_dome_lighting'):
            if enabled:
                self.plotter.enable_eye_dome_lighting()
            else:
                self.plotter.disable_eye_dome_lighting()
            self._edl_enabled = enabled
        else:
            print("[INFO] Eye Dome Lighting (EDL) not supported in this PyVista version.")

    def set_display_mode(self, mode, reset_camera=True, wire_color="black", point_color="red", point_size=6, edge_color="black", edge_width=1, shading="smooth"):
        """Switch mesh visualization mode: surface, surface+edge, wireframe, points. Adds customization and feedback."""
        if self.mesh is None:
            return
        self.plotter.clear()
        mesh = self.mesh
        # --- Always compute normals if missing or invalid (improves shading for STL, OBJ, etc.) ---
        normals = mesh.point_data.get('Normals', None)
        if normals is None or (hasattr(mesh, 'n_points') and (not hasattr(normals, 'shape') or normals.shape[0] != mesh.n_points)):
            mesh.compute_normals(inplace=True, auto_orient_normals=True)
        # Try to recover color arrays from original mesh if missing after processing
        color_keys = [k for k in mesh.point_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "vertex_colors")]
        cell_color_keys = [k for k in mesh.cell_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "face_colors")]
        # Advanced: If no color arrays, but original mesh exists and has them, transfer using nearest neighbor
        if not color_keys and hasattr(self, 'original_mesh'):
            orig = self.original_mesh
            orig_color_keys = [k for k in orig.point_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "vertex_colors")]
            if orig_color_keys:
                try:
                    mesh.point_data[orig_color_keys[0]] = self.transfer_vertex_colors(orig, mesh, orig_color_keys[0])
                    color_keys = [orig_color_keys[0]]
                except Exception:
                    pass
        if not cell_color_keys and hasattr(self, 'original_mesh'):
            orig = self.original_mesh
            orig_cell_color_keys = [k for k in orig.cell_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "face_colors")]
            if orig_cell_color_keys and len(orig.cell_data[orig_cell_color_keys[0]]) == mesh.n_cells:
                mesh.cell_data[orig_cell_color_keys[0]] = orig.cell_data[orig_cell_color_keys[0]]
                cell_color_keys = [orig_cell_color_keys[0]]
        kwargs = {}
        if color_keys:
            kwargs = {"scalars": color_keys[0], "rgb": True}
        elif cell_color_keys:
            kwargs = {"scalars": cell_color_keys[0], "rgb": True}
        elif hasattr(mesh, 'active_scalars_info') and mesh.active_scalars_info and mesh.active_scalars_info[1] == 'texture':
            kwargs = {"texture": mesh.active_scalars}
        elif hasattr(mesh, 'textures') and getattr(mesh, 'textures', None):
            tex = next(iter(mesh.textures.values())) if mesh.textures else None
            if tex is not None:
                kwargs = {"texture": tex}
        # --- Visualization modes ---
        try:
            if mode == "surface":
                self.plotter.add_mesh(
                    mesh,
                    show_edges=False,
                    smooth_shading=True,  # Always enable smooth shading for best appearance
                    ambient=0.35,
                    specular=0.45,
                    specular_power=18,
                    **kwargs
                )
            elif mode == "surface+edge":
                self.plotter.add_mesh(
                    mesh,
                    show_edges=True,
                    edge_color=edge_color,
                    line_width=edge_width,
                    smooth_shading=True,  # Always enable smooth shading for best appearance
                    ambient=0.35,
                    specular=0.45,
                    specular_power=18,
                    **kwargs
                )
            elif mode == "wireframe":
                self.plotter.add_mesh(
                    mesh,
                    style="wireframe",
                    color=wire_color,
                    line_width=edge_width
                )
            elif mode == "points":
                self.plotter.add_mesh(
                    mesh,
                    style="points",
                    render_points_as_spheres=True,
                    point_size=point_size,
                    color=point_color
                )
            else:
                self.plotter.add_text(f"Unknown mode: {mode}", position="upper_left", color="red")
        except Exception as e:
            self.plotter.add_text(f"Error: {e}", position="upper_left", color="red")
        # --- Enable EDL if supported and requested ---
        if getattr(self, '_edl_enabled', False) and hasattr(self.plotter, 'enable_eye_dome_lighting'):
            self.plotter.enable_eye_dome_lighting()
        if reset_camera:
            self.plotter.reset_camera()
        self.plotter.render()
        self.display_mode = mode
        # --- Always restore helpers after scene change ---
        self.restore_helpers_from_state()
        # --- Show status message for current mode ---
        try:
            from PySide6.QtWidgets import QApplication
            msg = f"Display mode: {mode}"
            if mode == "wireframe":
                msg += f" (color: {wire_color})"
            elif mode == "points":
                msg += f" (color: {point_color}, size: {point_size})"
            elif mode == "surface+edge":
                msg += f" (edge color: {edge_color}, width: {edge_width}, shading: smooth)"
            elif mode == "surface":
                msg += f" (shading: smooth)"
            QApplication.instance().statusBar().showMessage(msg)
        except Exception:
            pass

    def load_mesh(self, filename):
        try:
            import pyvista as pv
            mesh = pv.read(filename)
            # --- Convert to PolyData if needed ---
            converted = False
            if not isinstance(mesh, pv.PolyData):
                mesh = mesh.extract_surface().triangulate()
                converted = True
            # --- Try to auto-detect and set RGB colors if present ---
            color_keys = [k for k in mesh.point_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "vertex_colors")]
            if color_keys:
                arr = mesh.point_data[color_keys[0]]
                import numpy as np
                arr = np.asarray(arr)
                # If array is float, convert to uint8 (assuming 0-1 range)
                if arr.dtype in (np.float32, np.float64):
                    arr = (arr * 255).clip(0, 255).astype(np.uint8)
                if arr.dtype != np.uint8:
                    arr = arr.astype(np.uint8)
                if arr.ndim == 2 and arr.shape[1] >= 3:
                    mesh.point_data.clear()
                    mesh.point_data["RGB"] = arr[:, :3]
            self.mesh = mesh
            self.original_mesh = mesh.copy(deep=True)  # Save original for color recovery
            self.set_display_mode(getattr(self, 'display_mode', 'surface'))
            # --- Inform user if conversion occurred ---
            if converted:
                try:
                    from PySide6.QtWidgets import QMessageBox, QApplication
                    parent = QApplication.activeWindow() or self
                    QMessageBox.information(parent, "Mesh Converted", "The loaded mesh was converted to PolyData for compatibility with processing and export.")
                except Exception:
                    print("[INFO] Mesh was converted to PolyData.")
        except Exception as e:
            raise RuntimeError(f"Failed to load mesh: {e}")

    def set_colormap(self, array_name, nan_color=None, nan_opacity=0.15):
        """Display the mesh colored by the given scalar array (point_data)."""
        if self.mesh is None:
            return
        self.plotter.clear()
        try:
            self.plotter.remove_scalar_bar()
        except Exception:
            pass
        mesh = self.mesh
        normals = mesh.point_data.get('Normals', None)
        if normals is None or (hasattr(mesh, 'n_points') and (not hasattr(normals, 'shape') or normals.shape[0] != mesh.n_points)):
            mesh.compute_normals(inplace=True)
        if array_name in mesh.point_data:
            if nan_color is None:
                nan_color = "#b0b0b0"
            scalars = mesh.point_data[array_name]
            # --- Percentile-based clamping for colormap scaling with debug output ---
            valid = scalars[~np.isnan(scalars)] if isinstance(scalars, np.ndarray) else np.array([])
            valid = valid[np.isfinite(valid)]  # Remove inf/-inf
            if valid.size > 0:
                print(f"[DEBUG] {array_name} stats: min={valid.min():.4g}, max={valid.max():.4g}, mean={valid.mean():.4g}, std={valid.std():.4g}")
            if valid.size > 10:
                lower, upper = np.percentile(valid, 2), np.percentile(valid, 98)
                print(f"[DEBUG] {array_name} percentiles: 2nd={lower:.4g}, 98th={upper:.4g}")
                if lower == upper:
                    lower, upper = valid.min(), valid.max()
            elif valid.size > 0:
                lower, upper = valid.min(), valid.max()
            else:
                lower, upper = 0.0, 1.0
            print(f"[DEBUG] {array_name} colormap clamp: [{lower:.4g}, {upper:.4g}]")
            clim = [lower, upper]
            # --- Robust per-point opacity fade logic ---
            use_per_point_opacity = False
            opacity = 1.0
            if (
                isinstance(mesh, pv.PolyData)
                and hasattr(mesh, 'n_points')
                and isinstance(scalars, np.ndarray)
                and scalars.ndim == 1
                and scalars.shape[0] == mesh.n_points
            ):
                # Per-point opacity supported
                opacity = np.ones(mesh.n_points, dtype=float)
                safe_opacity = nan_opacity if nan_opacity > 0 else 0.05
                mask = np.isnan(scalars)
                opacity[mask] = safe_opacity
                use_per_point_opacity = True
            # Compose kwargs
            kwargs = dict(
                scalars=array_name,
                cmap="viridis",
                show_edges=False,
                smooth_shading=True,
                nan_color=nan_color,
                ambient=0.35,
                specular=0.45,
                specular_power=18,
                show_scalar_bar=False,
                clim=clim
            )
            if use_per_point_opacity:
                kwargs["opacity"] = opacity
            else:
                kwargs["opacity"] = 1.0  # fallback: fully opaque
            self.plotter.add_mesh(mesh, **kwargs)
        else:
            # --- Patch: Always try to transfer per-vertex color from original mesh if missing ---
            # This ensures that after cropping or any operation, colors are restored if possible
            color_keys = [k for k in mesh.point_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "vertex_colors")]
            if not color_keys and hasattr(self, 'original_mesh'):
                orig = self.original_mesh
                orig_color_keys = [k for k in orig.point_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "vertex_colors")]
                if orig_color_keys:
                    try:
                        mesh.point_data[orig_color_keys[0]] = self.transfer_vertex_colors(orig, mesh, orig_color_keys[0])
                        color_keys = [orig_color_keys[0]]
                    except Exception as e:
                        pass
            self.set_display_mode('surface', reset_camera=False)
            return
        self.plotter.render()

    def set_mesh(self, mesh):
        import pyvista as pv
        # Always convert UnstructuredGrid to PolyData for downstream compatibility
        if isinstance(mesh, pv.UnstructuredGrid):
            mesh = mesh.extract_surface().triangulate()
        elif isinstance(mesh, pv.MultiBlock):
            # If a MultiBlock, try to extract the first PolyData block
            for block in mesh:
                if isinstance(block, pv.PolyData):
                    mesh = block
                    break
                elif isinstance(block, pv.UnstructuredGrid):
                    mesh = block.extract_surface().triangulate()
                    break
        self.mesh = mesh
        self.set_display_mode(getattr(self, 'display_mode', 'surface'))
        # --- Patch: Always ensure RGB is present and correct before saving or after color transfer ---
        # If a color array is present but not named 'RGB', or is not uint8, normalize and rename
        color_keys = [k for k in mesh.point_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "vertex_colors")]
        if color_keys:
            arr = np.asarray(mesh.point_data[color_keys[0]])
            if arr.dtype in (np.float32, np.float64):
                arr = (arr * 255).clip(0, 255).astype(np.uint8)
            if arr.dtype != np.uint8:
                arr = arr.astype(np.uint8)
            if arr.ndim == 2 and arr.shape[1] >= 3:
                mesh.point_data.clear()
                mesh.point_data["RGB"] = arr[:, :3]
        # --- Patch: Force RGB as active scalars for saving ---
        if "RGB" in mesh.point_data:
            mesh.active_scalars_name = "RGB"
            mesh.set_active_scalars("RGB")
        # --- Patch: For PLY, force PyVista to write RGB as vertex colors ---
        # Remove _active_scalars_info patch, as it causes errors in recent PyVista versions
        # Only set active_scalars_name and set_active_scalars
        if "RGB" in mesh.point_data:
            try:
                mesh.active_scalars_name = "RGB"
                mesh.set_active_scalars("RGB")
            except Exception as e:
                pass

    def show_bounding_box(self, show=True):
        """Show or hide the mesh bounding box with dimensions."""
        if not hasattr(self, '_bounding_box_actor'):
            self._bounding_box_actor = None
        if show and self.mesh is not None:
            # Remove previous bounding box if any
            if self._bounding_box_actor:
                self.plotter.remove_actor(self._bounding_box_actor)
            # Add bounding box as a wireframe mesh
            bbox_mesh = self.mesh.bounding_box()
            self._bounding_box_actor = self.plotter.add_mesh(bbox_mesh, style="wireframe", color="yellow", line_width=2)
            # Add dimensions as a label
            bbox = self.mesh.bounds
            xlen = bbox[1] - bbox[0]
            ylen = bbox[3] - bbox[2]
            zlen = bbox[5] - bbox[4]
            label = f"X: {xlen:.2f}, Y: {ylen:.2f}, Z: {zlen:.2f}"
            if hasattr(self, '_bbox_label_actor') and self._bbox_label_actor:
                self.plotter.remove_actor(self._bbox_label_actor)
            self._bbox_label_actor = self.plotter.add_text(label, position='lower_left', font_size=10, color='yellow')
        else:
            if self._bounding_box_actor:
                self.plotter.remove_actor(self._bounding_box_actor)
                self._bounding_box_actor = None
            if hasattr(self, '_bbox_label_actor') and self._bbox_label_actor:
                self.plotter.remove_actor(self._bbox_label_actor)
                self._bbox_label_actor = None
        self._bounding_box_shown = show
        print(f"[DEBUG] Bounding box {'shown' if show else 'hidden'}.")

    def toggle_bounding_box(self):
        self.show_bounding_box(not self._bounding_box_shown)

    def set_analysis_plane_params(self, origin, normal, slab_thickness=None):
        """Safely set the analysis plane parameters, ensuring only valid numeric values are stored."""
        try:
            origin = np.asarray(origin, dtype=float)
            normal = np.asarray(normal, dtype=float)
            if origin.shape != (3,) or not np.isfinite(origin).all():
                raise ValueError
            if normal.shape != (3,) or not np.isfinite(normal).all():
                raise ValueError
        except Exception:
            print(f"[ERROR] Attempted to set invalid analysis plane parameters: origin={origin}, normal={normal}")
            print("Analysis plane NOT set. Please provide valid numeric values.")
            return False
        self._last_plane_params = (origin, normal)
        if slab_thickness is not None:
            self._last_slab_thickness = float(slab_thickness)
        return True

    def set_analysis_plane(self, plane_mesh, color='yellow', opacity=0.4, origin=None, normal=None, slab_thickness=None):
        """Add or update the persistent analysis plane actor, and optionally set plane parameters safely."""
        plotter = self.plotter
        # Remove previous if exists
        if self._analysis_plane_actor is not None:
            try:
                plotter.remove_actor(self._analysis_plane_actor)
            except Exception:
                pass
            self._analysis_plane_actor = None
        # Add new
        self._analysis_plane_actor = plotter.add_mesh(
            plane_mesh, color=color, opacity=opacity, name='analysis_plane', pickable=False, reset_camera=False
        )
        self._analysis_plane_visible = True
        plotter.render()
        # Optionally set plane parameters if provided
        if origin is not None and normal is not None:
            self.set_analysis_plane_params(origin, normal, slab_thickness)

    def show_analysis_plane(self, show=True):
        """Show or hide the persistent analysis plane actor."""
        plotter = self.plotter
        if self._analysis_plane_actor is not None:
            if show:
                # Re-add if not present
                if self._analysis_plane_actor not in plotter.actors.values():
                    plotter.add_actor(self._analysis_plane_actor)
                self._analysis_plane_visible = True
            else:
                try:
                    plotter.remove_actor(self._analysis_plane_actor)
                except Exception:
                    pass
                self._analysis_plane_visible = False
            plotter.render()

    def has_analysis_plane(self):
        return self._analysis_plane_actor is not None

    def remove_analysis_plane(self):
        plotter = self.plotter
        if self._analysis_plane_actor is not None:
            try:
                plotter.remove_actor(self._analysis_plane_actor)
            except Exception:
                pass
            self._analysis_plane_actor = None
            self._analysis_plane_visible = False
            plotter.render()

    def show_vector_field(self, array_name="Displacement", scale=1.0, color="red"):
        """Visualize a vector field as arrows/glyphs on the mesh, restricted to the slab if defined.
        Note: If you visualize a heatmap after this, you can call this method again to re-add the arrows (no restart needed).
        """
        if self.mesh is None or array_name not in self.mesh.point_data:
            print(f"Vector field '{array_name}' not found in mesh.point_data.")
            return
        vectors = self.mesh.point_data[array_name]
        points = self.mesh.points
        # Remove previous arrows if any
        self.plotter.remove_actor('vector_field_arrows', reset_camera=False)

        # --- Slab logic: restrict to points within the slab if defined ---
        slab_mask = None
        plane_params = getattr(self, '_last_plane_params', None)
        slab_thickness = getattr(self, '_last_slab_thickness', None)
        if plane_params is not None and slab_thickness is not None:
            origin, normal = plane_params
            # Ensure normal is a numeric array
            try:
                normal = np.asarray(normal, dtype=float)
                origin = np.asarray(origin, dtype=float)
                if normal.shape != (3,) or not np.isfinite(normal).all():
                    raise ValueError
                if origin.shape != (3,) or not np.isfinite(origin).all():
                    raise ValueError
            except Exception:
                print(f"[ERROR] Analysis plane parameters are invalid: origin={origin}, normal={normal}")
                print("Analysis plane state will be cleared. Please redefine the analysis plane before visualizing the vector field.")
                # Clear invalid state to prevent repeated errors
                self._last_plane_params = None
                self._last_slab_thickness = None
                return
            normal = normal / np.linalg.norm(normal)
            # Compute signed distance from each point to the plane
            vecs = points - origin
            dists = np.dot(vecs, normal)
            half_thick = slab_thickness / 2.0
            slab_mask = np.abs(dists) <= half_thick
            if not np.any(slab_mask):
                print("No points found within the current slab region.")
                return
            points_to_show = points[slab_mask]
            vectors_to_show = vectors[slab_mask]
        else:
            # No slab defined: show all points, inform user
            points_to_show = points
            vectors_to_show = vectors
            print("No analysis slab defined: showing vector field for the whole mesh.")
            try:
                from PySide6.QtWidgets import QApplication
                QApplication.instance().statusBar().showMessage("No analysis slab defined: showing vector field for the whole mesh.")
            except Exception:
                pass

        self.plotter.add_arrows(points_to_show, vectors_to_show, mag=scale, color=color, name='vector_field_arrows')
        self.plotter.render()
