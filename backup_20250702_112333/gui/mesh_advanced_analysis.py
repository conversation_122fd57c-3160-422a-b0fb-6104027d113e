from PySide6.QtWidgets import QDialog, QVBoxLayout, QLabel, QCheckBox, QFormLayout, QSpinBox, QDoubleSpinBox, QDialogButtonBox, QGroupBox, QHBoxLayout, QComboBox
from PySide6.QtCore import Signal

class MeshAdvancedAnalysisDialog(QDialog):
    # Signal for live update (params: dict)
    analysis_apply = Signal(dict)
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Advanced Mesh Analysis")
        self.setMinimumWidth(400)
        self.setModal(False)  # Make dialog non-modal (modeless)
        layout = QVBoxLayout(self)
        # Analysis options
        self.mean_curv_cb = QCheckBox("Mean Curvature")
        self.gauss_curv_cb = QCheckBox("Gaussian Curvature")
        self.thickness_cb = QCheckBox("Thickness (Ray Casting)")
        self.geodesic_cb = QCheckBox("Geodesic Distance (from Point)")
        self.custom_cb = QCheckBox("Custom Scalar Field (Random)")
        self.contact_cb = QCheckBox("Contact/Proximity Analysis (Min Distance)")
        self.export_results_cb = QCheckBox("Export Results to CSV/TXT")
        # Subset restriction
        self.subset_combo = QComboBox()
        self.subset_combo.addItems(["Full Mesh", "Current Slab/ROI", "Selection Only"])
        self.subset_combo.setToolTip("Restrict analysis to: full mesh, current slab/ROI, or selection.")
        # Visualization type for each analysis
        self.vis_types = {}
        for key, label in [
            ("mean_curv", "Mean Curvature"),
            ("gauss_curv", "Gaussian Curvature"),
            ("thickness", "Thickness"),
            ("geodesic", "Geodesic Distance"),
            ("custom", "Custom Scalar Field"),
            ("contact", "Contact/Proximity")]:
            cb = QComboBox()
            if key in ("mean_curv", "gauss_curv", "thickness", "custom"):
                cb.addItems(["Colormap", "Contours"])
            elif key == "geodesic":
                cb.addItems(["Colormap", "Contours", "Streamlines"])
            elif key == "contact":
                cb.addItems(["Colormap"])
            cb.setToolTip(f"Visualization type for {label}")
            self.vis_types[key] = cb
        # Parameters for each analysis
        form = QFormLayout()
        self.thickness_ray_length = QDoubleSpinBox()
        self.thickness_ray_length.setMinimum(1.0)
        self.thickness_ray_length.setMaximum(1000.0)
        self.thickness_ray_length.setValue(100.0)
        form.addRow("Thickness Ray Length:", self.thickness_ray_length)
        self.geodesic_source = QSpinBox()
        self.geodesic_source.setMinimum(0)
        self.geodesic_source.setMaximum(1000000)
        self.geodesic_source.setValue(0)
        form.addRow("Geodesic Source Point Index:", self.geodesic_source)
        # Group boxes for clarity
        analysis_group = QGroupBox("Select Analyses to Run")
        analysis_layout = QVBoxLayout(analysis_group)
        analysis_layout.addWidget(self.mean_curv_cb)
        analysis_layout.addWidget(self.vis_types["mean_curv"])
        analysis_layout.addWidget(self.gauss_curv_cb)
        analysis_layout.addWidget(self.vis_types["gauss_curv"])
        analysis_layout.addWidget(self.thickness_cb)
        analysis_layout.addWidget(self.vis_types["thickness"])
        analysis_layout.addWidget(self.geodesic_cb)
        analysis_layout.addWidget(self.vis_types["geodesic"])
        analysis_layout.addWidget(self.custom_cb)
        analysis_layout.addWidget(self.vis_types["custom"])
        analysis_layout.addWidget(self.contact_cb)
        analysis_layout.addWidget(self.vis_types["contact"])
        layout.addWidget(analysis_group)
        param_group = QGroupBox("Parameters")
        param_layout = QVBoxLayout(param_group)
        param_layout.addLayout(form)
        layout.addWidget(param_group)
        # Subset restriction UI
        subset_layout = QHBoxLayout()
        subset_layout.addWidget(QLabel("Restrict to:"))
        subset_layout.addWidget(self.subset_combo)
        layout.addLayout(subset_layout)
        self.info_label = QLabel("Select one or more analyses and set parameters.")
        layout.addWidget(self.info_label)
        # Button box with Apply
        self.button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.apply_btn = self.button_box.addButton("Apply", QDialogButtonBox.ApplyRole)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        self.apply_btn.clicked.connect(self._emit_apply)
        layout.addWidget(self.button_box)

    def _emit_apply(self):
        self.analysis_apply.emit(self.get_selected_analyses())

    def get_selected_analyses(self):
        return {
            'mean_curvature': self.mean_curv_cb.isChecked(),
            'mean_curv_vis': self.vis_types['mean_curv'].currentText(),
            'gaussian_curvature': self.gauss_curv_cb.isChecked(),
            'gauss_curv_vis': self.vis_types['gauss_curv'].currentText(),
            'thickness': self.thickness_cb.isChecked(),
            'thickness_vis': self.vis_types['thickness'].currentText(),
            'thickness_ray_length': self.thickness_ray_length.value(),
            'geodesic': self.geodesic_cb.isChecked(),
            'geodesic_vis': self.vis_types['geodesic'].currentText(),
            'geodesic_source': self.geodesic_source.value(),
            'custom': self.custom_cb.isChecked(),
            'custom_vis': self.vis_types['custom'].currentText(),
            'contact': self.contact_cb.isChecked(),
            'contact_vis': self.vis_types['contact'].currentText(),
            'export_results': self.export_results_cb.isChecked(),
            'subset': self.subset_combo.currentText(),
        }

    def apply_visualization(self, mesh, analysis_name, values, vis_type, viewer):
        """
        Apply the selected visualization type to the mesh for the given analysis.
        mesh: the mesh object
        analysis_name: e.g. 'mean_curvature', 'thickness', etc.
        values: the computed scalar/vector field (numpy array)
        vis_type: string, e.g. 'Colormap', 'Contours', 'Streamlines', 'Glyphs'
        viewer: the mesh viewer or plotter object
        """
        if vis_type == "Colormap":
            # Standard colormap visualization
            mesh.point_data[analysis_name] = values
            viewer.set_colormap(analysis_name)
        elif vis_type == "Contours":
            # Show contour lines/surfaces for the scalar field
            mesh.point_data[analysis_name] = values
            viewer.show_contours(analysis_name)
        elif vis_type == "Streamlines":
            # For vector fields: show streamlines (only for geodesic or custom vector fields)
            mesh.point_data[analysis_name] = values
            viewer.show_streamlines(analysis_name)
        elif vis_type == "Glyphs":
            # For vector fields: show glyphs/arrows (principal directions, etc.)
            mesh.point_data[analysis_name] = values
            viewer.show_glyphs(analysis_name)
        else:
            # Fallback: just show as colormap
            mesh.point_data[analysis_name] = values
            viewer.set_colormap(analysis_name)

    # Example usage in your mainwindow or analysis logic:
    # dialog = MeshAdvancedAnalysisDialog(self)
    # ...
    # params = dialog.get_selected_analyses()
    # result = compute_analysis(mesh, params)
    # dialog.apply_visualization(mesh, 'mean_curvature', result, params['mean_curv_vis'], self.mesh_viewer)
