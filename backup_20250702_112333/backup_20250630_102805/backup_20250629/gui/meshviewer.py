from PySide6.QtWidgets import QWidget, QVBoxLayout
from pyvistaqt import QtInteractor
import pyvista as pv
import numpy as np
from scipy.spatial import cKDTree

class MeshViewerWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        layout = QVBoxLayout(self)
        self.plotter = QtInteractor(self)
        self.plotter.set_background('#888888')  # Medium gray background
        # Add a default light for better shading
        import pyvista as pv
        light = pv.Light()
        light.set_direction_angle(45, -45)  # Overhead, angled
        light.intensity = 1.0
        light.positional = False
        self.plotter.renderer.AddLight(light)
        layout.addWidget(self.plotter.interactor)
        self.mesh = None
        self.display_mode = "surface"

    def transfer_vertex_colors(self, src_mesh, dst_mesh, color_key):
        """Transfer per-vertex colors from src_mesh to dst_mesh using nearest neighbor mapping."""
        src_points = np.asarray(src_mesh.points)
        dst_points = np.asarray(dst_mesh.points)
        src_colors = np.asarray(src_mesh.point_data[color_key])
        tree = cKDTree(src_points)
        dists, idxs = tree.query(dst_points)
        return src_colors[idxs]

    def set_display_mode(self, mode, reset_camera=True):
        """Switch mesh visualization mode: surface, surface+edge, wireframe, points."""
        if self.mesh is None:
            return
        self.plotter.clear()
        mesh = self.mesh
        # Try to recover color arrays from original mesh if missing after processing
        color_keys = [k for k in mesh.point_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "vertex_colors")]
        cell_color_keys = [k for k in mesh.cell_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "face_colors")]
        # Advanced: If no color arrays, but original mesh exists and has them, transfer using nearest neighbor
        if not color_keys and hasattr(self, 'original_mesh'):
            orig = self.original_mesh
            orig_color_keys = [k for k in orig.point_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "vertex_colors")]
            if orig_color_keys:
                # Only transfer if original has colors and processed mesh has points
                try:
                    mesh.point_data[orig_color_keys[0]] = self.transfer_vertex_colors(orig, mesh, orig_color_keys[0])
                    color_keys = [orig_color_keys[0]]
                except Exception:
                    pass
        if not cell_color_keys and hasattr(self, 'original_mesh'):
            orig = self.original_mesh
            orig_cell_color_keys = [k for k in orig.cell_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "face_colors")]
            if orig_cell_color_keys and len(orig.cell_data[orig_cell_color_keys[0]]) == mesh.n_cells:
                mesh.cell_data[orig_cell_color_keys[0]] = orig.cell_data[orig_cell_color_keys[0]]
                cell_color_keys = [orig_cell_color_keys[0]]
        kwargs = {}
        if color_keys:
            kwargs = {"scalars": color_keys[0], "rgb": True}
        elif cell_color_keys:
            kwargs = {"scalars": cell_color_keys[0], "rgb": True}
        elif hasattr(mesh, 'active_scalars_info') and mesh.active_scalars_info and mesh.active_scalars_info[1] == 'texture':
            kwargs = {"texture": mesh.active_scalars}
        elif hasattr(mesh, 'textures') and getattr(mesh, 'textures', None):
            tex = next(iter(mesh.textures.values())) if mesh.textures else None
            if tex is not None:
                kwargs = {"texture": tex}
        if mode == "surface":
            self.plotter.add_mesh(mesh, show_edges=False, **kwargs)
        elif mode == "surface+edge":
            self.plotter.add_mesh(mesh, show_edges=True, **kwargs)
        elif mode == "wireframe":
            self.plotter.add_mesh(mesh, style="wireframe", color="black")
        elif mode == "points":
            self.plotter.add_mesh(mesh, style="points", render_points_as_spheres=True, point_size=6, color="red")
        if reset_camera:
            self.plotter.reset_camera()
        self.plotter.render()
        self.display_mode = mode

    def load_mesh(self, filename):
        try:
            import pyvista as pv
            mesh = pv.read(filename)
            self.mesh = mesh
            self.original_mesh = mesh.copy(deep=True)  # Save original for color recovery
            self.set_display_mode(getattr(self, 'display_mode', 'surface'))
        except Exception as e:
            raise RuntimeError(f"Failed to load mesh: {e}")

    def set_colormap(self, array_name, nan_color=None, nan_opacity=0.15):
        """Display the mesh colored by the given scalar array (point_data)."""
        if self.mesh is None:
            return
        self.plotter.clear()
        try:
            self.plotter.remove_scalar_bar()
        except Exception:
            pass
        mesh = self.mesh
        normals = mesh.point_data.get('Normals', None)
        if normals is None or (hasattr(mesh, 'n_points') and (not hasattr(normals, 'shape') or normals.shape[0] != mesh.n_points)):
            mesh.compute_normals(inplace=True)
        if array_name in mesh.point_data:
            if nan_color is None:
                nan_color = "#b0b0b0"
            scalars = mesh.point_data[array_name]
            # --- Robust per-point opacity fade logic ---
            use_per_point_opacity = False
            opacity = 1.0
            if (
                isinstance(mesh, pv.PolyData)
                and hasattr(mesh, 'n_points')
                and isinstance(scalars, np.ndarray)
                and scalars.ndim == 1
                and scalars.shape[0] == mesh.n_points
            ):
                # Per-point opacity supported
                opacity = np.ones(mesh.n_points, dtype=float)
                safe_opacity = nan_opacity if nan_opacity > 0 else 0.05
                mask = np.isnan(scalars)
                opacity[mask] = safe_opacity
                use_per_point_opacity = True
            # Compose kwargs
            kwargs = dict(
                scalars=array_name,
                cmap="viridis",
                show_edges=False,
                smooth_shading=True,
                nan_color=nan_color,
                ambient=0.35,
                specular=0.45,
                specular_power=18,
                show_scalar_bar=False,
            )
            if use_per_point_opacity:
                kwargs["opacity"] = opacity
            else:
                kwargs["opacity"] = 1.0  # fallback: fully opaque
            self.plotter.add_mesh(mesh, **kwargs)
        else:
            self.set_display_mode('surface', reset_camera=False)
            return
        self.plotter.render()
