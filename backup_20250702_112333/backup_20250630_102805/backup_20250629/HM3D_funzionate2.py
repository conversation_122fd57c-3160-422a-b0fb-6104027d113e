import sys
import os
# Configurazione ambiente per NVIDIA (deve essere PRIMA di qualsiasi import Qt/PyVista)
os.environ["__GLX_VENDOR_LIBRARY_NAME"] = "nvidia"
os.environ["__NV_PRIME_RENDER_OFFLOAD"] = "1"
os.environ["DISPLAY"] = ":0"

import numpy as np
import pyvista as pv
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, 
    QLabel, QFileDialog, QMessageBox, QHBoxLayout, QSpacerItem, QSizePolicy, QDialog, QFormLayout, QDoubleSpinBox, QDialogButtonBox, QCheckBox, QComboBox, QSlider, QSpinBox, QGroupBox, QFrame, QToolBar, QInputDialog, QScrollArea, QStatusBar, QProgressDialog
)
from PySide6.QtCore import Qt
from pyvistaqt import QtInteractor
from PySide6.QtGui import QIcon, QAction
import csv
import imageio
import json

# --- MeshAnalysisDialog: mesh processing options dialog ---
class MeshAnalysisDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Mesh Analysis / Healing")
        self.setModal(True)
        layout = QVBoxLayout(self)
        self.clean_cb = QCheckBox("Clean mesh (remove duplicate points, degenerate faces)")
        self.clean_cb.setChecked(True)
        self.decimate_cb = QCheckBox("Decimate mesh (reduce # faces)")
        self.decimate_cb.setChecked(False)
        self.decimate_slider = QSlider(Qt.Horizontal)
        self.decimate_slider.setRange(1, 100)
        self.decimate_slider.setValue(50)
        self.decimate_label = QLabel("Target: 50% faces")
        self.decimate_slider.valueChanged.connect(
            lambda v: self.decimate_label.setText(f"Target: {v}% faces")
        )
        decimate_layout = QHBoxLayout()
        decimate_layout.addWidget(self.decimate_cb)
        decimate_layout.addWidget(self.decimate_slider)
        decimate_layout.addWidget(self.decimate_label)
        self.smooth_cb = QCheckBox("Smooth mesh (Laplacian)")
        self.smooth_cb.setChecked(False)
        self.fill_holes_cb = QCheckBox("Fill holes")
        self.fill_holes_cb.setChecked(False)
        self.remove_islands_cb = QCheckBox("Remove isolated pieces")
        self.remove_islands_cb.setChecked(False)
        self.normals_cb = QCheckBox("Recompute normals")
        self.normals_cb.setChecked(False)
        layout.addWidget(self.clean_cb)
        layout.addLayout(decimate_layout)
        layout.addWidget(self.smooth_cb)
        layout.addWidget(self.fill_holes_cb)
        layout.addWidget(self.remove_islands_cb)
        layout.addWidget(self.normals_cb)
        btns = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        btns.accepted.connect(self.accept)
        btns.rejected.connect(self.reject)
        layout.addWidget(btns)
    def get_options(self):
        return {
            'clean': self.clean_cb.isChecked(),
            'decimate': self.decimate_cb.isChecked(),
            'decimate_target': self.decimate_slider.value(),
            'smooth': self.smooth_cb.isChecked(),
            'fill_holes': self.fill_holes_cb.isChecked(),
            'remove_islands': self.remove_islands_cb.isChecked(),
            'recompute_normals': self.normals_cb.isChecked(),
        }

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Masonry Deformation Analysis (PyVista+NVIDIA)")
        self.setGeometry(100, 100, 900, 700)
        self._setup_state()
        self._setup_sidebar()
        self._setup_viewer()
        self._setup_menu()
        self._setup_connections()
        self._init_stat_label()
        self._colorbar_visible = True
        self.plotter.interactor.AddObserver('EndInteractionEvent', self._update_label_orientations)

    def _setup_state(self):
        self.mesh = None
        self.plane_points = []
        self.point_labels = []
        self.deformations = None
        self.texture = None
        self.use_shading = True
        self.picking_enabled = False
        self.plane = None
        self.arrow = None
        self.show_plane = True
        self.heatmap_direction = 1
        self.picked_points_actors = []
        self.label_actors = []
        self.bbox_actor = None
        self.bbox_bounds = None
        self.bbox_visible = False
        self._box_widget_active = False
        self.show_edges = False
        self._undo_stack = []
        self._redo_stack = []
        self._current_global_scale = 1.0
        self.mesh_view_mode = 'surface'
        self.current_cmap = "viridis"
        self.n_colors = 10

    def _setup_sidebar(self):
        self.sidebar_widget = QWidget()
        self.sidebar_layout = QVBoxLayout(self.sidebar_widget)
        self.sidebar_layout.setContentsMargins(5, 5, 5, 5)
        self.sidebar_layout.setSpacing(8)
        self.sidebar_scroll = QScrollArea()
        self.sidebar_scroll.setWidgetResizable(True)
        self.sidebar_scroll.setWidget(self.sidebar_widget)
        # Plane/analysis controls
        self.plane_controls_layout = QVBoxLayout()
        self.btn_define_plane = QPushButton("Define Plane (3 Points)")
        self.btn_define_plane.setToolTip("Pick 3 points to define the analysis plane.")
        self.plane_controls_layout.addWidget(self.btn_define_plane)
        self.btn_remove_last_point = QPushButton("Remove Last Point")
        self.btn_remove_last_point.setToolTip("Remove the last picked point.")
        self.plane_controls_layout.addWidget(self.btn_remove_last_point)
        self.thickness_label = QLabel("Slab Thickness:")
        self.thickness_spin = QDoubleSpinBox()
        self.thickness_spin.setRange(0.001, 10.0)
        self.thickness_spin.setSingleStep(0.01)
        self.thickness_spin.setValue(0.10)
        self.thickness_spin.setSuffix(" m")
        self.thickness_spin.setToolTip("Thickness of the region (slab) around the plane.")
        self.plane_controls_layout.addWidget(self.thickness_label)
        self.plane_controls_layout.addWidget(self.thickness_spin)
        self.chk_show_slab = QCheckBox("Show Slab Region")
        self.chk_show_slab.setChecked(True)
        self.plane_controls_layout.addWidget(self.chk_show_slab)
        self.btn_calculate = QPushButton("Calculate Deformations")
        self.plane_controls_layout.addWidget(self.btn_calculate)
        self.btn_visualize = QPushButton("Show 3D Heatmap")
        self.plane_controls_layout.addWidget(self.btn_visualize)
        self.plane_controls_layout.addStretch(1)
        plane_group = QGroupBox("Region Analysis")
        plane_group.setLayout(self.plane_controls_layout)
        # Visualization controls
        self.vis_controls_layout = QVBoxLayout()
        self.cmap_label = QLabel("Colormap:")
        self.cmap_combo = QComboBox()
        self.cmap_combo.addItems([
            "viridis", "plasma", "inferno", "magma", "cividis", "coolwarm", "RdBu", "jet", "gray"
        ])
        self.cmap_combo.setCurrentText("viridis")
        self.n_colors_label = QLabel("# Colors:")
        self.n_colors_spin = QSpinBox()
        self.n_colors_spin.setRange(2, 20)
        self.n_colors_spin.setValue(10)
        self.vis_controls_layout.addWidget(self.cmap_label)
        self.vis_controls_layout.addWidget(self.cmap_combo)
        self.vis_controls_layout.addWidget(self.n_colors_label)
        self.vis_controls_layout.addWidget(self.n_colors_spin)
        self.bg_label = QLabel("Background:")
        self.bg_combo = QComboBox()
        self.bg_combo.addItems(["Gray", "White", "Black", "Light Blue"])
        self.bg_combo.setCurrentText("Light Blue")
        self.vis_controls_layout.addWidget(self.bg_label)
        self.vis_controls_layout.addWidget(self.bg_combo)
        self.view_label = QLabel("View Direction:")
        self.view_combo = QComboBox()
        self.view_combo.addItems([
            "Top", "Bottom", "Left", "Right", "Front", "Back"
        ])
        self.view_combo.setCurrentText("Front")
        self.vis_controls_layout.addWidget(self.view_label)
        self.vis_controls_layout.addWidget(self.view_combo)
        self.proj_label = QLabel("Projection Mode:")
        self.proj_combo = QComboBox()
        self.proj_combo.addItems(["Perspective", "Orthographic"])
        self.proj_combo.setCurrentText("Perspective")
        self.vis_controls_layout.addWidget(self.proj_label)
        self.vis_controls_layout.addWidget(self.proj_combo)
        self.btn_reset_deformation = QPushButton("Reset Deformation/Heatmap")
        self.vis_controls_layout.addWidget(self.btn_reset_deformation)
        self.scale_group = QGroupBox("Model Scaling")
        self.scale_layout = QVBoxLayout()
        self.global_scale_label = QLabel("Global Scale Factor:")
        self.global_scale_spin = QDoubleSpinBox()
        self.global_scale_spin.setRange(0.001, 10000.0)
        self.global_scale_spin.setSingleStep(0.01)
        self.global_scale_spin.setValue(1.0)
        self.global_scale_spin.setDecimals(4)
        self.global_scale_spin.setToolTip("Multiply all coordinates by this factor.")
        self.scale_layout.addWidget(self.global_scale_label)
        self.scale_layout.addWidget(self.global_scale_spin)
        self.btn_2pt_scale = QPushButton("2-Point Scale Tool")
        self.btn_2pt_scale.setToolTip("Pick 2 points, enter real distance, and rescale model.")
        self.scale_layout.addWidget(self.btn_2pt_scale)
        self.scale_group.setLayout(self.scale_layout)
        self.vis_controls_layout.addWidget(self.scale_group)
        self.vis_controls_layout.addStretch(1)
        vis_group = QGroupBox("Visualization")
        vis_group.setLayout(self.vis_controls_layout)
        self.sidebar_layout.addWidget(plane_group)
        self.sidebar_layout.addWidget(vis_group)
        self.sidebar_layout.addStretch(1)
        # Annotations controls
        self.anno_group = QGroupBox("Annotations")
        self.anno_layout = QVBoxLayout()
        self.btn_add_marker = QPushButton("Add Marker")
        self.btn_add_marker.setToolTip("Add a colored sphere marker at a picked point.")
        self.anno_layout.addWidget(self.btn_add_marker)
        self.btn_add_arrow = QPushButton("Add Arrow")
        self.btn_add_arrow.setToolTip("Add an arrow annotation at a picked point (direction: camera view).")
        self.anno_layout.addWidget(self.btn_add_arrow)
        self.btn_add_note = QPushButton("Add Text Note")
        self.btn_add_note.setToolTip("Add a text note at a picked point.")
        self.anno_layout.addWidget(self.btn_add_note)
        self.anno_group.setLayout(self.anno_layout)
        self.sidebar_layout.insertWidget(2, self.anno_group)
        # Toggle button for sidebar
        self.btn_toggle_sidebar = QPushButton()
        self.btn_toggle_sidebar.setIcon(QIcon.fromTheme("go-previous"))
        self.btn_toggle_sidebar.setToolTip("Show/Hide controls")
        self.btn_toggle_sidebar.setCheckable(True)
        self.btn_toggle_sidebar.setChecked(True)
        self.btn_toggle_sidebar.setFixedWidth(28)

    def _setup_viewer(self):
        self.central_layout = QHBoxLayout()
        self.central_layout.setContentsMargins(0, 0, 0, 0)
        self.central_layout.setSpacing(0)
        self.central_layout.addWidget(self.btn_toggle_sidebar, alignment=Qt.AlignLeft)
        self.central_layout.addWidget(self.sidebar_scroll)
        self.viewer_frame = QFrame()
        self.viewer_layout = QVBoxLayout(self.viewer_frame)
        self.viewer_layout.setContentsMargins(0, 0, 0, 0)
        self.viewer_layout.setSpacing(0)
        title_label = QLabel("3D Masonry Deformation Analysis")
        font = title_label.font()
        font.setPointSize(16)
        font.setBold(True)
        title_label.setFont(font)
        title_label.setAlignment(Qt.AlignCenter)
        self.viewer_layout.addWidget(title_label)
        self.viewer_layout.addSpacing(10)
        self.plotter = QtInteractor(self.viewer_frame)
        self.plotter.enable_trackball_style()
        self.viewer_layout.addWidget(self.plotter.interactor)
        self.central_layout.addWidget(self.viewer_frame, stretch=1)
        self.main_widget = QWidget()
        self.main_widget.setLayout(self.central_layout)
        self.setCentralWidget(self.main_widget)
        self.main_widget.setStyleSheet("background-color: #f7f7fa;")
        self.plotter.add_axes(interactive=False, line_width=2, color='black', x_color='red', y_color='green', z_color='blue')
        pv.set_plot_theme("document")
        self.plotter.set_background("white")
        self.plotter.enable_anti_aliasing()

    def _setup_menu(self):
        menubar = self.menuBar()
        file_menu = menubar.addMenu("File")
        action_load_model = QAction("Load 3D Model", self)
        action_load_model.triggered.connect(self.load_model)
        file_menu.addAction(action_load_model)
        action_load_texture = QAction("Load Texture", self)
        action_load_texture.triggered.connect(self.load_texture)
        file_menu.addAction(action_load_texture)
        file_menu.addSeparator()
        action_screenshot = QAction("Save Screenshot", self)
        action_screenshot.triggered.connect(self.save_screenshot)
        file_menu.addAction(action_screenshot)
        file_menu.addSeparator()
        action_exit = QAction("Exit", self)
        action_exit.triggered.connect(self.close)
        file_menu.addAction(action_exit)
        process_menu = menubar.addMenu("Process Mesh")
        action_clean_mesh = QAction("Clean Mesh", self)
        action_clean_mesh.triggered.connect(self.show_clean_mesh_dialog)
        process_menu.addAction(action_clean_mesh)
        action_mesh_analysis = QAction("Mesh Analysis/Healing...", self)
        action_mesh_analysis.triggered.connect(self.show_mesh_analysis_dialog)
        process_menu.addAction(action_mesh_analysis)
        help_menu = menubar.addMenu("Help")
        action_about = QAction("About", self)
        action_about.triggered.connect(self.show_about_dialog)
        help_menu.addAction(action_about)
        edit_menu = menubar.addMenu("Edit")
        self.action_undo = QAction("Undo", self)
        self.action_undo.setShortcut("Ctrl+Z")
        self.action_undo.triggered.connect(self.undo_action)
        edit_menu.addAction(self.action_undo)
        self.action_redo = QAction("Redo", self)
        self.action_redo.setShortcut("Ctrl+Y")
        self.action_redo.triggered.connect(self.redo_action)
        edit_menu.addAction(self.action_redo)
        view_menu = menubar.addMenu("View")
        self.action_view_surface = QAction("Surface", self, checkable=True)
        self.action_view_surface.setChecked(True)
        self.action_view_surface.triggered.connect(lambda: self.set_mesh_view_mode('surface'))
        view_menu.addAction(self.action_view_surface)
        self.action_view_points = QAction("Points", self, checkable=True)
        self.action_view_points.triggered.connect(lambda: self.set_mesh_view_mode('points'))
        view_menu.addAction(self.action_view_points)
        self.action_view_wireframe = QAction("Wireframe (Edges)", self, checkable=True)
        self.action_view_wireframe.triggered.connect(lambda: self.set_mesh_view_mode('wireframe'))
        view_menu.addAction(self.action_view_wireframe)
        self.action_view_surface_edges = QAction("Surface + Edges", self, checkable=True)
        self.action_view_surface_edges.triggered.connect(lambda: self.set_mesh_view_mode('surface_edges'))
        view_menu.addAction(self.action_view_surface_edges)
        self.action_view_heatmap_edges = QAction("Heatmap + Edges", self, checkable=True)
        self.action_view_heatmap_edges.triggered.connect(lambda: self.set_mesh_view_mode('heatmap_edges'))
        view_menu.addAction(self.action_view_heatmap_edges)
        self.action_view_points_edges = QAction("Points + Edges", self, checkable=True)
        self.action_view_points_edges.triggered.connect(lambda: self.set_mesh_view_mode('points_edges'))
        view_menu.addAction(self.action_view_points_edges)
        self.action_edl = QAction("Eye-Dome Lighting (EDL)", self, checkable=True)
        self.action_edl.setChecked(False)
        self.action_edl.triggered.connect(self.toggle_edl)
        view_menu.addSeparator()
        view_menu.addAction(self.action_edl)
        self.view_actions = [
            self.action_view_surface,
            self.action_view_points,
            self.action_view_wireframe,
            self.action_view_surface_edges,
            self.action_view_heatmap_edges,
            self.action_view_points_edges
        ]

    def _setup_connections(self):
        self.btn_define_plane.clicked.connect(self.define_plane)
        self.btn_remove_last_point.clicked.connect(self.remove_last_point)
        self.btn_calculate.clicked.connect(self.calculate_deformations)
        self.btn_visualize.clicked.connect(self.visualize_heatmap)
        self.btn_reset_deformation.clicked.connect(self.reset_deformation)
        self.cmap_combo.currentTextChanged.connect(self.on_cmap_changed)
        self.n_colors_spin.valueChanged.connect(self.on_n_colors_changed)
        self.bg_combo.currentTextChanged.connect(self.on_bg_color_changed)
        self.view_combo.currentTextChanged.connect(self.on_view_combo_changed)
        self.proj_combo.currentTextChanged.connect(self.on_proj_combo_changed)
        self.global_scale_spin.valueChanged.connect(self.apply_global_scale)
        self.btn_2pt_scale.clicked.connect(self.start_2pt_scale)
        self.chk_show_slab.stateChanged.connect(self.toggle_slab_visibility)
        self.thickness_spin.valueChanged.connect(self.live_update_slab_thickness)
        self.btn_add_marker.clicked.connect(self.add_marker_annotation)
        self.btn_add_arrow.clicked.connect(self.add_arrow_annotation)
        self.btn_add_note.clicked.connect(self.add_text_annotation)
        self.btn_toggle_sidebar.clicked.connect(self.toggle_sidebar)

    def toggle_edl(self, checked):
        """Toggle Eye-Dome Lighting (EDL) in the 3D view."""
        # PyVistaQt supports EDL via plotter.enable_eye_dome_lighting()
        if checked:
            self.plotter.enable_eye_dome_lighting()
            self.statusBar().showMessage("Eye-Dome Lighting enabled.", 3000)
        else:
            self.plotter.disable_eye_dome_lighting()
            self.statusBar().showMessage("Eye-Dome Lighting disabled.", 3000)
        self.plotter.render()

    def load_model(self):
        """Load a 3D model (e.g. .stl, .obj, .ply)"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select 3D model", "", "3D Files (*.stl *.obj *.ply *.vtk *.vtp)"
        )
        if file_path:
            try:
                self.mesh = pv.read(file_path)
                # Ensure mesh has valid point normals
                if self.mesh.point_normals is None or len(self.mesh.point_normals) != self.mesh.n_points:
                    self.mesh.compute_normals(inplace=True)
                self.deformations = None  # Reset deformations when loading a new mesh
                self.texture = None  # Reset texture
                self.show_current_mesh()
                self.plotter.reset_camera()  # Only reset camera here
                self.plotter.render()  # Ensure the view updates after camera reset
                QMessageBox.information(self, "Model loaded", f"Model loaded: {os.path.basename(file_path)}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Error loading model:\n{str(e)}")

    def load_texture(self):
        """Load a texture and apply it to the 3D model if present."""
        if self.mesh is None:
            QMessageBox.warning(self, "Warning", "Please load a 3D model first!")
            return
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select texture", "", "Images (*.jpg *.jpeg *.png *.bmp *.tif *.tiff)"
        )
        if file_path:
            try:
                self.texture = pv.read_texture(file_path)
                self.show_current_mesh()
                self.plotter.reset_camera()
                QMessageBox.information(self, "Texture loaded", f"Texture applied: {os.path.basename(file_path)}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Error loading texture:\n{str(e)}")

    def save_screenshot(self):
        """Save a screenshot of the 3D view to an image file."""
        file_path, _ = QFileDialog.getSaveFileName(self, "Save Screenshot", "screenshot.png", "PNG Image (*.png);;JPEG Image (*.jpg *.jpeg)")
        if file_path:
            try:
                img = self.plotter.screenshot(return_img=True)
                import imageio
                imageio.imwrite(file_path, img)
                QMessageBox.information(self, "Screenshot Saved", f"Screenshot saved to: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save screenshot:\n{str(e)}")

    def save_highres_screenshot(self):
        """Save a high-resolution screenshot with overlays."""
        file_path, _ = QFileDialog.getSaveFileName(self, "Save High-Res Screenshot", "screenshot.png", "PNG Image (*.png);;JPEG Image (*.jpg *.jpeg)")
        if file_path:
            try:
                img = self.plotter.screenshot(return_img=True, scale=3)
                imageio.imwrite(file_path, img)
                QMessageBox.information(self, "Screenshot Saved", f"High-res screenshot saved to: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save screenshot:\n{str(e)}")

    def apply_global_scale(self, new_scale):
        """
        Apply a global scaling factor to the mesh and update the view. Supports undo/redo.
        """
        import numpy as np
        if not hasattr(self, 'mesh') or self.mesh is None:
            return  # No mesh loaded, nothing to scale
        prev_scale = getattr(self, '_current_global_scale', 1.0)
        if np.isclose(new_scale, prev_scale):
            return  # No change
        # Push undo state
        if hasattr(self, 'push_undo'):
            self.push_undo('global_scale', {
                'prev_scale': prev_scale,
                'new_scale': new_scale
            })
        # Apply scaling
        scale_factor = new_scale / prev_scale
        self.mesh.points *= scale_factor
        self._current_global_scale = new_scale
        # If there are picked points, scale them too
        if hasattr(self, 'plane_points') and self.plane_points:
            self.plane_points = [np.array(pt) * scale_factor for pt in self.plane_points]
        # Redraw mesh and overlays
        self.update_mesh_visualization()

    def update_mesh_visualization(self):
        """
        Redraw the mesh and overlays after scaling or other changes.
        """
        if self.mesh is not None:
            self.plotter.clear()
            self.plotter.add_mesh(self.mesh, show_edges=self.show_edges, color='white', opacity=1.0)
            self.plotter.add_axes(interactive=False, line_width=2, color='black', x_color='red', y_color='green', z_color='blue')
            self.plotter.reset_camera()
            self.plotter.render()

    def set_mesh_view_mode(self, mode):
        """Switch mesh display mode (surface, points, wireframe, etc.) and update the view."""
        self.mesh_view_mode = mode
        # Uncheck all view actions except the selected one
        for action in getattr(self, 'view_actions', []):
            if hasattr(action, 'setChecked'):
                action.setChecked(False)
        mode_map = {
            'surface': self.action_view_surface,
            'points': self.action_view_points,
            'wireframe': self.action_view_wireframe,
            'surface_edges': self.action_view_surface_edges,
            'heatmap_edges': self.action_view_heatmap_edges,
            'points_edges': self.action_view_points_edges
        }
        if mode in mode_map:
            mode_map[mode].setChecked(True)
        # Redraw mesh in selected mode
        self.show_current_mesh()

    def show_clean_mesh_dialog(self):
        """Show a dialog to clean the mesh (remove duplicate points, degenerate faces, etc.)."""
        if self.mesh is None:
            QMessageBox.warning(self, "Warning", "Please load a 3D model first!")
            return
        # Example cleaning: remove duplicate points and degenerate faces
        cleaned = self.mesh.clean(inplace=False)
        n_removed = self.mesh.n_points - cleaned.n_points
        self.mesh = cleaned
        self.show_current_mesh()
        self.plotter.reset_camera()
        self.plotter.render()
        QMessageBox.information(self, "Mesh Cleaned", f"Mesh cleaned. {n_removed} duplicate points removed.")

    def show_about_dialog(self):
        """Show the About dialog with app information."""
        QMessageBox.information(self, "About", (
            "<b>Masonry Deformation Analysis 3D</b><br>"
            "<br>"
            "A tool for visualizing and analyzing wall deformations in archaeological 3D models.<br>"
            "<br>"
            "<b>Authors: <AUTHORS>
            "<b>Powered by:</b> PyVista, PySide6, PyVistaQt<br>"
            "<br>"
            "For help and documentation, see the project README or contact the developers."
        ))

    def undo_action(self):
        """Undo the last action if possible."""
        if not self._undo_stack:
            QMessageBox.information(self, "Undo", "Nothing to undo.")
            return
        action_type, state = self._undo_stack.pop()
        # For now, just a stub. Implement full state restore as needed.
        QMessageBox.information(self, "Undo", f"Undo action: {action_type}")
        # Optionally push to redo stack
        self._redo_stack.append((action_type, state))

    def redo_action(self):
        """Redo the last undone action if possible."""
        if not self._redo_stack:
            QMessageBox.information(self, "Redo", "Nothing to redo.")
            return
        action_type, state = self._redo_stack.pop()
        # For now, just a stub. Implement full state restore as needed.
        QMessageBox.information(self, "Redo", f"Redo action: {action_type}")
        # Optionally push to undo stack
        self._undo_stack.append((action_type, state))

    def add_marker_annotation(self):
        """Add a colored sphere marker at a picked point."""
        if self.mesh is None:
            QMessageBox.warning(self, "Warning", "Please load a 3D model first!")
            return
        point = self.pick_point_dialog()
        if point is not None:
            sphere = pv.Sphere(radius=0.02 * self.get_model_scale(), center=point)
            self.plotter.add_mesh(sphere, color="red", name="marker", reset_camera=False)
            self.plotter.render()

    def add_arrow_annotation(self):
        """Add an arrow annotation at a picked point (direction: camera view)."""
        if self.mesh is None:
            QMessageBox.warning(self, "Warning", "Please load a 3D model first!")
            return
        point = self.pick_point_dialog()
        if point is not None:
            camera_pos = np.array(self.plotter.camera.GetPosition())
            direction = camera_pos - point
            direction = direction / np.linalg.norm(direction)
            arrow = pv.Arrow(start=point, direction=direction, scale=0.1 * self.get_model_scale())
            self.plotter.add_mesh(arrow, color="blue", name="arrow", reset_camera=False)
            self.plotter.render()

    def add_text_annotation(self):
        """Add a balloon-style text note at a picked point, always facing the camera."""
        if self.mesh is None:
            QMessageBox.warning(self, "Warning", "Please load a 3D model first!")
            return
        point = self.pick_point_dialog()
        if point is not None:
            text, ok = QInputDialog.getText(self, "Add Text Note", "Enter note text:")
            if ok and text:
                # Use balloon label instead of 3D text
                label = self.plotter.add_point_labels(
                    [point], [text],
                    font_size=16,
                    text_color="black",
                    shape_opacity=0.7,
                    shape_color="#cce6ff",
                    name="text_note",
                    always_visible=True,
                    reset_camera=False
                )
                self.plotter.render()

    def pick_point_dialog(self):
        """Helper to pick a point on the mesh interactively (robust, non-blocking)."""
        if self.mesh is None:
            QMessageBox.warning(self, "Warning", "Please load a 3D model first!")
            return None
        self._picked_point = None
        self._picking_done = False
        def _on_pick(point):
            if point is None or not np.isfinite(point).all():
                self.statusBar().showMessage("Could not determine picked point. Try again.", 4000)
                self.plotter.disable_picking()
                self._picking_done = True
                return
            idx = np.argmin(np.linalg.norm(self.mesh.points - point, axis=1))
            self._picked_point = self.mesh.points[idx]
            self._picking_done = True
            self.plotter.disable_picking()
        self.plotter.enable_point_picking(
            callback=_on_pick,
            use_picker=True,
            show_message=False,
            show_point=True,
            left_clicking=True,
            tolerance=0.01 * self.get_model_scale()
        )
        app = QApplication.instance()
        while not getattr(self, '_picking_done', False):
            app.processEvents()
        return getattr(self, '_picked_point', None)

    def get_model_scale(self):
        """Return the largest dimension of the model's bounding box (for scale-aware thresholds)."""
        if self.mesh is not None:
            bounds = self.mesh.bounds
            return max(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4])
        return 1.0

    def toggle_sidebar(self):
        """Show or hide the sidebar widget and update the toggle button icon."""
        visible = self.sidebar_widget.isVisible()
        self.sidebar_widget.setVisible(not visible)
        if visible:
            self.btn_toggle_sidebar.setIcon(QIcon.fromTheme("go-next"))
        else:
            self.btn_toggle_sidebar.setIcon(QIcon.fromTheme("go-previous"))

    def toggle_slab_visibility(self, state):
        """Show or hide the slab region actor in the 3D view based on checkbox state."""
        if hasattr(self, '_slab_actor') and self._slab_actor is not None:
            if state:
                self.plotter.add_actor(self._slab_actor)
            else:
                self.plotter.remove_actor(self._slab_actor)
        self.plotter.render()

    def remove_last_point(self):
        """Remove the last picked point and update the visualization."""
        if not self.plane_points:
            QMessageBox.information(self, "No Points", "No points to remove.")
            return
        self.plane_points.pop()
        # Remove last label actor
        if self.point_labels:
            lbl = self.point_labels.pop()
            self.plotter.remove_actor(lbl)
        # Remove and redraw the remaining points
        self.plotter.remove_actor("plane_points")
        if self.plane_points:
            self._draw_plane_points()
        else:
            self.plotter.render()

    def calculate_deformations(self):
        """Calculate deformations for points within a slab (thickness) around the defined plane."""
        if self.mesh is None:
            QMessageBox.warning(self, "Warning", "Please load a 3D model first!")
            return
        if len(self.plane_points) != 3:
            QMessageBox.warning(self, "Warning", "Please define a plane (pick 3 points) first!")
            return
        p1, p2, p3 = np.array(self.plane_points)
        v1 = p2 - p1
        v2 = p3 - p1
        normal = np.cross(v1, v2)
        normal = normal / np.linalg.norm(normal)
        center = (p1 + p2 + p3) / 3
        thickness = self.thickness_spin.value() / 2.0  # half-thickness on each side
        points = self.mesh.points
        # Signed distance from plane
        dists = np.dot(points - center, normal)
        mask = np.abs(dists) <= thickness
        deformations = np.full(points.shape[0], np.nan)
        deformations[mask] = dists[mask]
        self.deformations = deformations
        # Immediately show heatmap after calculation
        self.visualize_heatmap()
        # Use status bar for feedback instead of QMessageBox
        self.statusBar().showMessage(f"Deformations calculated (slab ±{thickness*2:.3f} m). Heatmap displayed.", 5000)

    def visualize_heatmap(self, show_edges=None):
        """Visualize the deformation heatmap on the mesh using the current colormap and discretization settings. Adds colorbar and summary."""
        if self.mesh is None or self.deformations is None:
            QMessageBox.warning(self, "Warning", "No deformations to visualize. Please calculate deformations first.")
            return
        self.plotter.clear()
        scalars = self.deformations.copy()
        if show_edges is None:
            show_edges = (self.mesh_view_mode == 'heatmap_edges')
        mesh_actor = self.plotter.add_mesh(
            self.mesh,
            scalars=scalars,
            cmap=self.current_cmap,
            nan_color="lightgray",
            show_edges=show_edges,
            smooth_shading=self.use_shading,
            clim=[np.nanmin(scalars), np.nanmax(scalars)] if np.any(~np.isnan(scalars)) else None,
            n_colors=self.n_colors,
            name="mesh"
        )
        self.plotter.remove_scalar_bar()
        if getattr(self, '_colorbar_visible', True):
            self.plotter.add_scalar_bar(title="Deformation (m)", n_labels=5, vertical=True, fmt="%.3f")
        # Restore mesh stats overlay after heatmap
        self.update_mesh_stats_overlay()
        self.plotter.render()
        # Show statistics in sidebar label
        valid = scalars[~np.isnan(scalars)]
        if valid.size > 0:
            msg = f"min: {valid.min():.4f}  max: {valid.max():.4f}  mean: {valid.mean():.4f}  std: {valid.std():.4f}"
            self.stat_label.setText(msg)
            self.stat_label.show()
        else:
            self.stat_label.setText("No valid deformation values.")
            self.stat_label.show()

    def update_mesh_stats_overlay(self):
        """Show mesh statistics as a 3D overlay in the PyVista window."""
        if not hasattr(self, 'plotter') or self.mesh is None:
            return
        # Remove previous stats overlay if present
        if hasattr(self, '_stats_text_actor') and self._stats_text_actor is not None:
            try:
                self.plotter.remove_actor(self._stats_text_actor)
            except Exception:
                pass
            self._stats_text_actor = None
        mesh = self.mesh
        n_points = mesh.n_points
        n_cells = mesh.n_cells if hasattr(mesh, 'n_cells') else 0
        area = mesh.area if hasattr(mesh, 'area') else None
        # Volume: only if mesh is closed
        try:
            is_closed = mesh.is_all_closed() if hasattr(mesh, 'is_all_closed') else mesh.is_closed()
        except Exception:
            is_closed = False
        volume = mesh.volume if is_closed and hasattr(mesh, 'volume') else None
        bounds = mesh.bounds
        bbox = (bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4])
        msg = f"Points: {n_points}  Cells: {n_cells}"
        if area is not None:
            msg += f"  Area: {area:.2f} m²"
        if volume is not None:
            msg += f"  Vol: {volume:.2f} m³"
        msg += f"  BBox: {bbox[0]:.2f}×{bbox[1]:.2f}×{bbox[2]:.2f} m"
        # Add overlay in top left corner, smaller font
        self._stats_text_actor = self.plotter.add_text(
            msg,
            position='upper_left',
            font_size=8,
            color='black',
            name='mesh_stats_overlay',
            shadow=False,
            viewport=True
        )
        self.plotter.render()

    def export_deformation_data(self):
        """Export deformation data as CSV or JSON."""
        if self.mesh is None or self.deformations is None:
            QMessageBox.warning(self, "Warning", "No deformation data to export.")
            return
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Deformation Data", "deformation.csv", "CSV (*.csv);;JSON (*.json)")
        if not file_path:
            return
        try:
            if file_path.endswith('.json'):
                data = [{"point": self.mesh.points[i].tolist(), "deformation": float(self.deformations[i])} for i in range(len(self.deformations)) if not np.isnan(self.deformations[i])]
                with open(file_path, 'w') as f:
                    json.dump(data, f, indent=2)
            else:
                with open(file_path, 'w', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow(["x", "y", "z", "deformation"])
                    for i in range(len(self.deformations)):
                        if not np.isnan(self.deformations[i]):
                            writer.writerow([
                                self.mesh.points[i][0],
                                self.mesh.points[i][1],
                                self.mesh.points[i][2],
                                float(self.deformations[i])
                            ])
            QMessageBox.information(self, "Export Complete", f"Deformation data exported to: {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Export Error", str(e))

    def on_cmap_changed(self, cmap):
        """Handle colormap selection change."""
        self.current_cmap = cmap
        if self.deformations is not None:
            self.visualize_heatmap()

    def on_n_colors_changed(self, n):
        """Handle number of colors (discretization) change."""
        self.n_colors = n
        if self.deformations is not None:
            self.visualize_heatmap()

    def on_bg_color_changed(self, color_name):
        """Handle background color change from combo box."""
        color_map = {
            "Gray": "#f7f7fa",
            "White": "white",
            "Black": "black",
            "Light Blue": "#e6f2ff"
        }
        color = color_map.get(color_name, "#f7f7fa")
        self.plotter.set_background(color)
        self.main_widget.setStyleSheet(f"background-color: {color};")
        self.plotter.render()

    def on_view_combo_changed(self, view):
        """Handle view direction change from combo box."""
        views = {
            "Top": (0, 0, 1),
            "Bottom": (0, 0, -1),
            "Left": (-1, 0, 0),
            "Right": (1, 0, 0),
            "Front": (0, 1, 0),
            "Back": (0, -1, 0)
        }
        if view in views:
            self.plotter.view_vector(views[view])
            self.plotter.reset_camera()
            self.plotter.render()

    def on_proj_combo_changed(self, mode):
        """Handle projection mode change from combo box."""
        if mode == "Perspective":
            self.plotter.camera.SetParallelProjection(False)
        else:
            self.plotter.camera.SetParallelProjection(True)
        self.plotter.render()

    def reset_deformation(self):
        """Clear deformation results and heatmap, show plain mesh."""
        self.deformations = None
        self.show_current_mesh()
        self.statusBar().showMessage("Deformation/heatmap reset. Showing plain mesh.", 4000)

    def delete_last_annotation(self):
        """Delete the last annotation (marker, arrow, or text note) added."""
        for name in ["text_note", "arrow", "marker"]:
            if self.plotter.has_actor(name):
                self.plotter.remove_actor(name)
                self.plotter.render()
                self.statusBar().showMessage(f"Deleted last annotation: {name}", 3000)
                return
        QMessageBox.information(self, "No Annotation", "No annotation to delete.")

    def export_deformation_data(self):
        """Export deformation data as CSV or JSON."""
        if self.mesh is None or self.deformations is None:
            QMessageBox.warning(self, "Warning", "No deformation data to export.")
            return
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Deformation Data", "deformation.csv", "CSV (*.csv);;JSON (*.json)")
        if not file_path:
            return
        try:
            if file_path.endswith('.json'):
                data = [{"point": self.mesh.points[i].tolist(), "deformation": float(self.deformations[i])} for i in range(len(self.deformations)) if not np.isnan(self.deformations[i])]
                with open(file_path, 'w') as f:
                    json.dump(data, f, indent=2)
            else:
                with open(file_path, 'w', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow(["x", "y", "z", "deformation"])
                    for i in range(len(self.deformations)):
                        if not np.isnan(self.deformations[i]):
                            writer.writerow([
                                self.mesh.points[i][0],
                                self.mesh.points[i][1],
                                self.mesh.points[i][2],
                                float(self.deformations[i])
                            ])
            QMessageBox.information(self, "Export Complete", f"Deformation data exported to: {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Export Error", str(e))

    def define_plane(self):
        """Enable interactive picking of 3 points to define the analysis plane (robust, non-blocking, always resets picking state)."""
        if self.mesh is None:
            QMessageBox.warning(self, "Warning", "Please load a 3D model first!")
            return
        self.plotter.disable_picking()
        self.plane_points = []
        self.point_labels = []
        self.picking_enabled = True
        self._plane_picking_count = 0
        self.statusBar().showMessage("Pick point 1 on the mesh (click in 3D view)...", 8000)
        def _on_pick(point):
            if point is None or not np.isfinite(point).all():
                self.statusBar().showMessage("Could not determine picked point. Try again.", 4000)
                self.plotter.disable_picking()
                self.picking_enabled = False
                return
            idx = np.argmin(np.linalg.norm(self.mesh.points - point, axis=1))
            picked_point = self.mesh.points[idx]
            # Only check distance to immediately previous point
            if self.plane_points:
                min_dist = 0.001 * self.get_model_scale()
                last_point = np.array(self.plane_points[-1])
                dist = np.linalg.norm(last_point - picked_point)
                if dist < min_dist:
                    self.statusBar().showMessage(f"Point too close to previous pick (min {min_dist:.4g} m). Pick a different point.", 4000)
                    return
            self.plane_points.append(picked_point)
            self._draw_plane_points()
            self._plane_picking_count += 1
            if self._plane_picking_count < 3:
                self.statusBar().showMessage(f"Pick point {self._plane_picking_count+1} on the mesh (click in 3D view)...", 8000)
            else:
                self.picking_enabled = False
                self.plotter.disable_picking()
                self.statusBar().showMessage("Plane defined. You can now calculate deformations.", 5000)
        self.plotter.enable_point_picking(
            callback=_on_pick,
            use_picker=True,
            show_message=True,
            show_point=True,
            left_clicking=True,
            tolerance=0.01 * self.get_model_scale()
        )

    def _pick_point_for_plane(self, *args):
        """Deprecated: No longer used. Picking is now callback-driven in define_plane."""
        pass

    def live_update_slab_thickness(self, value=None):
        # Optionally update slab visualization in real time
        pass

    def _draw_plane_points(self):
        # Remove previous points actor
        self.plotter.remove_actor("plane_points")
        # Remove previous point labels
        for lbl in getattr(self, 'point_labels', []):
            self.plotter.remove_actor(lbl)
        self.point_labels = []
        if self.plane_points:
            # Add all picked points as a single PolyData
            pts = np.array(self.plane_points)
            self.plotter.add_mesh(
                pv.PolyData(pts),
                color="yellow",
                point_size=20,
                render_points_as_spheres=True,
                name="plane_points",
                reset_camera=False
            )
            # Add a label for each point, always flattening
            for i, pt in enumerate(pts):
                labels = self.plotter.add_point_labels(
                    [pt], [str(i+1)],
                    font_size=14,
                    text_color="black",
                    shape_opacity=0.5,
                    shape_color="#fffbe6",
                    name=f"plane_point_label_{i}",
                    always_visible=True,
                    reset_camera=False
                )
                # Always treat as a list for consistency
                if isinstance(labels, list):
                    self.point_labels.extend(labels)
                else:
                    self.point_labels.append(labels)
        self.plotter.render()

    def show_mesh_analysis_dialog(self):
        dlg = MeshAnalysisDialog(self)
        if dlg.exec() == QDialog.Accepted:
            opts = dlg.get_options()
            if self.mesh is None:
                QMessageBox.warning(self, "Warning", "Please load a 3D model first!")
                return
            mesh = self.mesh
            if opts['clean']:
                mesh = mesh.clean(inplace=False)
            if opts['decimate']:
                mesh = mesh.decimate(opts['decimate_target'] / 100.0, inplace=False)
            if opts['smooth']:
                mesh = mesh.smooth(n_iter=20, inplace=False)
            if opts['fill_holes']:
                mesh = mesh.fill_holes(1000.0, inplace=False)
            if opts['remove_islands']:
                mesh = mesh.extract_largest(inplace=False)
            if opts['recompute_normals']:
                mesh = mesh.compute_normals(inplace=False)
            self.mesh = mesh
            self.show_current_mesh()
            self.plotter.reset_camera()
            self.plotter.render()
            QMessageBox.information(self, "Mesh Analysis", "Mesh processing complete.")

    def show_statistical_summary(self):
        """Show a dialog with mesh statistics: points, triangles, area, volume, bounding box."""
        if self.mesh is None:
            QMessageBox.information(self, "Mesh Statistics", "No mesh loaded.")
            return
        mesh = self.mesh
        n_points = mesh.n_points
        n_faces = mesh.n_faces if hasattr(mesh, 'n_faces') else (mesh.faces.shape[0] // 4 if hasattr(mesh, 'faces') else 0)
        area = mesh.area if hasattr(mesh, 'area') else None
        try:
            is_closed = mesh.is_all_closed() if hasattr(mesh, 'is_all_closed') else mesh.is_closed()
        except Exception:
            is_closed = False
        volume = mesh.volume if is_closed and hasattr(mesh, 'volume') else None
        bounds = mesh.bounds
        bbox = (bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4])
        msg = f"<b>Mesh Statistics</b><br>"
        msg += f"Points: <b>{n_points}</b><br>Triangles: <b>{n_faces}</b><br>"
        if area is not None:
            msg += f"Surface Area: <b>{area:.3f} m²</b><br>"
        if volume is not None:
            msg += f"Volume: <b>{volume:.3f} m³</b><br>"
        msg += f"Bounding Box: <b>{bbox[0]:.3f} x {bbox[1]:.3f} x {bbox[2]:.3f} m</b>"
        QMessageBox.information(self, "Mesh Statistics", msg)

    def toggle_colorbar(self):
        """Toggle the visibility of the colorbar in the 3D view."""
        self._colorbar_visible = not getattr(self, '_colorbar_visible', True)
        # Update button text if present
        if hasattr(self, 'btn_colorbar'):
            if self._colorbar_visible:
                self.btn_colorbar.setText("Hide Colorbar")
            else:
                self.btn_colorbar.setText("Show Colorbar")
        # Redraw heatmap if active, else just update mesh
        if self.deformations is not None:
            self.visualize_heatmap()
        else:
            self.show_current_mesh()

    def _add_advanced_feature_buttons(self):
        adv_group = QGroupBox("Advanced Features")
        adv_layout = QVBoxLayout()
        btn_colorbar = QPushButton("Toggle Colorbar")
        btn_colorbar.clicked.connect(self.toggle_colorbar)
        adv_layout.addWidget(btn_colorbar)
        btn_stats = QPushButton("Show Statistical Summary")
        btn_stats.clicked.connect(self.show_statistical_summary)
        adv_layout.addWidget(btn_stats)
        btn_export = QPushButton("Export Deformation Data")
        btn_export.clicked.connect(self.export_deformation_data)
        adv_layout.addWidget(btn_export)
        btn_highres = QPushButton("Save High-Res Screenshot")
        btn_highres.clicked.connect(self.save_highres_screenshot)
        adv_layout.addWidget(btn_highres)
        btn_cancel_pick = QPushButton("Cancel Picking")
        btn_cancel_pick.clicked.connect(self.cancel_picking)
        adv_layout.addWidget(btn_cancel_pick)
        btn_del_anno = QPushButton("Delete Last Annotation")
        btn_del_anno.clicked.connect(self.delete_last_annotation)
        adv_layout.addWidget(btn_del_anno)
        adv_group.setLayout(adv_layout)
        self.sidebar_layout.insertWidget(3, adv_group)

    def _update_label_orientations(self, *args, **kwargs):
        """Update all annotation balloon labels to face the camera after interaction."""
        if not hasattr(self, 'label_actors') or not self.label_actors:
            return
        camera = self.plotter.camera
        cam_pos = np.array(camera.GetPosition())
        for lbl in self.label_actors:
            try:
                # For PyVista point labels, this is handled automatically, but if you use custom actors:
                if hasattr(lbl, 'SetOrientationToCamera'):
                    lbl.SetOrientationToCamera()
                elif hasattr(lbl, 'SetCamera'):  # VTK text actor
                    lbl.SetCamera(camera)
                # Otherwise, skip (PyVista's add_point_labels usually handles this)
            except Exception:
                pass
        self.plotter.render()

    def _init_stat_label(self):
        self.stat_label = QLabel()
        self.stat_label.setStyleSheet("font-size: 12px; color: #333; background: #e6f2ff; border: 1px solid #b3d1ff; padding: 4px;")
        self.stat_label.setAlignment(Qt.AlignLeft)
        self.stat_label.hide()
        self.sidebar_layout.addWidget(self.stat_label)

    def show_current_mesh(self):
        # Display the current mesh with color, texture, or default light gray. Do NOT show a false heatmap.
        if self.mesh is None:
            self.plotter.clear()
            self.plotter.render()
            return
        self.plotter.clear()
        mesh = self.mesh
        # Decide what to show: texture, per-vertex/face color, or default
        show_edges = self.show_edges or (self.mesh_view_mode in ['wireframe', 'surface_edges', 'heatmap_edges', 'points_edges'])
        # If mesh has per-vertex or per-face colors, show them
        if hasattr(mesh, 'active_scalars') and mesh.active_scalars is not None and mesh.active_scalars_name not in (None, '', 'deformation'):
            self.plotter.add_mesh(
                mesh,
                scalars=mesh.active_scalars,
                show_edges=show_edges,
                cmap=self.current_cmap,
                nan_color="lightgray",
                smooth_shading=self.use_shading,
                name="mesh"
            )
        elif self.texture is not None:
            self.plotter.add_mesh(
                mesh,
                texture=self.texture,
                show_edges=show_edges,
                color=None,
                smooth_shading=self.use_shading,
                name="mesh"
            )
        else:
            # Default: light gray
            color = "#e0e0e0"
            render_points = (self.mesh_view_mode in ['points', 'points_edges'])
            self.plotter.add_mesh(
                mesh,
                color=color,
                show_edges=show_edges,
                render_points_as_spheres=render_points,
                point_size=8 if render_points else None,
                style='points' if render_points else 'surface',
                smooth_shading=self.use_shading,
                name="mesh"
            )
        # Add overlays and axes
        self.plotter.add_axes(interactive=False, line_width=2, color='black', x_color='red', y_color='green', z_color='blue')
        self.update_mesh_stats_overlay()
        self.plotter.render()
        # Hide stat label if not in heatmap mode
        if hasattr(self, 'stat_label'):
            self.stat_label.hide()

    def start_2pt_scale(self):
        """Start the 2-point scale tool: pick 2 points, enter real distance, and rescale the model accordingly."""
        if self.mesh is None:
            QMessageBox.warning(self, "Warning", "Please load a 3D model first!")
            return
        points = []
        for i in range(2):
            self.statusBar().showMessage(f"Pick scale point {i+1} on the mesh (click in 3D view)...", 8000)
            picked_point = self.pick_point_dialog()
            if picked_point is None:
                self.statusBar().showMessage("2-point scale cancelled or failed.", 4000)
                return
            points.append(picked_point)
        dist = np.linalg.norm(np.array(points[0]) - np.array(points[1]))
        real_dist, ok = QInputDialog.getDouble(self, "2-Point Scale", f"Measured distance: {dist:.4f} m\nEnter real-world distance:", value=dist, min=1e-6, max=1e6, decimals=6)
        if not ok or real_dist <= 0:
            self.statusBar().showMessage("2-point scale cancelled.", 4000)
            return
        scale_factor = real_dist / dist
        self.global_scale_spin.setValue(self._current_global_scale * scale_factor)
        self.apply_global_scale(self._current_global_scale * scale_factor)
        self.statusBar().showMessage(f"Model rescaled by factor {scale_factor:.4g}.", 5000)
        self.plotter.remove_actor("scale_points")
        self.plotter.render()

    def cancel_picking(self):
        """Cancel any ongoing picking operation and disable picking mode."""
        self.picking_enabled = False
        self.plotter.disable_picking()
        self.statusBar().showMessage("Picking cancelled.", 3000)

# --- Main entry point ---
if __name__ == "__main__":
    import sys
    import numpy as np
    import pyvista as pv
    import os
    app = QApplication(sys.argv)
    window = MainWindow()
    window._add_advanced_feature_buttons()
    window.show()
    sys.exit(app.exec())