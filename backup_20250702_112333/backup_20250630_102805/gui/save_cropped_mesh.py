from PySide6.QtWidgets import QFileDialog, QMessageBox
import pyvista as pv
import os
import numpy as np

def save_cropped_mesh(mesh, parent=None):
    if mesh is None or mesh.n_points == 0:
        QMessageBox.warning(parent, "No Mesh", "No mesh to save.")
        return
    # --- Always transfer RGB from original mesh if available ---
    orig_mesh = getattr(parent, 'mesh_viewer', None)
    if orig_mesh and hasattr(orig_mesh, 'original_mesh'):
        orig = orig_mesh.original_mesh
        if orig is not None and 'RGB' in orig.point_data:
            from scipy.spatial import cKDTree
            src_points = np.asarray(orig.points)
            dst_points = np.asarray(mesh.points)
            src_colors = np.asarray(orig.point_data['RGB'])
            tree = cKDTree(src_points)
            dists, idxs = tree.query(dst_points)
            mesh.point_data['RGB'] = src_colors[idxs]
    # Ask user for file path
    file_path, _ = QFileDialog.getSaveFileName(
        parent,
        "Save Cropped Mesh",
        os.path.expanduser("~/cropped_mesh.ply"),
        "PLY files (*.ply);;STL files (*.stl);;OBJ files (*.obj);;VTK files (*.vtk);;All Files (*)"
    )
    if not file_path:
        return
    try:
        ext = os.path.splitext(file_path)[1].lower()
        if ext in (".ply", ".stl", ".obj", ".vtk"):
            mesh.save(file_path)
        else:
            mesh.save(file_path + ".ply")
        QMessageBox.information(parent, "Mesh Saved", f"Mesh saved to: {file_path}")
    except Exception as e:
        QMessageBox.critical(parent, "Save Error", f"Failed to save mesh:\n{e}")
