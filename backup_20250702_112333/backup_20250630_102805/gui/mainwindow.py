import sys
from PySide6.QtWidgets import (Q<PERSON><PERSON>lication, QMainWindow, QToolBar, QDockWidget, QTabWidget, QWidget, QVBoxLayout, QLabel, QListWidget, QTextEdit, QHBoxLayout, QPushButton, QFileDialog, QMessageBox, QSlider, QDoubleSpinBox, QFrame)
from PySide6.QtCore import Qt, Signal
from gui.meshviewer import MeshViewerWidget
from gui.mesh_display import MeshDisplayModeDialog
from gui.mesh_processing import MeshProcessingDialog
from gui.mesh_advanced_analysis import MeshAdvancedAnalysisDialog
from gui.plane_definition_choice import PlaneDefinitionChoiceDialog
from gui.plane_by_points import PlaneByPointsDialog
from gui.plane_canonical import PlaneCanonicalDialog
from gui.crop_slice_mesh import CropSliceMeshDialog
from PySide6.QtGui import QIcon, QColor
from PySide6.QtWidgets import QColorDialog
import os

def run_app():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Deformation Viewer 3D")
        self.setGeometry(100, 100, 1000, 700)
        self._init_ui()
        # Add slab thickness control (hidden by default)
        self._init_slab_thickness_control()

    def _init_ui(self):
        # Central widget: MeshViewerWidget
        self.mesh_viewer = MeshViewerWidget()
        self.setCentralWidget(self.mesh_viewer)

        # Toolbar
        toolbar = QToolBar("Main Toolbar")
        self.addToolBar(toolbar)
        icon_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "resources", "icons")
        def icon(name):
            return QIcon(os.path.join(icon_dir, name))
        load_action = toolbar.addAction(icon("load_mesh.svg"), "Load 3D Mesh")
        load_action.triggered.connect(self.load_mesh_dialog)
        toolbar.addAction(icon("load_texture.svg"), "Load Texture")
        self.action_define_plane = toolbar.addAction(icon("define_plane.svg"), "Define Plane")
        self.action_define_plane.triggered.connect(self.show_plane_definition_choice)
        toolbar.addAction(icon("set_slab_thickness.svg"), "Set Slab Thickness")
        self.action_show_heatmap = toolbar.addAction(icon("show_heatmap.svg"), "Show Deformation Heatmap")
        self.action_show_heatmap.triggered.connect(self.show_heatmap_clicked)
        toolbar.addAction(icon("export_csv.svg"), "Export Deformation CSV")
        toolbar.addAction(icon("save_screenshot.svg"), "Save Screenshot")
        toolbar.addAction(icon("reset_picking.svg"), "Reset Picking")
        toolbar.addSeparator()
        toolbar.addAction(icon("flip_direction.svg"), "Flip Analysis Direction")
        toolbar.addAction(icon("select_colormap.svg"), "Select Colormap")
        mesh_display_action = toolbar.addAction(icon("mesh_display_mode.svg"), "Mesh Display Mode")
        mesh_display_action.triggered.connect(self.show_mesh_display_mode_dialog)
        process_mesh_action = toolbar.addAction(icon("process_mesh.svg"), "Process Mesh")
        process_mesh_action.triggered.connect(self.show_mesh_processing_dialog)
        adv_analysis_action = toolbar.addAction(icon("advanced_analysis.svg"), "Advanced Analysis")
        adv_analysis_action.triggered.connect(self.show_advanced_analysis_dialog)
        # Add Crop/Slice Mesh button
        crop_box_action = toolbar.addAction(icon("crop_slice_mesh.svg"), "Crop Mesh (Box)")
        crop_box_action.setToolTip("Interactively crop the mesh using a box")
        crop_box_action.triggered.connect(self.show_crop_box_dialog)
        # Add Save Cropped Mesh button
        save_cropped_action = toolbar.addAction(icon("export_mesh.svg"), "Save Cropped Mesh")
        save_cropped_action.setToolTip("Save the currently displayed (cropped) mesh to file")
        save_cropped_action.triggered.connect(self.save_cropped_mesh)
        toolbar.addAction(icon("slice_with_plane.svg"), "Slice with Plane")
        toolbar.addAction(icon("help_about.svg"), "Help/About")
        toolbar.addAction(icon("reset_view.svg"), "Reset View")
        toolbar.addAction(icon("export_mesh.svg"), "Export Mesh")
        toolbar.addAction(icon("batch_process.svg"), "Batch Process")
        toolbar.addAction(icon("undo.svg"), "Undo")
        toolbar.addAction(icon("redo.svg"), "Redo")
        toolbar.addAction(icon("save_session.svg"), "Save Session")
        toolbar.addAction(icon("load_session.svg"), "Load Session")

        # Sidebar (dock widget with tabs)
        self.sidebar = QDockWidget("Tools & Info", self)
        self.sidebar.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)
        self.tab_widget = QTabWidget()
        # Annotation tab
        annotation_tab = QWidget()
        ann_layout = QVBoxLayout(annotation_tab)
        ann_layout.addWidget(QLabel("Annotations:"))
        ann_layout.addWidget(QListWidget())
        btns_layout = QHBoxLayout()
        btns_layout.addWidget(QPushButton("Add Point"))
        btns_layout.addWidget(QPushButton("Add Line"))
        btns_layout.addWidget(QPushButton("Add Text"))
        ann_layout.addLayout(btns_layout)
        ann_layout.addWidget(QPushButton("Export Annotations"))
        self.tab_widget.addTab(annotation_tab, "Annotations")
        # Mesh info tab
        info_tab = QWidget()
        info_layout = QVBoxLayout(info_tab)
        info_layout.addWidget(QLabel("Mesh Info:"))
        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        info_layout.addWidget(self.info_text)
        self.tab_widget.addTab(info_tab, "Mesh Info")
        # History/session tab
        history_tab = QWidget()
        hist_layout = QVBoxLayout(history_tab)
        hist_layout.addWidget(QLabel("Session History:"))
        hist_layout.addWidget(QListWidget())
        self.tab_widget.addTab(history_tab, "History")
        self.sidebar.setWidget(self.tab_widget)
        self.addDockWidget(Qt.RightDockWidgetArea, self.sidebar)

        # --- Left sidebar for view controls ---
        self.view_toolbar = QToolBar("View Controls")
        self.view_toolbar.setOrientation(Qt.Vertical)
        self.addToolBar(Qt.LeftToolBarArea, self.view_toolbar)
        self.action_proj_toggle = self.view_toolbar.addAction(icon("square.svg"), "Perspective/Ortho")
        self.action_proj_toggle.triggered.connect(self.toggle_projection_mode)
        self.action_view_top = self.view_toolbar.addAction(icon("arrow-up.svg"), "Top")
        self.action_view_top.triggered.connect(lambda: self.set_standard_view('top'))
        self.action_view_bottom = self.view_toolbar.addAction(icon("arrow-down.svg"), "Bottom")
        self.action_view_bottom.triggered.connect(lambda: self.set_standard_view('bottom'))
        self.action_view_left = self.view_toolbar.addAction(icon("arrow-left.svg"), "Left")
        self.action_view_left.triggered.connect(lambda: self.set_standard_view('left'))
        self.action_view_right = self.view_toolbar.addAction(icon("arrow-right.svg"), "Right")
        self.action_view_right.triggered.connect(lambda: self.set_standard_view('right'))
        self.action_view_front = self.view_toolbar.addAction(icon("corner-down-right.svg"), "Front")
        self.action_view_front.triggered.connect(lambda: self.set_standard_view('front'))
        self.action_view_back = self.view_toolbar.addAction(icon("corner-up-left.svg"), "Back")
        self.action_view_back.triggered.connect(lambda: self.set_standard_view('back'))
        self.action_zoom_all = self.view_toolbar.addAction(icon("maximize-2.svg"), "Zoom All")
        self.action_zoom_all.triggered.connect(self.zoom_all)
        self.action_zoom_window = self.view_toolbar.addAction(icon("search.svg"), "Zoom Window")
        self.action_zoom_window.triggered.connect(self.zoom_window)

        # --- Configuration menu ---
        menubar = self.menuBar()
        config_menu = menubar.addMenu("Configuration")
        self.action_show_sidebar = config_menu.addAction("Show Sidebar")
        self.action_show_sidebar.setCheckable(True)
        self.action_show_sidebar.setChecked(True)
        self.action_show_sidebar.triggered.connect(self.toggle_sidebar)
        self.action_show_left_toolbar = config_menu.addAction("Show Left Toolbar")
        self.action_show_left_toolbar.setCheckable(True)
        self.action_show_left_toolbar.setChecked(True)
        self.action_show_left_toolbar.triggered.connect(self.toggle_left_toolbar)
        self.action_set_bg_color = config_menu.addAction("Set Background Color...")
        self.action_set_bg_color.triggered.connect(self.pick_background_color)

    def toggle_sidebar(self, checked=None):
        if checked is None:
            checked = self.action_show_sidebar.isChecked()
        self.sidebar.setVisible(checked)
        self.action_show_sidebar.setChecked(self.sidebar.isVisible())

    def toggle_left_toolbar(self, checked=None):
        if checked is None:
            checked = self.action_show_left_toolbar.isChecked()
        self.view_toolbar.setVisible(checked)
        self.action_show_left_toolbar.setChecked(self.view_toolbar.isVisible())

    def pick_background_color(self):
        color = QColorDialog.getColor(QColor('#888888'), self, "Select 3D Background Color")
        if color.isValid():
            hex_color = color.name()
            self.mesh_viewer.plotter.set_background(hex_color)
            self.mesh_viewer.plotter.render()

    def load_mesh_dialog(self):
        filters = (
            "3D Mesh Files (*.stl *.obj *.ply *.vtk *.vtp *.off *.gltf *.glb *.3ds *.dae *.fbx *.x *.wrl *.mesh *.msh *.ctm *.gts *.usd *.usda *.usdc *.usdz);;"
            "All Files (*)"
        )
        last_dir = os.path.expanduser("~")
        file_path, _ = QFileDialog.getOpenFileName(self, "Open 3D Mesh", last_dir, filters)
        if file_path:
            try:
                self.mesh_viewer.load_mesh(file_path)
                self.update_mesh_info()
            except Exception as e:
                QMessageBox.critical(self, "Load Error", str(e))

    def update_mesh_info(self):
        mesh = self.mesh_viewer.mesh
        if mesh is None:
            self.info_text.setPlainText("No mesh loaded.")
            return
        info = []
        info.append(f"Type: {type(mesh).__name__}")
        info.append(f"Points: {mesh.n_points}")
        info.append(f"Cells: {mesh.n_cells}")
        if hasattr(mesh, 'faces') and mesh.faces is not None:
            info.append(f"Faces: {len(mesh.faces) // 4 if mesh.faces is not None else 'N/A'}")
        if hasattr(mesh, 'n_faces'):
            info.append(f"n_faces: {mesh.n_faces}")
        if hasattr(mesh, 'n_edges'):
            info.append(f"n_edges: {mesh.n_edges}")
        if hasattr(mesh, 'area'):
            info.append(f"Area: {getattr(mesh, 'area', 'N/A'):.4f}")
        if hasattr(mesh, 'volume'):
            info.append(f"Volume: {getattr(mesh, 'volume', 'N/A'):.4f}")
        info.append(f"Bounds: {mesh.bounds}")
        info.append(f"Arrays: {list(mesh.point_data.keys())}")
        self.info_text.setPlainText("\n".join(info))

    def toggle_projection_mode(self):
        plotter = self.mesh_viewer.plotter
        if plotter.camera.GetParallelProjection():
            plotter.camera.ParallelProjectionOff()
        else:
            plotter.camera.ParallelProjectionOn()
        plotter.render()

    def set_standard_view(self, view):
        plotter = self.mesh_viewer.plotter
        views = {
            'top': (0, 0, 1),
            'bottom': (0, 0, -1),
            'left': (-1, 0, 0),
            'right': (1, 0, 0),
            'front': (0, 1, 0),
            'back': (0, -1, 0),
        }
        if view in views:
            plotter.view_vector(views[view])
            plotter.reset_camera()
            plotter.render()

    def zoom_all(self):
        self.mesh_viewer.plotter.reset_camera()
        self.mesh_viewer.plotter.render()

    def zoom_window(self):
        # Use the correct PyVistaQt method for rubber band zoom
        self.mesh_viewer.plotter.enable_rubber_band_2d_style()
        self.mesh_viewer.plotter.render()

    def show_mesh_display_mode_dialog(self):
        current_mode = getattr(self.mesh_viewer, 'display_mode', 'surface')
        dialog = MeshDisplayModeDialog(current_mode, self)
        from PySide6.QtWidgets import QDialog
        if dialog.exec() == QDialog.Accepted:
            mode = dialog.selected_mode()
            self.mesh_viewer.set_display_mode(mode)

    def show_mesh_processing_dialog(self):
        if self.mesh_viewer.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        dialog = MeshProcessingDialog(self)
        if dialog.exec():
            params = dialog.get_selected_filters()
            self.process_mesh(params)

    def process_mesh(self, params):
        mesh = self.mesh_viewer.mesh
        import mesh_utils
        from PySide6.QtWidgets import QProgressDialog
        steps = [
            ('clean', params['clean']),
            ('components', params['components']),
            ('holes', params['holes']),
            ('smooth', params['smooth']),
            ('decimate', params['decimate']),
            ('remesh', params['remesh'])
        ]
        total = sum(1 for _, enabled in steps if enabled)
        progress = QProgressDialog("Processing mesh...", None, 0, total, self)
        progress.setWindowTitle("Mesh Processing")
        progress.setWindowModality(Qt.WindowModal)
        progress.setValue(0)
        step = 0
        # Clean
        if params['clean']:
            mesh = mesh_utils.clean_mesh(mesh)
            step += 1
            progress.setValue(step)
        # Remove small components
        if params['components']:
            mesh = mesh_utils.remove_small_components(mesh)
            step += 1
            progress.setValue(step)
        # Fill small holes
        if params['holes']:
            mesh = mesh_utils.fill_small_holes(mesh, max_area=params['hole_size'])
            step += 1
            progress.setValue(step)
        # Smooth
        if params['smooth']:
            mesh = mesh_utils.smooth_mesh(mesh, n_iter=params['smooth_iter'], relaxation=params['smooth_relax'])
            step += 1
            progress.setValue(step)
        # Decimate
        if params['decimate']:
            reduction = params['decimate_target'] / 100.0
            try:
                mesh = mesh.decimate(target_reduction=1.0 - reduction)
            except TypeError:
                mesh = mesh.decimate(1.0 - reduction)
            step += 1
            progress.setValue(step)
        # Remesh (if available)
        if params['remesh'] and hasattr(mesh, 'remesh'):
            try:
                mesh = mesh.remesh(params['remesh_size'])
            except Exception:
                QMessageBox.warning(self, "Remesh Error", "Remeshing not supported for this mesh or PyVista version.")
            step += 1
            progress.setValue(step)
        progress.setValue(total)
        self.mesh_viewer.mesh = mesh
        self.mesh_viewer.set_display_mode(getattr(self.mesh_viewer, 'display_mode', 'surface'))
        self.update_mesh_info()

    def show_advanced_analysis_dialog(self):
        if self.mesh_viewer.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        dialog = MeshAdvancedAnalysisDialog(self)
        if dialog.exec():
            params = dialog.get_selected_analyses()
            self.run_advanced_analysis(params)

    def run_advanced_analysis(self, params):
        mesh = self.mesh_viewer.mesh
        import numpy as np
        import pyvista as pv
        results = []
        # Mean Curvature
        if params.get('mean_curvature'):
            try:
                scalars = mesh.curvature(curv_type='mean')
                mesh.point_data['MeanCurvature'] = scalars
                self.mesh_viewer.set_colormap('MeanCurvature')
                results.append('Mean Curvature')
            except Exception as e:
                QMessageBox.critical(self, "Curvature Error", f"Mean: {e}")
        # Gaussian Curvature
        if params.get('gaussian_curvature'):
            try:
                scalars = mesh.curvature(curv_type='gaussian')
                mesh.point_data['GaussianCurvature'] = scalars
                self.mesh_viewer.set_colormap('GaussianCurvature')
                results.append('Gaussian Curvature')
            except Exception as e:
                QMessageBox.critical(self, "Curvature Error", f"Gaussian: {e}")
        # Thickness
        if params.get('thickness'):
            try:
                normals = mesh.point_normals
                points = mesh.points
                ray_length = params.get('thickness_ray_length', 100.0)
                thickness = np.zeros(points.shape[0])
                for i, (p, n) in enumerate(zip(points, normals)):
                    hits = mesh.ray_trace(p, p + n * ray_length, first_point=False)[0]
                    if len(hits) > 0:
                        thickness[i] = np.linalg.norm(hits[0] - p)
                    else:
                        thickness[i] = 0
                mesh.point_data['Thickness'] = thickness
                self.mesh_viewer.set_colormap('Thickness')
                results.append('Thickness')
            except Exception as e:
                QMessageBox.critical(self, "Thickness Error", str(e))
        # Geodesic Distance
        if params.get('geodesic'):
            try:
                source = params.get('geodesic_source', 0)
                dists = mesh.geodesic_distance(source)
                mesh.point_data['GeodesicDist'] = dists
                self.mesh_viewer.set_colormap('GeodesicDist')
                results.append('Geodesic Distance')
            except Exception as e:
                QMessageBox.critical(self, "Geodesic Error", str(e))
        # Custom Scalar Field
        if params.get('custom'):
            try:
                mesh.point_data['RandomField'] = np.random.rand(mesh.n_points)
                self.mesh_viewer.set_colormap('RandomField')
                results.append('Custom Scalar Field')
            except Exception as e:
                QMessageBox.critical(self, "Custom Field Error", str(e))
        if results:
            QMessageBox.information(self, "Analysis Complete", f"Computed: {', '.join(results)}. Showing last result.")
        self.update_mesh_info()

    def show_plane_definition_choice(self):
        if self.mesh_viewer.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        dialog = PlaneDefinitionChoiceDialog(self)
        result = dialog.exec()
        if result == 1:
            self.show_plane_by_points()
        elif result == 2:
            self.show_plane_canonical()

    def show_plane_by_points(self):
        # Pass the current outside_opacity to the dialog
        outside_opacity = getattr(self, '_outside_opacity', 0.18)
        dialog = PlaneByPointsDialog(self.mesh_viewer, self, outside_opacity=outside_opacity)
        def on_done():
            # Only store the parameters, do NOT run analysis automatically
            plane_params = getattr(dialog, 'plane_params', None)
            slab_thickness = getattr(dialog, 'slab_thickness', None)
            if plane_params is not None and slab_thickness is not None:
                # Store for later use (e.g., as attributes)
                self._last_plane_params = plane_params
                self._last_slab_thickness = slab_thickness
            # Disconnect the slider from the dialog after closing
            try:
                self.opacity_spin.valueChanged.disconnect(dialog.set_outside_opacity)
            except Exception:
                pass
            dialog.deleteLater()
        dialog.finished.connect(on_done)
        # Connect the opacity slider to the dialog's set_outside_opacity method
        self.opacity_spin.valueChanged.connect(dialog.set_outside_opacity)
        dialog.show()
        # Show slab thickness controls as soon as plane is defined
        self.slab_control_frame.setVisible(True)

    def show_plane_canonical(self):
        dialog = PlaneCanonicalDialog(self.mesh_viewer, self)
        def on_done():
            # Only store the parameters, do NOT run analysis automatically
            plane_params = getattr(dialog, 'plane_params', None)
            slab_thickness = getattr(dialog, 'slab_thickness', None)
            if plane_params is not None and slab_thickness is not None:
                self._last_plane_params = plane_params
                self._last_slab_thickness = slab_thickness
            dialog.deleteLater()
        dialog.finished.connect(on_done)
        dialog.show()

    def run_deformation_analysis(self, plane_params, slab_thickness):
        # plane_params: dict with 'origin' and 'normal' (numpy arrays)
        # slab_thickness: float
        mesh = self.mesh_viewer.mesh
        if mesh is None:
            QMessageBox.warning(self, "No Mesh", "Please load a mesh first.")
            return
        import numpy as np
        points = mesh.points
        origin = np.array(plane_params['origin'])
        normal = np.array(plane_params['normal']) / np.linalg.norm(plane_params['normal'])
        distances = np.dot(points - origin, normal)
        # Mask for points within slab
        mask = np.abs(distances) <= (slab_thickness / 2)
        mesh.point_data['Deformation'] = distances
        deformation_masked = distances.copy()
        deformation_masked[~mask] = np.nan
        mesh.point_data['DeformationMasked'] = deformation_masked
        # Set color for outside-slab region (nan_color)
        prev_mask = getattr(self, '_last_deformation_mask', None)
        self.mesh_viewer.set_colormap('DeformationMasked', nan_color="#e0e0e0", nan_opacity=getattr(self, '_outside_opacity', 0.15))
        # Always update colorbar here, not in _on_slab_thickness_changed
        try:
            self.mesh_viewer.plotter.remove_scalar_bar()
        except Exception:
            pass
        self.mesh_viewer.plotter.render()  # Ensure mesh is rendered before adding colorbar
        self.mesh_viewer.plotter.add_scalar_bar(title="Deformation (m)", vertical=True, fmt="%.3f", interactive=False, position_x=0.88)
        self._last_deformation_mask = mask.copy()
        self.mesh_viewer.plotter.render()
        self.statusBar().showMessage("Deformation heatmap computed and displayed.", 4000)
        self.update_mesh_info()

    def _init_slab_thickness_control(self):
        # Add slab thickness slider/spinbox below the 3D viewer, initially hidden
        self.slab_control_frame = QFrame(self)
        self.slab_control_frame.setFrameShape(QFrame.StyledPanel)
        self.slab_control_frame.setVisible(False)
        layout = QHBoxLayout(self.slab_control_frame)
        layout.setContentsMargins(8, 2, 8, 2)
        layout.addWidget(QLabel("Slab Thickness:"))
        self.slab_slider = QSlider(Qt.Horizontal)
        self.slab_slider.setMinimum(1)
        self.slab_slider.setMaximum(200)
        self.slab_slider.setValue(20)
        self.slab_slider.setSingleStep(1)
        self.slab_slider.setTickInterval(10)
        self.slab_slider.setTickPosition(QSlider.TicksBelow)
        layout.addWidget(self.slab_slider)
        self.slab_spin = QDoubleSpinBox()
        self.slab_spin.setRange(0.001, 10.0)
        self.slab_spin.setDecimals(3)
        self.slab_spin.setSingleStep(0.001)
        self.slab_spin.setValue(0.20)
        self.slab_spin.setSuffix(" m")
        layout.addWidget(self.slab_spin)
        # --- Add outside opacity control ---
        layout.addWidget(QLabel("Outside Opacity:"))
        self.opacity_slider = QSlider(Qt.Horizontal)
        self.opacity_slider.setMinimum(0)
        self.opacity_slider.setMaximum(100)
        self.opacity_slider.setValue(15)
        self.opacity_slider.setSingleStep(1)
        self.opacity_spin = QDoubleSpinBox()
        self.opacity_spin.setRange(0.0, 1.0)
        self.opacity_spin.setDecimals(2)
        self.opacity_spin.setSingleStep(0.01)
        self.opacity_spin.setValue(0.15)
        layout.addWidget(self.opacity_slider)
        layout.addWidget(self.opacity_spin)
        # Sync slider and spinbox
        self.opacity_slider.valueChanged.connect(lambda v: self.opacity_spin.setValue(v/100.0))
        self.opacity_spin.valueChanged.connect(lambda v: self.opacity_slider.setValue(int(v*100)))
        self.opacity_spin.valueChanged.connect(self._on_opacity_changed)
        self.slab_slider.valueChanged.connect(lambda v: self.slab_spin.setValue(v/100.0))
        self.slab_spin.valueChanged.connect(lambda v: self.slab_slider.setValue(int(v*100)))
        self.slab_spin.valueChanged.connect(self._on_slab_thickness_changed)
        # Add below central widget
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0,0,0,0)
        main_layout.setSpacing(0)
        main_layout.addWidget(self.mesh_viewer)
        main_layout.addWidget(self.slab_control_frame)
        container = QWidget()
        container.setLayout(main_layout)
        self.setCentralWidget(container)

    def _on_opacity_changed(self, value):
        self._outside_opacity = float(value)
        # Live update the heatmap if active
        if hasattr(self, '_last_plane_params') and self.mesh_viewer.mesh is not None:
            self._on_slab_thickness_changed(self.slab_spin.value())

    def _on_slab_thickness_changed(self, value):
        # Live update the heatmap when slab thickness changes
        if not hasattr(self, '_last_plane_params') or self.mesh_viewer.mesh is None:
            return
        import numpy as np
        mesh = self.mesh_viewer.mesh
        plane_params = self._last_plane_params
        slab_thickness = float(value)
        points = mesh.points
        origin = np.array(plane_params['origin'])
        normal = np.array(plane_params['normal']) / np.linalg.norm(plane_params['normal'])
        distances = np.dot(points - origin, normal)
        mask = np.abs(distances) <= (slab_thickness / 2)
        mesh.point_data['Deformation'] = distances
        deformation_masked = distances.copy()
        deformation_masked[~mask] = np.nan
        mesh.point_data['DeformationMasked'] = deformation_masked
        self.mesh_viewer.set_colormap('DeformationMasked', nan_color="#e0e0e0", nan_opacity=getattr(self, '_outside_opacity', 0.15))
        self._last_slab_thickness = slab_thickness
        # Always update colorbar here to ensure it stays on the right
        try:
            self.mesh_viewer.plotter.remove_scalar_bar()
        except Exception:
            pass
        self.mesh_viewer.plotter.render()  # Ensure mesh is rendered before adding colorbar
        self.mesh_viewer.plotter.add_scalar_bar(title="Deformation (m)", vertical=True, fmt="%.3f", interactive=False, position_x=0.88)
        self.mesh_viewer.plotter.render()

    def show_heatmap_clicked(self):
        # Called when the user clicks the Show Deformation Heatmap button
        if not hasattr(self, '_last_plane_params') or not hasattr(self, '_last_slab_thickness'):
            QMessageBox.warning(self, "No Plane Defined", "Please define a plane and slab thickness first.")
            return
        if self._last_plane_params is None or self._last_slab_thickness is None:
            QMessageBox.warning(self, "No Plane Defined", "Please define a plane and slab thickness first.")
            return
        self.run_deformation_analysis(self._last_plane_params, self._last_slab_thickness)

    def show_crop_box_dialog(self):
        if self.mesh_viewer.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        from gui.crop_box_dialog import CropBoxDialog
        dialog = CropBoxDialog(self.mesh_viewer, parent=self)
        dialog.show()  # Use show() for non-modal, non-blocking dialog

    def save_cropped_mesh(self):
        from gui.save_cropped_mesh import save_cropped_mesh
        mesh = getattr(self.mesh_viewer, 'mesh', None)
        save_cropped_mesh(mesh, parent=self)

run_app()
