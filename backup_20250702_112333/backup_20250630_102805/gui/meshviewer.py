from PySide6.QtWidgets import QWidget, QVBoxLayout
from pyvistaqt import QtInteractor
import pyvista as pv
import numpy as np
from scipy.spatial import cKDTree

class MeshViewerWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        layout = QVBoxLayout(self)
        self.plotter = QtInteractor(self)
        self.plotter.set_background('#888888')  # Medium gray background
        # Add a default light for better shading
        import pyvista as pv
        light = pv.Light()
        light.set_direction_angle(45, -45)  # Overhead, angled
        light.intensity = 1.0
        light.positional = False
        self.plotter.renderer.AddLight(light)
        layout.addWidget(self.plotter.interactor)
        self.mesh = None
        self.display_mode = "surface"

    def transfer_vertex_colors(self, src_mesh, dst_mesh, color_key):
        """Transfer per-vertex colors from src_mesh to dst_mesh using nearest neighbor mapping."""
        src_points = np.asarray(src_mesh.points)
        dst_points = np.asarray(dst_mesh.points)
        src_colors = np.asarray(src_mesh.point_data[color_key])
        tree = cKDTree(src_points)
        dists, idxs = tree.query(dst_points)
        return src_colors[idxs]

    def set_display_mode(self, mode, reset_camera=True):
        """Switch mesh visualization mode: surface, surface+edge, wireframe, points."""
        if self.mesh is None:
            return
        self.plotter.clear()
        mesh = self.mesh
        # Try to recover color arrays from original mesh if missing after processing
        color_keys = [k for k in mesh.point_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "vertex_colors")]
        cell_color_keys = [k for k in mesh.cell_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "face_colors")]
        # Advanced: If no color arrays, but original mesh exists and has them, transfer using nearest neighbor
        if not color_keys and hasattr(self, 'original_mesh'):
            orig = self.original_mesh
            orig_color_keys = [k for k in orig.point_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "vertex_colors")]
            if orig_color_keys:
                # Only transfer if original has colors and processed mesh has points
                try:
                    mesh.point_data[orig_color_keys[0]] = self.transfer_vertex_colors(orig, mesh, orig_color_keys[0])
                    color_keys = [orig_color_keys[0]]
                except Exception:
                    pass
        if not cell_color_keys and hasattr(self, 'original_mesh'):
            orig = self.original_mesh
            orig_cell_color_keys = [k for k in orig.cell_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "face_colors")]
            if orig_cell_color_keys and len(orig.cell_data[orig_cell_color_keys[0]]) == mesh.n_cells:
                mesh.cell_data[orig_cell_color_keys[0]] = orig.cell_data[orig_cell_color_keys[0]]
                cell_color_keys = [orig_cell_color_keys[0]]
        kwargs = {}
        if color_keys:
            kwargs = {"scalars": color_keys[0], "rgb": True}
        elif cell_color_keys:
            kwargs = {"scalars": cell_color_keys[0], "rgb": True}
        elif hasattr(mesh, 'active_scalars_info') and mesh.active_scalars_info and mesh.active_scalars_info[1] == 'texture':
            kwargs = {"texture": mesh.active_scalars}
        elif hasattr(mesh, 'textures') and getattr(mesh, 'textures', None):
            tex = next(iter(mesh.textures.values())) if mesh.textures else None
            if tex is not None:
                kwargs = {"texture": tex}
        if mode == "surface":
            self.plotter.add_mesh(mesh, show_edges=False, **kwargs)
        elif mode == "surface+edge":
            self.plotter.add_mesh(mesh, show_edges=True, **kwargs)
        elif mode == "wireframe":
            self.plotter.add_mesh(mesh, style="wireframe", color="black")
        elif mode == "points":
            self.plotter.add_mesh(mesh, style="points", render_points_as_spheres=True, point_size=6, color="red")
        if reset_camera:
            self.plotter.reset_camera()
        self.plotter.render()
        self.display_mode = mode

    def load_mesh(self, filename):
        try:
            import pyvista as pv
            mesh = pv.read(filename)
            # --- Try to auto-detect and set RGB colors if present ---
            color_keys = [k for k in mesh.point_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "vertex_colors")]
            if color_keys:
                arr = mesh.point_data[color_keys[0]]
                import numpy as np
                arr = np.asarray(arr)
                # If array is float, convert to uint8 (assuming 0-1 range)
                if arr.dtype in (np.float32, np.float64):
                    arr = (arr * 255).clip(0, 255).astype(np.uint8)
                if arr.dtype != np.uint8:
                    arr = arr.astype(np.uint8)
                if arr.ndim == 2 and arr.shape[1] >= 3:
                    mesh.point_data.clear()
                    mesh.point_data["RGB"] = arr[:, :3]
            self.mesh = mesh
            self.original_mesh = mesh.copy(deep=True)  # Save original for color recovery
            self.set_display_mode(getattr(self, 'display_mode', 'surface'))
            # --- Patch: After loading, print debug info about color arrays ---
            color_keys = [k for k in mesh.point_data.keys()]
            print(f"[DEBUG] Color arrays after loading: {color_keys}")
            if "RGB" in mesh.point_data:
                arr = mesh.point_data["RGB"]
                print(f"[DEBUG] 'RGB' array dtype: {arr.dtype}, shape: {arr.shape}, min: {arr.min()}, max: {arr.max()}")
        except Exception as e:
            raise RuntimeError(f"Failed to load mesh: {e}")

    def set_colormap(self, array_name, nan_color=None, nan_opacity=0.15):
        """Display the mesh colored by the given scalar array (point_data)."""
        if self.mesh is None:
            return
        self.plotter.clear()
        try:
            self.plotter.remove_scalar_bar()
        except Exception:
            pass
        mesh = self.mesh
        normals = mesh.point_data.get('Normals', None)
        if normals is None or (hasattr(mesh, 'n_points') and (not hasattr(normals, 'shape') or normals.shape[0] != mesh.n_points)):
            mesh.compute_normals(inplace=True)
        if array_name in mesh.point_data:
            if nan_color is None:
                nan_color = "#b0b0b0"
            scalars = mesh.point_data[array_name]
            # --- Robust per-point opacity fade logic ---
            use_per_point_opacity = False
            opacity = 1.0
            if (
                isinstance(mesh, pv.PolyData)
                and hasattr(mesh, 'n_points')
                and isinstance(scalars, np.ndarray)
                and scalars.ndim == 1
                and scalars.shape[0] == mesh.n_points
            ):
                # Per-point opacity supported
                opacity = np.ones(mesh.n_points, dtype=float)
                safe_opacity = nan_opacity if nan_opacity > 0 else 0.05
                mask = np.isnan(scalars)
                opacity[mask] = safe_opacity
                use_per_point_opacity = True
            # Compose kwargs
            kwargs = dict(
                scalars=array_name,
                cmap="viridis",
                show_edges=False,
                smooth_shading=True,
                nan_color=nan_color,
                ambient=0.35,
                specular=0.45,
                specular_power=18,
                show_scalar_bar=False,
            )
            if use_per_point_opacity:
                kwargs["opacity"] = opacity
            else:
                kwargs["opacity"] = 1.0  # fallback: fully opaque
            self.plotter.add_mesh(mesh, **kwargs)
        else:
            # --- Patch: Always try to transfer per-vertex color from original mesh if missing ---
            # This ensures that after cropping or any operation, colors are restored if possible
            color_keys = [k for k in mesh.point_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "vertex_colors")]
            if not color_keys and hasattr(self, 'original_mesh'):
                orig = self.original_mesh
                orig_color_keys = [k for k in orig.point_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "vertex_colors")]
                if orig_color_keys:
                    try:
                        mesh.point_data[orig_color_keys[0]] = self.transfer_vertex_colors(orig, mesh, orig_color_keys[0])
                        color_keys = [orig_color_keys[0]]
                        print(f"[DEBUG] Transferred color array '{orig_color_keys[0]}' to mesh, shape: {mesh.point_data[orig_color_keys[0]].shape}")
                    except Exception as e:
                        print(f"[DEBUG] Failed to transfer color array: {e}")
            self.set_display_mode('surface', reset_camera=False)
            return
        self.plotter.render()

    def set_mesh(self, mesh):
        import pyvista as pv
        # Always convert UnstructuredGrid to PolyData for downstream compatibility
        if isinstance(mesh, pv.UnstructuredGrid):
            mesh = mesh.extract_surface().triangulate()
        elif isinstance(mesh, pv.MultiBlock):
            # If a MultiBlock, try to extract the first PolyData block
            for block in mesh:
                if isinstance(block, pv.PolyData):
                    mesh = block
                    break
                elif isinstance(block, pv.UnstructuredGrid):
                    mesh = block.extract_surface().triangulate()
                    break
        self.mesh = mesh
        self.set_display_mode(getattr(self, 'display_mode', 'surface'))
        # --- Patch: Always ensure RGB is present and correct before saving or after color transfer ---
        # If a color array is present but not named 'RGB', or is not uint8, normalize and rename
        color_keys = [k for k in mesh.point_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "vertex_colors")]
        if color_keys:
            arr = np.asarray(mesh.point_data[color_keys[0]])
            if arr.dtype in (np.float32, np.float64):
                arr = (arr * 255).clip(0, 255).astype(np.uint8)
            if arr.dtype != np.uint8:
                arr = arr.astype(np.uint8)
            if arr.ndim == 2 and arr.shape[1] >= 3:
                mesh.point_data.clear()
                mesh.point_data["RGB"] = arr[:, :3]
                print(f"[DEBUG] Normalized and set color array as 'RGB', shape: {arr[:, :3].shape}")
        # --- Patch: Force RGB as active scalars for saving ---
        if "RGB" in mesh.point_data:
            mesh.active_scalars_name = "RGB"
            mesh.set_active_scalars("RGB")
            print(f"[DEBUG] Set 'RGB' as active scalars for saving.")
        # --- Patch: For PLY, force PyVista to write RGB as vertex colors ---
        # Remove _active_scalars_info patch, as it causes errors in recent PyVista versions
        # Only set active_scalars_name and set_active_scalars
        if "RGB" in mesh.point_data:
            try:
                mesh.active_scalars_name = "RGB"
                mesh.set_active_scalars("RGB")
                print(f"[DEBUG] Set 'RGB' as active scalars for saving.")
            except Exception as e:
                print(f"[DEBUG] Failed to set active scalars: {e}")
