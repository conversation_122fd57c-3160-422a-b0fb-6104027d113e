#!/usr/bin/env python3
"""
Backup Verification Script for DeformViz 3D v2.0 Stable
"""

import os
import sys
from pathlib import Path

def verify_backup():
    print("🔍 DeformViz 3D v2.0 Backup Verification")
    print("=" * 50)
    
    success = True
    
    # Check critical files
    critical_files = [
        "main.py",
        "requirements.txt", 
        "src/gui/mainwindow.py",
        "src/gui/analysis_wizard.py",
        "src/gui/meshviewer.py",
        "test_startup.py",
        "DEFORMVIZ_3D_USER_GUIDE.md"
    ]
    
    print("📁 Checking Critical Files...")
    for file_path in critical_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ Missing: {file_path}")
            success = False
    
    # Check directories
    critical_dirs = ["src", "src/gui", "src/core", "src/icons"]
    
    print("\n📂 Checking Directories...")
    for dir_path in critical_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ Missing directory: {dir_path}/")
            success = False
    
    # Test imports
    print("\n🧪 Testing Imports...")
    try:
        from src.gui.mainwindow import MainWindow
        print("✅ MainWindow import successful")
        
        from src.gui.analysis_wizard import AnalysisWizard  
        print("✅ AnalysisWizard import successful")
        
        from src.gui.meshviewer import MeshViewerWidget
        print("✅ MeshViewerWidget import successful")
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 BACKUP VERIFICATION SUCCESSFUL!")
        print("✅ All critical files present")
        print("✅ All imports working")
        print("✅ Backup is ready for use")
    else:
        print("❌ BACKUP VERIFICATION FAILED!")
    
    return success

if __name__ == "__main__":
    success = verify_backup()
    sys.exit(0 if success else 1)
