from PySide6.QtWidgets import Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QGroupBox, QFormLayout, QDoubleSpinBox, QMessageBox
from PySide6.QtCore import Qt
import numpy as np
import pyvista as pv

class CropSliceMeshDialog(QDialog):
    def __init__(self, mesh_viewer, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Crop/Slice Mesh")
        self.setMinimumWidth(400)
        self.mesh_viewer = mesh_viewer
        self.plotter = mesh_viewer.plotter
        self.mesh = mesh_viewer.mesh.copy() if hasattr(mesh_viewer, 'mesh') else None
        self._original_mesh = self.mesh.copy() if self.mesh is not None else None
        self._undo_stack = []
        self._redo_stack = []
        self._box_widget = None
        self._box_bounds = None
        self._box_actor = None
        self._last_box_params = None
        self._init_ui()
        self._init_box_widget()

    def _init_ui(self):
        layout = QVBoxLayout(self)
        layout.addWidget(QLabel("Use the box to select a region to crop or slice the mesh.\nAdjust translation, scale, and rotation as needed."))
        # Box transform controls
        self.transform_group = QGroupBox("Box Transform")
        form = QFormLayout(self.transform_group)
        # Translation
        self.trans_x = QDoubleSpinBox(); self.trans_x.setRange(-1000, 1000); self.trans_x.setDecimals(3)
        self.trans_y = QDoubleSpinBox(); self.trans_y.setRange(-1000, 1000); self.trans_y.setDecimals(3)
        self.trans_z = QDoubleSpinBox(); self.trans_z.setRange(-1000, 1000); self.trans_z.setDecimals(3)
        form.addRow("Translate X:", self.trans_x)
        form.addRow("Translate Y:", self.trans_y)
        form.addRow("Translate Z:", self.trans_z)
        # Scale
        self.scale_x = QDoubleSpinBox(); self.scale_x.setRange(0.01, 100); self.scale_x.setDecimals(3); self.scale_x.setValue(1.0)
        self.scale_y = QDoubleSpinBox(); self.scale_y.setRange(0.01, 100); self.scale_y.setDecimals(3); self.scale_y.setValue(1.0)
        self.scale_z = QDoubleSpinBox(); self.scale_z.setRange(0.01, 100); self.scale_z.setDecimals(3); self.scale_z.setValue(1.0)
        form.addRow("Scale X:", self.scale_x)
        form.addRow("Scale Y:", self.scale_y)
        form.addRow("Scale Z:", self.scale_z)
        # Rotation (degrees, axis-constrained)
        self.rot_x = QDoubleSpinBox(); self.rot_x.setRange(-180, 180); self.rot_x.setDecimals(2)
        self.rot_y = QDoubleSpinBox(); self.rot_y.setRange(-180, 180); self.rot_y.setDecimals(2)
        self.rot_z = QDoubleSpinBox(); self.rot_z.setRange(-180, 180); self.rot_z.setDecimals(2)
        form.addRow("Rotate X (deg):", self.rot_x)
        form.addRow("Rotate Y (deg):", self.rot_y)
        form.addRow("Rotate Z (deg):", self.rot_z)
        layout.addWidget(self.transform_group)
        # Info label
        self.info_label = QLabel("")
        layout.addWidget(self.info_label)
        # Buttons
        btns = QHBoxLayout()
        self.btn_apply = QPushButton("Apply Crop/Slice")
        self.btn_reset = QPushButton("Reset Box")
        self.btn_undo = QPushButton("Undo")
        self.btn_redo = QPushButton("Redo")
        self.btn_cancel = QPushButton("Cancel")
        btns.addWidget(self.btn_apply)
        btns.addWidget(self.btn_reset)
        btns.addWidget(self.btn_undo)
        btns.addWidget(self.btn_redo)
        btns.addWidget(self.btn_cancel)
        layout.addLayout(btns)
        # Connect signals
        self.btn_apply.clicked.connect(self.apply_crop)
        self.btn_reset.clicked.connect(self.reset_box)
        self.btn_undo.clicked.connect(self.undo)
        self.btn_redo.clicked.connect(self.redo)
        self.btn_cancel.clicked.connect(self.reject)
        for w in [self.trans_x, self.trans_y, self.trans_z, self.scale_x, self.scale_y, self.scale_z, self.rot_x, self.rot_y, self.rot_z]:
            w.valueChanged.connect(self.update_box_transform)

    def _init_box_widget(self):
        # Remove any previous box widget
        if self._box_widget:
            try:
                self.plotter.remove_box_widget()
            except Exception:
                pass
        # Estimate bounds from mesh
        if self.mesh is not None:
            bounds = np.array(self.mesh.bounds)
        else:
            bounds = np.array([-1, 1, -1, 1, -1, 1])
        self._box_bounds = bounds.copy()
        # Add box widget
        self._box_widget = self.plotter.add_box_widget(
            callback=self._on_box_updated,
            bounds=bounds,
            color="#00e6ff",
            outline_translation=True,
            use_planes=False,
            pass_widget=False,
            rotation_enabled=True
        )
        self._last_box_params = self._get_box_params()
        self.update_box_transform()
        self.update_info()

    def _on_box_updated(self, box):
        # Called when user moves box widget
        self._box_bounds = box.bounds
        self._last_box_params = self._get_box_params()
        self._update_box_actor()
        self.update_info()

    def _get_box_params(self):
        # Returns dict of box transform params
        return dict(
            trans_x=self.trans_x.value(),
            trans_y=self.trans_y.value(),
            trans_z=self.trans_z.value(),
            scale_x=self.scale_x.value(),
            scale_y=self.scale_y.value(),
            scale_z=self.scale_z.value(),
            rot_x=self.rot_x.value(),
            rot_y=self.rot_y.value(),
            rot_z=self.rot_z.value(),
            bounds=list(self._box_bounds) if self._box_bounds is not None else None
        )

    def update_box_transform(self):
        # Apply transform to box widget and preview actor
        if not self._box_widget:
            return
        # Get base bounds
        bounds = self._box_bounds if self._box_bounds is not None else np.array([-1,1,-1,1,-1,1])
        # Center
        cx = (bounds[0] + bounds[1]) / 2
        cy = (bounds[2] + bounds[3]) / 2
        cz = (bounds[4] + bounds[5]) / 2
        # Scale
        sx = self.scale_x.value()
        sy = self.scale_y.value()
        sz = self.scale_z.value()
        # Translation
        tx = self.trans_x.value()
        ty = self.trans_y.value()
        tz = self.trans_z.value()
        # Rotation
        rx = np.deg2rad(self.rot_x.value())
        ry = np.deg2rad(self.rot_y.value())
        rz = np.deg2rad(self.rot_z.value())
        # Build transform
        from scipy.spatial.transform import Rotation as R
        rotmat = R.from_euler('zyx', [rz, ry, rx]).as_matrix()
        # Box geometry
        box = pv.Box(bounds=[-0.5,0.5,-0.5,0.5,-0.5,0.5])
        box.points *= [sx, sy, sz]
        box.points = (rotmat @ box.points.T).T
        box.points += [cx+tx, cy+ty, cz+tz]
        self._box_actor = self.plotter.add_mesh(
            box, color="#00e6ff", opacity=0.2, name="crop_box_preview", reset_camera=False, show_edges=True, edge_color="#0080a0", line_width=2
        )
        self.plotter.render()
        self._last_box_params = self._get_box_params()
        self.update_info()

    def _update_box_actor(self):
        # Remove previous
        try:
            self.plotter.remove_actor("crop_box_preview")
        except Exception:
            pass
        self.update_box_transform()

    def update_info(self):
        if self.mesh is not None:
            npts = self.mesh.n_points
            ncells = self.mesh.n_cells
            self.info_label.setText(f"Mesh: {npts} points, {ncells} cells. Box bounds: {np.round(self._box_bounds, 3)}")
        else:
            self.info_label.setText("No mesh loaded.")

    def apply_crop(self):
        if self.mesh is None:
            QMessageBox.warning(self, "No Mesh", "No mesh loaded.")
            return
        # Save for undo
        self._undo_stack.append(self.mesh.copy())
        # Get the transformed box as previewed
        bounds = self._box_bounds if self._box_bounds is not None else self.mesh.bounds
        # Center
        cx = (bounds[0] + bounds[1]) / 2
        cy = (bounds[2] + bounds[3]) / 2
        cz = (bounds[4] + bounds[5]) / 2
        # Scale
        sx = self.scale_x.value()
        sy = self.scale_y.value()
        sz = self.scale_z.value()
        # Translation
        tx = self.trans_x.value()
        ty = self.trans_y.value()
        tz = self.trans_z.value()
        # Rotation
        rx = np.deg2rad(self.rot_x.value())
        ry = np.deg2rad(self.rot_y.value())
        rz = np.deg2rad(self.rot_z.value())
        from scipy.spatial.transform import Rotation as R
        rotmat = R.from_euler('zyx', [rz, ry, rx]).as_matrix()
        import pyvista as pv
        box = pv.Box(bounds=[-0.5,0.5,-0.5,0.5,-0.5,0.5])
        box.points *= [sx, sy, sz]
        box.points = (rotmat @ box.points.T).T
        box.points += [cx+tx, cy+ty, cz+tz]
        try:
            cropped = self.mesh.clip_box(box, invert=False)
            if cropped.n_points == 0:
                QMessageBox.warning(self, "Empty Crop", "Crop region contains no mesh points.")
                return
            # --- Transfer per-vertex color if missing ---
            color_keys = [k for k in self.mesh.point_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "vertex_colors")]
            for name in self.mesh.point_data:
                if name not in cropped.point_data:
                    cropped.point_data[name] = self.mesh.point_data[name][:cropped.n_points]
            # If cropped mesh is missing color, transfer from original using nearest neighbor
            if color_keys and all(k not in cropped.point_data for k in color_keys):
                try:
                    from scipy.spatial import cKDTree
                    src_points = np.asarray(self.mesh.points)
                    dst_points = np.asarray(cropped.points)
                    src_colors = np.asarray(self.mesh.point_data[color_keys[0]])
                    tree = cKDTree(src_points)
                    dists, idxs = tree.query(dst_points)
                    cropped.point_data[color_keys[0]] = src_colors[idxs]
                    print(f"[DEBUG] Transferred color array '{color_keys[0]}' to cropped mesh, shape: {cropped.point_data[color_keys[0]].shape}")
                except Exception as e:
                    print(f"[DEBUG] Failed to transfer color array: {e}")
            else:
                # Debug output
                for k in color_keys:
                    if k in cropped.point_data:
                        print(f"[DEBUG] Cropped mesh has color array '{k}', shape: {cropped.point_data[k].shape}")
            self.mesh = cropped
            self.mesh_viewer.set_mesh(self.mesh)
            self.update_info()
        except Exception as e:
            QMessageBox.critical(self, "Crop Error", str(e))
            return
        # Remove the crop box preview actor and widget after crop
        try:
            self.plotter.remove_actor("crop_box_preview")
        except Exception:
            pass
        try:
            self.plotter.remove_box_widget()
        except Exception:
            pass
        self._box_actor = None
        self._box_widget = None
        self.plotter.render()
        self.close()

    def reset_box(self):
        # Reset box widget and controls
        self.trans_x.setValue(0)
        self.trans_y.setValue(0)
        self.trans_z.setValue(0)
        self.scale_x.setValue(1.0)
        self.scale_y.setValue(1.0)
        self.scale_z.setValue(1.0)
        self.rot_x.setValue(0)
        self.rot_y.setValue(0)
        self.rot_z.setValue(0)
        self._init_box_widget()

    def undo(self):
        if not self._undo_stack:
            return
        self._redo_stack.append(self.mesh.copy())
        self.mesh = self._undo_stack.pop()
        self.mesh_viewer.set_mesh(self.mesh)
        self.update_info()

    def redo(self):
        if not self._redo_stack:
            return
        self._undo_stack.append(self.mesh.copy())
        self.mesh = self._redo_stack.pop()
        self.mesh_viewer.set_mesh(self.mesh)
        self.update_info()

    def closeEvent(self, event):
        # Remove box widget and preview actor
        try:
            self.plotter.remove_box_widget()
        except Exception:
            pass
        try:
            self.plotter.remove_actor("crop_box_preview")
        except Exception:
            pass
        self._box_actor = None
        self._box_widget = None
        self.plotter.render()
        super().closeEvent(event)
