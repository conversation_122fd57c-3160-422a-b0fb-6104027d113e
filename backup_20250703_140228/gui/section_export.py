# section_export.py
"""
Section extraction and DXF export utilities for 3D mesh viewer.
"""
import pyvista as pv
import numpy as np

def extract_section_curves(mesh, origin, normal):
    """
    Extract intersection polylines (section) between mesh and a plane.
    Args:
        mesh: pyvista.PolyData
        origin: (3,) array-like, point on the plane
        normal: (3,) array-like, normal vector of the plane
    Returns:
        section: pyvista.PolyData containing polylines (lines)
    """
    plane = pv.Plane(center=origin, direction=normal)
    section = mesh.slice(normal=normal, origin=origin)
    # section is PolyData with lines (polylines)
    return section

def export_section_to_dxf(section, filename):
    """
    Export section polylines (from PolyData) to a DXF file using ezdxf.
    Args:
        section: pyvista.PolyData containing polylines
        filename: output DXF file path
    """
    import ezdxf
    doc = ezdxf.new(dxfversion="R2010")
    msp = doc.modelspace()
    # Extract polylines from section
    lines = section.lines
    points = section.points
    i = 0
    while i < len(lines):
        npts = lines[i]
        polyline_pts = [points[j] for j in lines[i+1:i+1+npts]]
        msp.add_lwpolyline(polyline_pts, dxfattribs={"closed": False})
        i += npts + 1
    doc.saveas(filename)
