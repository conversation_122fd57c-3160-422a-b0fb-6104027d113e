from PySide6.QtWidgets import Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QRadioButton, QPushButton, QLabel, QCheckBox

class ScreenshotDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Save Screenshot")
        self.selected_mode = 'normal'
        layout = QVBoxLayout(self)
        layout.addWidget(QLabel("Choose screenshot quality:"))
        self.radio_normal = QRadioButton("Normal (window size)")
        self.radio_hd = QRadioButton("HD (3x window size)")
        self.radio_normal.setChecked(True)
        layout.addWidget(self.radio_normal)
        layout.addWidget(self.radio_hd)
        self.transparent_checkbox = QCheckBox("Transparent background")
        layout.addWidget(self.transparent_checkbox)
        btns = QHBoxLayout()
        self.ok_btn = QPushButton("OK")
        self.cancel_btn = QPushButton("Cancel")
        btns.addWidget(self.ok_btn)
        btns.addWidget(self.cancel_btn)
        layout.addLayout(btns)
        self.ok_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)

    def get_mode(self):
        if self.radio_hd.isChecked():
            return 'hd'
        return 'normal'

    def is_transparent(self):
        return self.transparent_checkbox.isChecked()
