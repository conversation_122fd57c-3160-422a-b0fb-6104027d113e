from PySide6.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QSlider, QDoubleSpinBox, QGroupBox, QFormLayout, QMessageBox, QRadioButton, QButtonGroup
from PySide6.QtCore import Qt
import numpy as np

class PlaneByPointsDialog(QDialog):
    def __init__(self, mesh_viewer, parent=None, outside_opacity=0.18):
        super().__init__(parent)
        self.setWindowTitle("Define Plane by 3 Points")
        self.setMinimumWidth(350)
        self.mesh_viewer = mesh_viewer
        self.outside_opacity = outside_opacity  # Store the initial opacity
        self.layout = QVBoxLayout(self)  # Store as self.layout to prevent GC
        layout = self.layout
        layout.addWidget(QLabel("Pick 3 points on the mesh to define the plane."))
        self.info_label = QLabel("Points picked: 0/3")
        layout.addWidget(self.info_label)
        self.btn_undo = QPushButton("Undo Last Point")
        self.btn_reset = QPushButton("Reset")
        btns = QHBoxLayout()
        btns.addWidget(self.btn_undo)
        btns.addWidget(self.btn_reset)
        layout.addLayout(btns)
        # Slab thickness controls (hidden until 3 points picked)
        self.slab_group = QGroupBox("Slab Thickness")
        slab_layout = QFormLayout(self.slab_group)
        self.slab_slider = QSlider(Qt.Horizontal)
        self.slab_slider.setRange(1, 200)
        self.slab_slider.setValue(20)
        self.slab_spin = QDoubleSpinBox()
        self.slab_spin.setRange(0.01, 10.0)
        self.slab_spin.setDecimals(3)
        self.slab_spin.setValue(0.20)
        self.slab_spin.setSuffix(" m")
        slab_layout.addRow("Thickness:", self.slab_spin)
        slab_layout.addRow("(Quick adjust):", self.slab_slider)
        self.slab_group.setVisible(False)
        layout.addWidget(self.slab_group)
        self.btn_apply = QPushButton("Apply")
        self.btn_apply.setEnabled(False)
        layout.addWidget(self.btn_apply)

        # --- Plane transform controls (hidden until 3 points picked) ---
        self.transform_group = QGroupBox("Plane Transform")
        transform_layout = QFormLayout(self.transform_group)
        # Translation controls
        self.trans_x = QDoubleSpinBox(); self.trans_x.setRange(-1000, 1000); self.trans_x.setDecimals(3)
        self.trans_y = QDoubleSpinBox(); self.trans_y.setRange(-1000, 1000); self.trans_y.setDecimals(3)
        self.trans_z = QDoubleSpinBox(); self.trans_z.setRange(-1000, 1000); self.trans_z.setDecimals(3)
        transform_layout.addRow("Translate X:", self.trans_x)
        transform_layout.addRow("Translate Y:", self.trans_y)
        transform_layout.addRow("Translate Z:", self.trans_z)
        # Rotation controls (degrees)
        self.rot_x = QDoubleSpinBox(); self.rot_x.setRange(-180, 180); self.rot_x.setDecimals(2)
        self.rot_y = QDoubleSpinBox(); self.rot_y.setRange(-180, 180); self.rot_y.setDecimals(2)
        self.rot_z = QDoubleSpinBox(); self.rot_z.setRange(-180, 180); self.rot_z.setDecimals(2)
        transform_layout.addRow("Rotate X (deg):", self.rot_x)
        transform_layout.addRow("Rotate Y (deg):", self.rot_y)
        transform_layout.addRow("Rotate Z (deg):", self.rot_z)
        self.transform_group.setVisible(False)
        layout.addWidget(self.transform_group)

        # --- Plane alignment options ---
        align_group = QGroupBox("Align Plane Normal To")
        align_layout = QHBoxLayout()
        self.align_keep = QRadioButton("Keep as is")
        self.align_x = QRadioButton("X (YZ plane)")
        self.align_y = QRadioButton("Y (ZX plane)")
        self.align_z = QRadioButton("Z (XY plane)")
        self.align_keep.setChecked(True)
        align_layout.addWidget(self.align_keep)
        align_layout.addWidget(self.align_x)
        align_layout.addWidget(self.align_y)
        align_layout.addWidget(self.align_z)
        align_group.setLayout(align_layout)
        align_group.setVisible(False)
        layout.addWidget(align_group)
        self.align_group = align_group

        # --- Slab position options ---
        slabpos_group = QGroupBox("Slab Position")
        slabpos_layout = QHBoxLayout()
        self.slab_centered = QRadioButton("Centered on plane")
        self.slab_positive = QRadioButton("Positive side")
        self.slab_negative = QRadioButton("Negative side")
        self.slab_centered.setChecked(True)
        slabpos_layout.addWidget(self.slab_centered)
        slabpos_layout.addWidget(self.slab_positive)
        slabpos_layout.addWidget(self.slab_negative)
        slabpos_group.setLayout(slabpos_layout)
        slabpos_group.setVisible(False)
        layout.addWidget(slabpos_group)
        self.slabpos_group = slabpos_group

        # Internal state
        self.picked_points = []
        self.plane_params = None
        self.slab_thickness = None
        # Connect signals
        self.btn_undo.clicked.connect(self.undo_point)
        self.btn_reset.clicked.connect(self.reset_points)
        self.btn_apply.clicked.connect(self.apply_and_accept)
        self.slab_spin.valueChanged.connect(self._on_slab_spin_changed)
        self.slab_slider.valueChanged.connect(self._on_slab_slider_changed)
        # Connect transform controls
        self.trans_x.valueChanged.connect(self.update_plane_preview)
        self.trans_y.valueChanged.connect(self.update_plane_preview)
        self.trans_z.valueChanged.connect(self.update_plane_preview)
        self.rot_x.valueChanged.connect(self.update_plane_preview)
        self.rot_y.valueChanged.connect(self.update_plane_preview)
        self.rot_z.valueChanged.connect(self.update_plane_preview)
        # Connect slab position radio buttons for live preview
        self.slab_centered.toggled.connect(self.update_plane_preview)
        self.slab_positive.toggled.connect(self.update_plane_preview)
        self.slab_negative.toggled.connect(self.update_plane_preview)
        # Connect plane alignment radio buttons for live preview
        self.align_keep.toggled.connect(self.update_plane_preview)
        self.align_x.toggled.connect(self.update_plane_preview)
        self.align_y.toggled.connect(self.update_plane_preview)
        self.align_z.toggled.connect(self.update_plane_preview)
        # Start picking
        self.start_point_picking()
        # self.setWindowModality(Qt.WindowModal)  # REMOVE or comment out this line to allow picking

    def start_point_picking(self):
        # Updated: use_picker=True instead of use_mesh (deprecation fix)
        self.mesh_viewer.plotter.enable_point_picking(
            callback=self.on_point_picked,
            use_picker=True,  # PyVista >=0.42.0
            show_message=False,  # Suppress the obsolete label
            show_point=True,
            left_clicking=True
        )
        self.update_info()

    def on_point_picked(self, *args, **kwargs):
        # Accepts variable args for compatibility with PyVista callback
        # Try to extract the picked point from args
        point = None
        # PyVista passes (picker, point) or (plotter, picker, point)
        for arg in args:
            if hasattr(arg, '__len__') and len(arg) == 3 and all(isinstance(x, (float, int)) for x in arg):
                point = arg
                break
        if point is None:
            # fallback: try kwargs
            point = kwargs.get('point', None)
        if point is None:
            return  # Could not extract point
        if len(self.picked_points) < 3:
            self.picked_points.append(point)
            self.update_info()
            self.update_plane_preview()  # Optional: live preview
            if len(self.picked_points) == 3:
                self.define_plane()

    def update_plane_preview(self):
        plotter = self.mesh_viewer.plotter
        # Remove previous plane preview
        if hasattr(self, '_plane_preview_actor') and self._plane_preview_actor:
            try:
                plotter.remove_actor(self._plane_preview_actor)
            except Exception:
                pass
            self._plane_preview_actor = None
        # Remove previous point preview actor
        if hasattr(self, '_point_preview_actor') and self._point_preview_actor:
            try:
                plotter.remove_actor(self._point_preview_actor)
            except Exception:
                pass
            self._point_preview_actor = None
        # Remove previous slab preview actor
        if hasattr(self, '_slab_preview_actor') and self._slab_preview_actor:
            try:
                plotter.remove_actor(self._slab_preview_actor)
            except Exception:
                pass
            self._slab_preview_actor = None
        # Add new point previews (yellow spheres using add_points)
        if self.picked_points:
            points = np.array(self.picked_points)
            self._point_preview_actor = plotter.add_points(
                points,
                color='yellow',
                point_size=18,
                render_points_as_spheres=True,
                name='point_preview',
                opacity=1.0,
                reset_camera=False
            )
        # If 3 points, show the plane with transform and slab region
        if len(self.picked_points) == 3 and self.plane_params is not None:
            import pyvista as pv
            origin = np.array(self.plane_params['origin'])
            normal = np.array(self.plane_params['normal'])
            # --- Apply alignment if selected ---
            # Align to axis: normal points along axis (perpendicular to plane)
            if self.align_x.isChecked():
                normal = np.array([1,0,0])  # Perpendicular to YZ
            elif self.align_y.isChecked():
                normal = np.array([0,1,0])  # Perpendicular to ZX
            elif self.align_z.isChecked():
                normal = np.array([0,0,1])  # Perpendicular to XY
            # Apply translation
            t = np.array([
                self.trans_x.value(),
                self.trans_y.value(),
                self.trans_z.value()
            ])
            center = origin + t
            # Apply rotation (Euler angles, degrees)
            rx, ry, rz = np.deg2rad(self.rot_x.value()), np.deg2rad(self.rot_y.value()), np.deg2rad(self.rot_z.value())
            def rotmat(rx, ry, rz):
                from scipy.spatial.transform import Rotation as R
                return R.from_euler('zyx', [rz, ry, rx]).as_matrix()
            Rmat = rotmat(rx, ry, rz)
            normal_rot = Rmat @ normal
            # Estimate plane size from mesh bounds
            mesh = getattr(self.mesh_viewer, 'mesh', None)
            if mesh is not None:
                bounds = np.array(mesh.bounds).reshape(3, 2)
                size = np.linalg.norm(bounds[:, 1] - bounds[:, 0]) * 0.5
            else:
                size = 1.0
            # Plane preview
            plane = pv.Plane(center=center, direction=normal_rot, i_size=size, j_size=size)
            self._plane_preview_actor = plotter.add_mesh(
                plane, color='yellow', opacity=0.4, name='plane_preview', pickable=False, reset_camera=False
            )
            # Slab region preview (box)
            thickness = self.slab_spin.value() if self.slab_group.isVisible() else 0.2
            # --- Slab position logic ---
            slab_offset = 0.0
            if self.slab_positive.isChecked():
                slab_offset = thickness/2
            elif self.slab_negative.isChecked():
                slab_offset = -thickness/2
            center = center + normal_rot * slab_offset
            # Build box centered on plane, oriented with normal
            if mesh is not None and thickness > 0:
                # Get axes for the plane
                z = normal_rot / np.linalg.norm(normal_rot)
                # Find a vector not parallel to z
                up = np.array([0, 0, 1]) if abs(z[2]) < 0.9 else np.array([1, 0, 0])
                x = np.cross(up, z); x /= np.linalg.norm(x)
                y = np.cross(z, x)
                # Build 4x4 transform
                T = np.eye(4)
                T[:3, 0] = x
                T[:3, 1] = y
                T[:3, 2] = z
                T[:3, 3] = center
                # Box extents
                slab_size = size
                # Use self.outside_opacity for the slab preview
                box = pv.Box(bounds=(-slab_size, slab_size, -slab_size, slab_size, -thickness/2, thickness/2))
                box = box.transform(T, inplace=False)
                self._slab_preview_actor = plotter.add_mesh(
                    box, color="#00e6ff", opacity=self.outside_opacity, style='surface', name="slab_preview", reset_camera=False,
                    show_edges=True, edge_color="#0080a0", line_width=2
                )
            # --- Positive side indicator ---
            if self.slab_positive.isChecked() and thickness > 0:
                arrow_length = thickness * 0.5
                direction = normal_rot / np.linalg.norm(normal_rot)
                arrow_start = center - direction * arrow_length
                arrow_vec = direction * (2 * arrow_length)
                plotter.add_arrows(
                    np.array([arrow_start]), np.array([arrow_vec]), mag=1.0, color="green", name="positive_side_arrow", reset_camera=False
                )
            # --- Center indicator for centered slab ONLY ---
            elif self.slab_centered.isChecked() and thickness > 0:
                plotter.add_points(
                    np.array([center]), color='red', point_size=20, render_points_as_spheres=True, name='center_indicator', opacity=0.7, reset_camera=False
                )
            # For negative side, do not show dot

        # Add arrow or dot to indicate slab side
        if len(self.picked_points) == 3 and self.plane_params is not None:
            # Remove previous arrow/dot
            if hasattr(self, '_arrow_preview_actor') and self._arrow_preview_actor:
                try:
                    plotter.remove_actor(self._arrow_preview_actor)
                except Exception:
                    pass
                self._arrow_preview_actor = None
            arrow_length = size * 0.3
            if self.slab_centered.isChecked():
                # Show a dot at the center using add_points (not add_sphere)
                self._arrow_preview_actor = plotter.add_points(
                    np.array([center]), color='red', point_size=20, render_points_as_spheres=True, name='center_indicator', opacity=0.7, reset_camera=False
                )
            else:
                # Show an arrow in the positive or negative direction
                direction = normal_rot / np.linalg.norm(normal_rot)
                if self.slab_negative.isChecked():
                    direction = -direction
                start = center
                self._arrow_preview_actor = plotter.add_arrows(
                    np.array([start]), np.array([direction]), mag=arrow_length, color='red', name='slab_side_arrow', reset_camera=False
                )
        plotter.render()

    def undo_point(self):
        if self.picked_points:
            self.picked_points.pop()
            self.update_info()
            self.update_plane_preview()

    def reset_points(self):
        self.picked_points = []
        self.update_info()
        self.slab_group.setVisible(False)
        self.btn_apply.setEnabled(False)
        self.plane_params = None
        self.slab_thickness = None
        self.update_plane_preview()
        # Remove arrow/dot preview
        plotter = self.mesh_viewer.plotter
        if hasattr(self, '_arrow_preview_actor') and self._arrow_preview_actor:
            try:
                plotter.remove_actor(self._arrow_preview_actor)
            except Exception:
                pass
            self._arrow_preview_actor = None

    def update_info(self):
        self.info_label.setText(f"Points picked: {len(self.picked_points)}/3")

    def define_plane(self):
        import numpy as np
        p1, p2, p3 = [np.array(p) for p in self.picked_points]
        normal = np.cross(p2 - p1, p3 - p1)
        if np.linalg.norm(normal) == 0:
            self.info_label.setText("Points are colinear. Pick again.")
            self.reset_points()
            return
        normal = normal / np.linalg.norm(normal)
        self.plane_params = {'origin': p1, 'normal': normal}
        self.slab_group.setVisible(True)
        self.btn_apply.setEnabled(True)
        self.info_label.setText("Plane defined. Set slab thickness and apply.")
        # Set transform controls to zero and show
        self.trans_x.setValue(0)
        self.trans_y.setValue(0)
        self.trans_z.setValue(0)
        self.rot_x.setValue(0)
        self.rot_y.setValue(0)
        self.rot_z.setValue(0)
        self.transform_group.setVisible(True)
        self.align_group.setVisible(True)
        self.slabpos_group.setVisible(True)
        self.update_plane_preview()

    def _on_slab_spin_changed(self, val):
        # Sync slider and update preview
        self.slab_slider.blockSignals(True)
        self.slab_slider.setValue(int(val * 100))
        self.slab_slider.blockSignals(False)
        self.update_plane_preview()

    def _on_slab_slider_changed(self, val):
        # Sync spinbox and update preview
        self.slab_spin.blockSignals(True)
        self.slab_spin.setValue(val / 100.0)
        self.slab_spin.blockSignals(False)
        self.update_plane_preview()

    @property
    def slab_side(self):
        if self.slab_centered.isChecked():
            return 'centered'
        elif self.slab_positive.isChecked():
            return 'positive'
        elif self.slab_negative.isChecked():
            return 'negative'
        return 'centered'

    def apply_and_accept(self):
        self.slab_thickness = self.slab_spin.value()
        self._slab_side = self.slab_side  # Store for mainwindow
        plotter = self.mesh_viewer.plotter
        # Remove only temporary preview actors (not persistent analysis plane)
        for attr in ['_slab_preview_actor', '_point_preview_actor']:
            actor = getattr(self, attr, None)
            if actor:
                try:
                    plotter.remove_actor(actor)
                except Exception:
                    pass
                setattr(self, attr, None)
        plotter.render()
        # Store plane and slab thickness for mainwindow/analysis
        if self.plane_params is not None:
            origin = np.array(self.plane_params['origin'])
            normal = np.array(self.plane_params['normal'])
            # --- Apply alignment if selected ---
            if self.align_x.isChecked():
                normal = np.array([1,0,0])
            elif self.align_y.isChecked():
                normal = np.array([0,1,0])
            elif self.align_z.isChecked():
                normal = np.array([0,0,1])
            # --- Slab position logic ---
            slab_offset = 0.0
            if self.slab_positive.isChecked():
                slab_offset = self.slab_thickness/2
            elif self.slab_negative.isChecked():
                slab_offset = -self.slab_thickness/2
            origin = origin + normal * slab_offset
            self.mesh_viewer.set_analysis_plane_params(origin, normal, self.slab_thickness)
        else:
            QMessageBox.warning(self, "Incomplete Plane", "Please pick 3 points to define the plane before applying.")
        self.accept()

    def update_apply_button_state(self):
        # Enable Apply only if 3 valid points are picked
        enable = hasattr(self, 'plane_points') and isinstance(self.plane_points, list) and len(self.plane_points) == 3
        self.btn_apply.setEnabled(enable)

    def closeEvent(self, event):
        # Remove only temporary actor
        pass

    def set_outside_opacity(self, value):
        self.outside_opacity = float(value)
        self.update_plane_preview()
