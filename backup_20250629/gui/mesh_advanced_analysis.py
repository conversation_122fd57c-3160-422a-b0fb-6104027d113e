from PySide6.QtWidgets import QDialog, QVBoxLayout, QLabel, QCheckBox, QFormLayout, QSpinBox, QDoubleSpinBox, QDialogButtonBox, QGroupBox, QHBoxLayout

class MeshAdvancedAnalysisDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Advanced Mesh Analysis")
        self.setMinimumWidth(400)
        layout = QVBoxLayout(self)
        # Analysis options
        self.mean_curv_cb = QCheckBox("Mean Curvature")
        self.gauss_curv_cb = QCheckBox("Gaussian Curvature")
        self.thickness_cb = QCheckBox("Thickness (Ray Casting)")
        self.geodesic_cb = QCheckBox("Geodesic Distance (from Point)")
        self.custom_cb = QCheckBox("Custom Scalar Field (Random)")
        # Parameters for each analysis
        form = QFormLayout()
        self.thickness_ray_length = QDoubleSpinBox()
        self.thickness_ray_length.setMinimum(1.0)
        self.thickness_ray_length.setMaximum(1000.0)
        self.thickness_ray_length.setValue(100.0)
        form.addRow("Thickness Ray Length:", self.thickness_ray_length)
        self.geodesic_source = QSpinBox()
        self.geodesic_source.setMinimum(0)
        self.geodesic_source.setMaximum(1000000)
        self.geodesic_source.setValue(0)
        form.addRow("Geodesic Source Point Index:", self.geodesic_source)
        # Group boxes for clarity
        analysis_group = QGroupBox("Select Analyses to Run")
        analysis_layout = QVBoxLayout(analysis_group)
        analysis_layout.addWidget(self.mean_curv_cb)
        analysis_layout.addWidget(self.gauss_curv_cb)
        analysis_layout.addWidget(self.thickness_cb)
        analysis_layout.addWidget(self.geodesic_cb)
        analysis_layout.addWidget(self.custom_cb)
        layout.addWidget(analysis_group)
        param_group = QGroupBox("Parameters")
        param_layout = QVBoxLayout(param_group)
        param_layout.addLayout(form)
        layout.addWidget(param_group)
        self.info_label = QLabel("Select one or more analyses and set parameters.")
        layout.addWidget(self.info_label)
        self.button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        layout.addWidget(self.button_box)

    def get_selected_analyses(self):
        return {
            'mean_curvature': self.mean_curv_cb.isChecked(),
            'gaussian_curvature': self.gauss_curv_cb.isChecked(),
            'thickness': self.thickness_cb.isChecked(),
            'thickness_ray_length': self.thickness_ray_length.value(),
            'geodesic': self.geodesic_cb.isChecked(),
            'geodesic_source': self.geodesic_source.value(),
            'custom': self.custom_cb.isChecked(),
        }
