#!/usr/bin/env python3
"""
Backup Verification Script for DeformViz 3D v2.0 Stable
Verifies that the backup is complete and functional
"""

import os
import sys
from pathlib import Path

def verify_backup():
    """Verify that the backup is complete and functional"""
    print("🔍 DeformViz 3D v2.0 Backup Verification")
    print("=" * 50)
    
    backup_root = Path(__file__).parent
    success = True
    
    # Check critical files
    critical_files = [
        "main.py",
        "requirements.txt", 
        "src/gui/mainwindow.py",
        "src/gui/analysis_wizard.py",
        "src/gui/meshviewer.py",
        "src/core/mesh_utils.py",
        "test_startup.py",
        "DEFORMVIZ_3D_USER_GUIDE.md",
        "BACKUP_DOCUMENTATION.md"
    ]
    
    print("📁 Checking Critical Files...")
    for file_path in critical_files:
        full_path = backup_root / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ Missing: {file_path}")
            success = False
    
    # Check directories
    critical_dirs = [
        "src",
        "src/gui", 
        "src/core",
        "src/icons",
        "tests"
    ]
    
    print("\n📂 Checking Directories...")
    for dir_path in critical_dirs:
        full_path = backup_root / dir_path
        if full_path.exists() and full_path.is_dir():
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ Missing directory: {dir_path}/")
            success = False
    
    # Check icon files
    icons_dir = backup_root / "src" / "icons"
    if icons_dir.exists():
        icon_files = list(icons_dir.glob("*.svg"))
        print(f"\n🎨 Found {len(icon_files)} icon files")
        if len(icon_files) >= 20:  # Should have 25+ icons
            print("✅ Icon collection complete")
        else:
            print("⚠️  Icon collection may be incomplete")
    
    # Check documentation files
    doc_files = [
        "DEFORMVIZ_3D_USER_GUIDE.md",
        "DEFORMVIZ_3D_COMPLETE_SUMMARY.md", 
        "SHORT_TERM_IMPROVEMENTS_COMPLETE.md",
        "STARTUP_ISSUES_FIXED.md",
        "CORE_ISSUES_FIXED.md"
    ]
    
    print("\n📖 Checking Documentation...")
    for doc_file in doc_files:
        full_path = backup_root / doc_file
        if full_path.exists():
            size = full_path.stat().st_size
            print(f"✅ {doc_file} ({size:,} bytes)")
        else:
            print(f"❌ Missing: {doc_file}")
    
    # Test imports (if possible)
    print("\n🧪 Testing Imports...")
    try:
        sys.path.insert(0, str(backup_root))
        
        # Test core imports
        from src.gui.mainwindow import MainWindow
        print("✅ MainWindow import successful")
        
        from src.gui.analysis_wizard import AnalysisWizard  
        print("✅ AnalysisWizard import successful")
        
        from src.gui.meshviewer import MeshViewerWidget
        print("✅ MeshViewerWidget import successful")
        
        print("✅ All critical imports successful!")
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        success = False
    
    # Check file sizes (basic sanity check)
    print("\n📊 File Size Check...")
    mainwindow_path = backup_root / "src" / "gui" / "mainwindow.py"
    if mainwindow_path.exists():
        size = mainwindow_path.stat().st_size
        if size > 50000:  # Should be substantial file
            print(f"✅ mainwindow.py: {size:,} bytes (substantial)")
        else:
            print(f"⚠️  mainwindow.py: {size:,} bytes (may be incomplete)")
    
    user_guide_path = backup_root / "DEFORMVIZ_3D_USER_GUIDE.md"
    if user_guide_path.exists():
        size = user_guide_path.stat().st_size
        if size > 20000:  # Should be comprehensive
            print(f"✅ User Guide: {size:,} bytes (comprehensive)")
        else:
            print(f"⚠️  User Guide: {size:,} bytes (may be incomplete)")
    
    # Summary
    print("\n" + "=" * 50)
    if success:
        print("🎉 BACKUP VERIFICATION SUCCESSFUL!")
        print("✅ All critical files present")
        print("✅ All imports working")
        print("✅ Documentation complete")
        print("✅ Backup is ready for use")
        print("\n🚀 To use this backup:")
        print("   python main.py")
        print("\n🧪 To test functionality:")
        print("   python test_startup.py")
    else:
        print("❌ BACKUP VERIFICATION FAILED!")
        print("   Some critical files or functionality missing")
        print("   Check error messages above")
    
    return success

if __name__ == "__main__":
    success = verify_backup()
    sys.exit(0 if success else 1)
