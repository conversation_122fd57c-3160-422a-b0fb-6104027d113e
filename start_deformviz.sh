#!/bin/bash

# DeformViz 3D Startup Script with Qt Platform Fix
# This script handles Qt platform plugin issues

echo "🚀 Starting DeformViz 3D with Qt Platform Fixes..."

# Try different Qt platform configurations
export QT_DEBUG_PLUGINS=0
export QT_LOGGING_RULES="qt.qpa.plugin.debug=false"

# Method 1: Try with explicit xcb platform
echo "📱 Attempting Method 1: XCB Platform..."
export QT_QPA_PLATFORM=xcb
python main.py "$@" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ Success with XCB platform!"
    exit 0
fi

# Method 2: Try without platform specification
echo "📱 Attempting Method 2: Auto Platform Detection..."
unset QT_QPA_PLATFORM
python main.py "$@" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ Success with auto platform!"
    exit 0
fi

# Method 3: Try with wayland
echo "📱 Attempting Method 3: Wayland Platform..."
export QT_QPA_PLATFORM=wayland
python main.py "$@" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ Success with Wayland platform!"
    exit 0
fi

# Method 4: Try without NVIDIA prime-run
echo "📱 Attempting Method 4: Without NVIDIA Prime..."
export QT_QPA_PLATFORM=xcb
unset __NV_PRIME_RENDER_OFFLOAD
unset __VK_LAYER_NV_optimus
unset __GLX_VENDOR_LIBRARY_NAME
python main.py "$@" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ Success without NVIDIA Prime!"
    exit 0
fi

# Method 5: Virtual display for testing
echo "📱 Attempting Method 5: Virtual Display..."
export QT_QPA_PLATFORM=minimal
python main.py --test "$@" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ Success with minimal platform (testing mode)!"
    exit 0
fi

# If all methods fail, show diagnostic info
echo "❌ All Qt platform methods failed. Diagnostic information:"
echo ""
echo "🔍 Environment Information:"
echo "Python: $(python --version)"
echo "Qt Platform Plugins Available:"
find /usr -name "*qxcb*" 2>/dev/null | head -5
echo ""
echo "💡 Possible Solutions:"
echo "1. Install Qt platform plugins: sudo apt install qt6-qpa-plugins"
echo "2. Install XCB libraries: sudo apt install libxcb-xinerama0"
echo "3. Try running in a desktop environment with X11"
echo "4. Use remote desktop or VNC for GUI access"
echo ""
echo "🧪 For testing without GUI, use: python test_startup.py"
echo ""

# Show the actual error for debugging
echo "🐛 Full error output:"
python main.py "$@"
