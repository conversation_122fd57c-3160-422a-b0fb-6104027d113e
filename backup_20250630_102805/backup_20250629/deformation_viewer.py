import os
os.environ["__GLX_VENDOR_LIBRARY_NAME"] = "nvidia"
os.environ["__NV_PRIME_RENDER_OFFLOAD"] = "1"
os.environ["DISPLAY"] = ":0"

import sys
from PySide6.QtCore import Qt, QSettings
from PySide6.QtWidgets import (QApplication, QMainWindow, QFileDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QSlider, QSpinBox, QDoubleSpinBox, QDialog, QDialogButtonBox, QComboBox, QCheckBox, QLineEdit, QMessageBox, QProgressBar, QProgressDialog, QButtonGroup, QRadioButton, QDockWidget, QTabWidget, QWidget, QListWidget, QTextEdit, QInputDialog)
from pyvistaqt import QtInteractor
import pyvista as pv
from mesh_utils import clean_mesh, remove_small_components, fill_small_holes, smooth_mesh, curvature_analysis
import numpy as np
import csv
import zipfile
import tempfile
import shutil
import traceback
try:
    import meshio
except ImportError:
    meshio = None
try:
    import pygltflib
except ImportError:
    pygltflib = None

# --- MeshProcessingDialog: mesh processing options dialog ---
class MeshProcessingDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Mesh Processing Options")
        self.setModal(True)
        layout = QVBoxLayout(self)
        self.clean_cb = QCheckBox("Clean mesh (remove duplicate points, degenerate faces)")
        self.clean_cb.setChecked(True)
        self.components_cb = QCheckBox("Remove small components")
        self.components_cb.setChecked(False)
        self.holes_cb = QCheckBox("Fill small holes")
        self.holes_cb.setChecked(False)
        self.hole_size_spin = QDoubleSpinBox()
        self.hole_size_spin.setRange(0.01, 10000.0)
        self.hole_size_spin.setValue(100.0)
        self.hole_size_spin.setSuffix(" area")
        holes_layout = QHBoxLayout()
        holes_layout.addWidget(self.holes_cb)
        holes_layout.addWidget(QLabel("Max Area:"))
        holes_layout.addWidget(self.hole_size_spin)
        self.smooth_cb = QCheckBox("Smooth mesh (Laplacian)")
        self.smooth_cb.setChecked(False)
        self.smooth_iter_spin = QSpinBox()
        self.smooth_iter_spin.setRange(1, 100)
        self.smooth_iter_spin.setValue(20)
        self.smooth_relax_spin = QDoubleSpinBox()
        self.smooth_relax_spin.setRange(0.001, 1.0)
        self.smooth_relax_spin.setValue(0.01)
        self.smooth_relax_spin.setSingleStep(0.01)
        smooth_layout = QHBoxLayout()
        smooth_layout.addWidget(self.smooth_cb)
        smooth_layout.addWidget(QLabel("Iter:"))
        smooth_layout.addWidget(self.smooth_iter_spin)
        smooth_layout.addWidget(QLabel("Relax:"))
        smooth_layout.addWidget(self.smooth_relax_spin)
        # --- Decimation/Remeshing ---
        self.decimate_cb = QCheckBox("Decimate mesh (reduce # faces)")
        self.decimate_cb.setChecked(False)
        self.decimate_slider = QSlider(Qt.Horizontal)
        self.decimate_slider.setRange(1, 100)
        self.decimate_slider.setValue(50)
        self.decimate_label = QLabel("Target: 50% faces")
        self.decimate_slider.valueChanged.connect(
            lambda v: self.decimate_label.setText(f"Target: {v}% faces")
        )
        decimate_layout = QHBoxLayout()
        decimate_layout.addWidget(self.decimate_cb)
        decimate_layout.addWidget(self.decimate_slider)
        decimate_layout.addWidget(self.decimate_label)
        self.remesh_cb = QCheckBox("Remesh (isotropic, experimental)")
        self.remesh_cb.setChecked(False)
        self.remesh_size = QDoubleSpinBox()
        self.remesh_size.setRange(0.001, 1.0)
        self.remesh_size.setValue(0.05)
        remesh_layout = QHBoxLayout()
        remesh_layout.addWidget(self.remesh_cb)
        remesh_layout.addWidget(QLabel("Target edge length:"))
        remesh_layout.addWidget(self.remesh_size)
        self.curvature_cb = QCheckBox("Curvature analysis")
        self.curvature_cb.setChecked(False)
        self.curv_type_spin = QSpinBox()
        self.curv_type_spin.setRange(0, 3)
        self.curv_type_spin.setValue(0)
        self.curv_smooth_spin = QSpinBox()
        self.curv_smooth_spin.setRange(0, 100)
        self.curv_smooth_spin.setValue(0)
        curv_layout = QHBoxLayout()
        curv_layout.addWidget(self.curvature_cb)
        curv_layout.addWidget(QLabel("Type (0=mean,1=gauss,2=max,3=min):"))
        curv_layout.addWidget(self.curv_type_spin)
        curv_layout.addWidget(QLabel("Smooth:"))
        curv_layout.addWidget(self.curv_smooth_spin)
        layout.addWidget(self.clean_cb)
        layout.addWidget(self.components_cb)
        layout.addLayout(holes_layout)
        layout.addLayout(smooth_layout)
        layout.addLayout(decimate_layout)
        layout.addLayout(remesh_layout)
        layout.addLayout(curv_layout)
        btns = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        btns.accepted.connect(self.accept)
        btns.rejected.connect(self.reject)
        layout.addWidget(btns)
        self.setLayout(layout)

    def get_selected_filters(self):
        return {
            'clean': self.clean_cb.isChecked(),
            'components': self.components_cb.isChecked(),
            'holes': self.holes_cb.isChecked(),
            'hole_size': self.hole_size_spin.value(),
            'smooth': self.smooth_cb.isChecked(),
            'smooth_iter': self.smooth_iter_spin.value(),
            'smooth_relax': self.smooth_relax_spin.value(),
            'decimate': self.decimate_cb.isChecked(),
            'decimate_target': self.decimate_slider.value(),
            'remesh': self.remesh_cb.isChecked(),
            'remesh_size': self.remesh_size.value(),
            'curvature': self.curvature_cb.isChecked(),
            'curv_type': self.curv_type_spin.value(),
            'curv_smooth': self.curv_smooth_spin.value(),
        }

# --- SliceOptionsDialog: options for slicing with plane ---
class SliceOptionsDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Slice Options")
        self.setModal(True)
        layout = QVBoxLayout(self)
        self.side_group = QButtonGroup(self)
        self.rb_pos = QRadioButton("Positive Side")
        self.rb_neg = QRadioButton("Negative Side")
        self.rb_both = QRadioButton("Ask Each Time")
        self.rb_pos.setChecked(True)
        self.side_group.addButton(self.rb_pos)
        self.side_group.addButton(self.rb_neg)
        self.side_group.addButton(self.rb_both)
        layout.addWidget(QLabel("Which side of the plane to keep?"))
        layout.addWidget(self.rb_pos)
        layout.addWidget(self.rb_neg)
        layout.addWidget(self.rb_both)
        self.cb_curve = QCheckBox("Extract intersection curve (polyline)")
        self.cb_curve.setChecked(False)
        layout.addWidget(self.cb_curve)
        btns = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        btns.accepted.connect(self.accept)
        btns.rejected.connect(self.reject)
        layout.addWidget(btns)

    def get_options(self):
        if self.rb_pos.isChecked():
            side = 'pos'
        elif self.rb_neg.isChecked():
            side = 'neg'
        else:
            side = 'both'
        want_curve = self.cb_curve.isChecked()
        return side, want_curve

class DeformationViewer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.settings = QSettings("DeformationViewer", "3DHeatmap")
        self.setWindowTitle("Deformation Viewer 3D")
        self.setGeometry(100, 100, 1000, 700)
        self._init_ui()
        self.mesh = None
        self.analysis_direction = int(self.settings.value("analysis_direction", 1))
        self.colormap = self.settings.value("colormap", "jet")
        self.slab_thickness = float(self.settings.value("slab_thickness", 0.10))
        self.mesh_vis_mode = self.settings.value("mesh_vis_mode", "surface+edge")
        self.last_dir = self.settings.value("last_dir", "")

    def closeEvent(self, event):
        self.settings.setValue("colormap", self.colormap)
        self.settings.setValue("slab_thickness", self.slab_thickness)
        self.settings.setValue("mesh_vis_mode", self.mesh_vis_mode)
        self.settings.setValue("analysis_direction", self.analysis_direction)
        self.settings.setValue("last_dir", getattr(self, 'last_dir', ""))
        super().closeEvent(event)

    def _init_ui(self):
        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)
        self.plotter = QtInteractor(central_widget)
        layout.addWidget(self.plotter.interactor)
        self.setCentralWidget(central_widget)
        self.plotter.set_background("lightgray")
        self.plotter.add_axes()
        pv.set_plot_theme("document")
        self.picked_points = []
        self.picked_point_actors = []

        # --- Sidebar for annotations, mesh info, and session/history ---
        self.sidebar = QDockWidget("Tools & Info", self)
        self.sidebar.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)
        self.tab_widget = QTabWidget()
        # Annotation tab
        self.annotation_tab = QWidget()
        ann_layout = QVBoxLayout(self.annotation_tab)
        self.ann_list = QListWidget()
        ann_layout.addWidget(QLabel("Annotations:"))
        ann_layout.addWidget(self.ann_list)
        btns_layout = QHBoxLayout()
        self.btn_add_point = QPushButton("Add Point")
        self.btn_add_line = QPushButton("Add Line")
        self.btn_add_text = QPushButton("Add Text")
        btns_layout.addWidget(self.btn_add_point)
        btns_layout.addWidget(self.btn_add_line)
        btns_layout.addWidget(self.btn_add_text)
        ann_layout.addLayout(btns_layout)
        self.btn_export_ann = QPushButton("Export Annotations")
        ann_layout.addWidget(self.btn_export_ann)
        self.tab_widget.addTab(self.annotation_tab, "Annotations")
        # Mesh info tab
        self.info_tab = QWidget()
        info_layout = QVBoxLayout(self.info_tab)
        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        info_layout.addWidget(QLabel("Mesh Info:"))
        info_layout.addWidget(self.info_text)
        self.tab_widget.addTab(self.info_tab, "Mesh Info")
        # History/session tab
        self.history_tab = QWidget()
        hist_layout = QVBoxLayout(self.history_tab)
        self.history_list = QListWidget()
        hist_layout.addWidget(QLabel("Session History:"))
        hist_layout.addWidget(self.history_list)
        self.tab_widget.addTab(self.history_tab, "History")
        self.sidebar.setWidget(self.tab_widget)
        self.addDockWidget(Qt.RightDockWidgetArea, self.sidebar)

        # Toolbar setup
        toolbar = self.addToolBar("Main Toolbar")
        toolbar.setMovable(False)
        toolbar.setFloatable(False)

        action_load = toolbar.addAction("Load 3D Mesh")
        action_load.setToolTip("Load a 3D mesh file (STL, OBJ, PLY, VTK, VTP)")
        action_load.triggered.connect(self.load_mesh)
        action_load_texture = toolbar.addAction("Load Texture")
        action_load_texture.setToolTip("Load and apply a texture image to the mesh (if supported)")
        action_load_texture.triggered.connect(self.load_texture)
        action_define_plane = toolbar.addAction("Define Plane (3 Points)")
        action_define_plane.setToolTip("Pick 3 points on the mesh to define a reference plane")
        action_define_plane.triggered.connect(self.start_point_picking)
        action_slab = toolbar.addAction("Set Slab Thickness")
        action_slab.setToolTip("Set the thickness of the slab region for analysis")
        action_slab.triggered.connect(self.set_slab_thickness)
        action_heatmap = toolbar.addAction("Show Deformation Heatmap")
        action_heatmap.setToolTip("Show deformation heatmap (distance from plane)")
        action_heatmap.triggered.connect(self.calculate_and_show_deformation)
        action_export = toolbar.addAction("Export Deformation CSV")
        action_export.setToolTip("Export deformation data as CSV")
        action_export.triggered.connect(self.export_deformation_csv)
        action_screenshot = toolbar.addAction("Save Screenshot")
        action_screenshot.setToolTip("Save a high-resolution screenshot of the 3D view")
        action_screenshot.triggered.connect(self.save_screenshot)
        action_reset = toolbar.addAction("Reset Picking")
        action_reset.setToolTip("Reset picked points and overlays")
        action_reset.triggered.connect(self.reset_picking)

        # Add separator for clarity
        toolbar.addSeparator()

        # Add Flip Analysis Direction button
        action_flip_dir = toolbar.addAction("Flip Analysis Direction")
        action_flip_dir.setToolTip("Flip the analysis direction (+normal/-normal)")
        action_flip_dir.triggered.connect(self.flip_analysis_direction)

        # Add Select Colormap button
        action_colormap = toolbar.addAction("Select Colormap")
        action_colormap.setToolTip("Select the colormap for the deformation heatmap")
        action_colormap.triggered.connect(self.select_colormap)

        # Add Mesh Visualization Mode button
        action_vis_mode = toolbar.addAction("Mesh Display Mode")
        action_vis_mode.setToolTip("Change mesh visualization: Surface+Edge, Surface, Points, Wireframe")
        action_vis_mode.triggered.connect(self.select_mesh_vis_mode)
        self.mesh_vis_mode = "surface+edge"  # Default mode

        # Add Mesh Processing button
        action_process = toolbar.addAction("Process Mesh")
        action_process.setToolTip("Clean, smooth, analyze, or transform the mesh")
        action_process.triggered.connect(self.process_mesh_dialog)

        # Add Crop/Slice Mesh button
        action_crop = toolbar.addAction("Crop/Slice Mesh")
        action_crop.setToolTip("Interactively crop or section the mesh using a box widget")
        action_crop.triggered.connect(self.crop_mesh_dialog)

        # Add Slice with Plane button
        action_slice = toolbar.addAction("Slice with Plane")
        action_slice.setToolTip("Interactively slice the mesh using a plane widget")
        action_slice.triggered.connect(self.slice_mesh_dialog)

        # Add Help/About button
        action_help = toolbar.addAction("Help/About")
        action_help.setToolTip("Show help and about information")
        action_help.triggered.connect(self.show_help_dialog)

        # Add Reset View button
        action_reset_view = toolbar.addAction("Reset View")
        action_reset_view.setToolTip("Reset camera and overlays")
        action_reset_view.triggered.connect(self.reset_view)

        # Add Export Mesh button
        action_export_mesh = toolbar.addAction("Export Mesh")
        action_export_mesh.setToolTip("Export the current mesh in various formats")
        action_export_mesh.triggered.connect(self.export_mesh_dialog)

        # Add Batch Processing button
        action_batch = toolbar.addAction("Batch Process")
        action_batch.setToolTip("Batch process multiple meshes (clean/process/export)")
        action_batch.triggered.connect(self.batch_process_dialog)

        # Add Advanced Analysis button
        action_advanced = toolbar.addAction("Advanced Analysis")
        action_advanced.setToolTip("Run advanced mesh analysis (thickness, curvature, quality)")
        action_advanced.triggered.connect(self.advanced_mesh_analysis_dialog)

        # Undo/Redo actions
        self.action_undo = toolbar.addAction("Undo")
        self.action_undo.setToolTip("Undo last mesh operation")
        self.action_undo.setShortcut("Ctrl+Z")
        self.action_undo.triggered.connect(self.undo)
        self.action_redo = toolbar.addAction("Redo")
        self.action_redo.setToolTip("Redo last undone operation")
        self.action_redo.setShortcut("Ctrl+Y")
        self.action_redo.triggered.connect(self.redo)
        self.action_undo.setEnabled(False)
        self.action_redo.setEnabled(False)

        # Add Save/Load Session actions
        action_save_session = toolbar.addAction("Save Session")
        action_save_session.setToolTip("Save current session (mesh, annotations, parameters)")
        action_save_session.setShortcut("Ctrl+Shift+S")
        action_save_session.triggered.connect(self.save_session)
        action_load_session = toolbar.addAction("Load Session")
        action_load_session.setToolTip("Load a saved session")
        action_load_session.setShortcut("Ctrl+Shift+O")
        action_load_session.triggered.connect(self.load_session)

        # Keyboard shortcuts
        action_load.setShortcut("Ctrl+O")
        action_screenshot.setShortcut("Ctrl+S")
        action_export.setShortcut("Ctrl+E")
        action_reset.setShortcut("Ctrl+R")
        action_help.setShortcut("F1")
        action_export_mesh.setShortcut("Ctrl+Shift+E")
        action_reset_view.setShortcut("Ctrl+Shift+R")

        # --- Add left-side toolbar for view controls ---
        view_toolbar = self.addToolBar("View Controls")
        view_toolbar.setMovable(False)
        view_toolbar.setFloatable(False)
        view_toolbar.setOrientation(Qt.Vertical)
        self.addToolBar(Qt.LeftToolBarArea, view_toolbar)

        # Perspective/Orthographic toggle
        self.action_proj_toggle = view_toolbar.addAction("Perspective/Ortho")
        self.action_proj_toggle.setToolTip("Toggle between perspective and orthographic projection")
        self.action_proj_toggle.triggered.connect(self.toggle_projection_mode)

        # Standard view buttons
        self.action_view_top = view_toolbar.addAction("Top")
        self.action_view_top.setToolTip("View from Top (Z+)")
        self.action_view_top.triggered.connect(lambda: self.set_standard_view('top'))
        self.action_view_bottom = view_toolbar.addAction("Bottom")
        self.action_view_bottom.setToolTip("View from Bottom (Z-)")
        self.action_view_bottom.triggered.connect(lambda: self.set_standard_view('bottom'))
        self.action_view_left = view_toolbar.addAction("Left")
        self.action_view_left.setToolTip("View from Left (X-)")
        self.action_view_left.triggered.connect(lambda: self.set_standard_view('left'))
        self.action_view_right = view_toolbar.addAction("Right")
        self.action_view_right.setToolTip("View from Right (X+)")
        self.action_view_right.triggered.connect(lambda: self.set_standard_view('right'))
        self.action_view_front = view_toolbar.addAction("Front")
        self.action_view_front.setToolTip("View from Front (Y+)")
        self.action_view_front.triggered.connect(lambda: self.set_standard_view('front'))
        self.action_view_back = view_toolbar.addAction("Back")
        self.action_view_back.setToolTip("View from Back (Y-)")
        self.action_view_back.triggered.connect(lambda: self.set_standard_view('back'))

        self.slab_thickness = 0.10
        self.slab_actor = None
        self.deformation_actor = None

        # Use the built-in statusBar() method, do not overwrite it
        self.statusBar().showMessage("Ready")
        # Annotation storage
        self.annotations = []

        # Connect annotation buttons
        self.btn_add_point.clicked.connect(self.add_annotation_point)
        self.btn_add_line.clicked.connect(self.add_annotation_line)
        self.btn_add_text.clicked.connect(self.add_annotation_text)
        self.btn_export_ann.clicked.connect(self.export_annotations)

        # Initialize mesh history
        self.mesh_history = []
        self.redo_stack = []
        self.saved_camera = None

    def closeEvent(self, event):
        self.settings.setValue("colormap", self.colormap)
        self.settings.setValue("slab_thickness", self.slab_thickness)
        self.settings.setValue("mesh_vis_mode", self.mesh_vis_mode)
        self.settings.setValue("analysis_direction", self.analysis_direction)
        self.settings.setValue("last_dir", getattr(self, 'last_dir', ""))
        super().closeEvent(event)

    def push_mesh_history(self):
        import copy
        if self.mesh is not None:
            self.mesh_history.append(self.mesh.copy(deep=True))
            if len(self.mesh_history) > 20:
                self.mesh_history.pop(0)
            self.action_undo.setEnabled(True)
            self.redo_stack.clear()
            self.action_redo.setEnabled(False)

    def undo(self):
        if not self.mesh_history:
            return
        self.redo_stack.append(self.mesh.copy(deep=True))
        self.mesh = self.mesh_history.pop()
        self.update_mesh_display()
        self.action_redo.setEnabled(True)
        if not self.mesh_history:
            self.action_undo.setEnabled(False)

    def redo(self):
        if not self.redo_stack:
            return
        self.mesh_history.append(self.mesh.copy(deep=True))
        self.mesh = self.redo_stack.pop()
        self.update_mesh_display()
        self.action_undo.setEnabled(True)
        if not self.redo_stack:
            self.action_redo.setEnabled(False)

    def _init_ui(self):
        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)
        self.plotter = QtInteractor(central_widget)
        layout.addWidget(self.plotter.interactor)
        self.setCentralWidget(central_widget)
        self.plotter.set_background("lightgray")
        self.plotter.add_axes()
        pv.set_plot_theme("document")
        self.picked_points = []
        self.picked_point_actors = []

        # --- Sidebar for annotations, mesh info, and session/history ---
        self.sidebar = QDockWidget("Tools & Info", self)
        self.sidebar.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)
        self.tab_widget = QTabWidget()
        # Annotation tab
        self.annotation_tab = QWidget()
        ann_layout = QVBoxLayout(self.annotation_tab)
        self.ann_list = QListWidget()
        ann_layout.addWidget(QLabel("Annotations:"))
        ann_layout.addWidget(self.ann_list)
        btns_layout = QHBoxLayout()
        self.btn_add_point = QPushButton("Add Point")
        self.btn_add_line = QPushButton("Add Line")
        self.btn_add_text = QPushButton("Add Text")
        btns_layout.addWidget(self.btn_add_point)
        btns_layout.addWidget(self.btn_add_line)
        btns_layout.addWidget(self.btn_add_text)
        ann_layout.addLayout(btns_layout)
        self.btn_export_ann = QPushButton("Export Annotations")
        ann_layout.addWidget(self.btn_export_ann)
        self.tab_widget.addTab(self.annotation_tab, "Annotations")
        # Mesh info tab
        self.info_tab = QWidget()
        info_layout = QVBoxLayout(self.info_tab)
        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        info_layout.addWidget(QLabel("Mesh Info:"))
        info_layout.addWidget(self.info_text)
        self.tab_widget.addTab(self.info_tab, "Mesh Info")
        # History/session tab
        self.history_tab = QWidget()
        hist_layout = QVBoxLayout(self.history_tab)
        self.history_list = QListWidget()
        hist_layout.addWidget(QLabel("Session History:"))
        hist_layout.addWidget(self.history_list)
        self.tab_widget.addTab(self.history_tab, "History")
        self.sidebar.setWidget(self.tab_widget)
        self.addDockWidget(Qt.RightDockWidgetArea, self.sidebar)

        # Toolbar setup
        toolbar = self.addToolBar("Main Toolbar")
        toolbar.setMovable(False)
        toolbar.setFloatable(False)

        action_load = toolbar.addAction("Load 3D Mesh")
        action_load.setToolTip("Load a 3D mesh file (STL, OBJ, PLY, VTK, VTP)")
        action_load.triggered.connect(self.load_mesh)
        action_load_texture = toolbar.addAction("Load Texture")
        action_load_texture.setToolTip("Load and apply a texture image to the mesh (if supported)")
        action_load_texture.triggered.connect(self.load_texture)
        action_define_plane = toolbar.addAction("Define Plane (3 Points)")
        action_define_plane.setToolTip("Pick 3 points on the mesh to define a reference plane")
        action_define_plane.triggered.connect(self.start_point_picking)
        action_slab = toolbar.addAction("Set Slab Thickness")
        action_slab.setToolTip("Set the thickness of the slab region for analysis")
        action_slab.triggered.connect(self.set_slab_thickness)
        action_heatmap = toolbar.addAction("Show Deformation Heatmap")
        action_heatmap.setToolTip("Show deformation heatmap (distance from plane)")
        action_heatmap.triggered.connect(self.calculate_and_show_deformation)
        action_export = toolbar.addAction("Export Deformation CSV")
        action_export.setToolTip("Export deformation data as CSV")
        action_export.triggered.connect(self.export_deformation_csv)
        action_screenshot = toolbar.addAction("Save Screenshot")
        action_screenshot.setToolTip("Save a high-resolution screenshot of the 3D view")
        action_screenshot.triggered.connect(self.save_screenshot)
        action_reset = toolbar.addAction("Reset Picking")
        action_reset.setToolTip("Reset picked points and overlays")
        action_reset.triggered.connect(self.reset_picking)

        # Add separator for clarity
        toolbar.addSeparator()

        # Add Flip Analysis Direction button
        action_flip_dir = toolbar.addAction("Flip Analysis Direction")
        action_flip_dir.setToolTip("Flip the analysis direction (+normal/-normal)")
        action_flip_dir.triggered.connect(self.flip_analysis_direction)

        # Add Select Colormap button
        action_colormap = toolbar.addAction("Select Colormap")
        action_colormap.setToolTip("Select the colormap for the deformation heatmap")
        action_colormap.triggered.connect(self.select_colormap)

        # Add Mesh Visualization Mode button
        action_vis_mode = toolbar.addAction("Mesh Display Mode")
        action_vis_mode.setToolTip("Change mesh visualization: Surface+Edge, Surface, Points, Wireframe")
        action_vis_mode.triggered.connect(self.select_mesh_vis_mode)
        self.mesh_vis_mode = "surface+edge"  # Default mode

        # Add Mesh Processing button
        action_process = toolbar.addAction("Process Mesh")
        action_process.setToolTip("Clean, smooth, analyze, or transform the mesh")
        action_process.triggered.connect(self.process_mesh_dialog)

        # Add Crop/Slice Mesh button
        action_crop = toolbar.addAction("Crop/Slice Mesh")
        action_crop.setToolTip("Interactively crop or section the mesh using a box widget")
        action_crop.triggered.connect(self.crop_mesh_dialog)

        # Add Slice with Plane button
        action_slice = toolbar.addAction("Slice with Plane")
        action_slice.setToolTip("Interactively slice the mesh using a plane widget")
        action_slice.triggered.connect(self.slice_mesh_dialog)

        # Add Help/About button
        action_help = toolbar.addAction("Help/About")
        action_help.setToolTip("Show help and about information")
        action_help.triggered.connect(self.show_help_dialog)

        # Add Reset View button
        action_reset_view = toolbar.addAction("Reset View")
        action_reset_view.setToolTip("Reset camera and overlays")
        action_reset_view.triggered.connect(self.reset_view)

        # Add Export Mesh button
        action_export_mesh = toolbar.addAction("Export Mesh")
        action_export_mesh.setToolTip("Export the current mesh in various formats")
        action_export_mesh.triggered.connect(self.export_mesh_dialog)

        # Add Batch Processing button
        action_batch = toolbar.addAction("Batch Process")
        action_batch.setToolTip("Batch process multiple meshes (clean/process/export)")
        action_batch.triggered.connect(self.batch_process_dialog)

        # Add Advanced Analysis button
        action_advanced = toolbar.addAction("Advanced Analysis")
        action_advanced.setToolTip("Run advanced mesh analysis (thickness, curvature, quality)")
        action_advanced.triggered.connect(self.advanced_mesh_analysis_dialog)

        # Undo/Redo actions
        self.action_undo = toolbar.addAction("Undo")
        self.action_undo.setToolTip("Undo last mesh operation")
        self.action_undo.setShortcut("Ctrl+Z")
        self.action_undo.triggered.connect(self.undo)
        self.action_redo = toolbar.addAction("Redo")
        self.action_redo.setToolTip("Redo last undone operation")
        self.action_redo.setShortcut("Ctrl+Y")
        self.action_redo.triggered.connect(self.redo)
        self.action_undo.setEnabled(False)
        self.action_redo.setEnabled(False)

        # Add Save/Load Session actions
        action_save_session = toolbar.addAction("Save Session")
        action_save_session.setToolTip("Save current session (mesh, annotations, parameters)")
        action_save_session.setShortcut("Ctrl+Shift+S")
        action_save_session.triggered.connect(self.save_session)
        action_load_session = toolbar.addAction("Load Session")
        action_load_session.setToolTip("Load a saved session")
        action_load_session.setShortcut("Ctrl+Shift+O")
        action_load_session.triggered.connect(self.load_session)

        # Keyboard shortcuts
        action_load.setShortcut("Ctrl+O")
        action_screenshot.setShortcut("Ctrl+S")
        action_export.setShortcut("Ctrl+E")
        action_reset.setShortcut("Ctrl+R")
        action_help.setShortcut("F1")
        action_export_mesh.setShortcut("Ctrl+Shift+E")
        action_reset_view.setShortcut("Ctrl+Shift+R")

        # --- Add left-side toolbar for view controls ---
        view_toolbar = self.addToolBar("View Controls")
        view_toolbar.setMovable(False)
        view_toolbar.setFloatable(False)
        view_toolbar.setOrientation(Qt.Vertical)
        self.addToolBar(Qt.LeftToolBarArea, view_toolbar)

        # Perspective/Orthographic toggle
        self.action_proj_toggle = view_toolbar.addAction("Perspective/Ortho")
        self.action_proj_toggle.setToolTip("Toggle between perspective and orthographic projection")
        self.action_proj_toggle.triggered.connect(self.toggle_projection_mode)

        # Standard view buttons
        self.action_view_top = view_toolbar.addAction("Top")
        self.action_view_top.setToolTip("View from Top (Z+)")
        self.action_view_top.triggered.connect(lambda: self.set_standard_view('top'))
        self.action_view_bottom = view_toolbar.addAction("Bottom")
        self.action_view_bottom.setToolTip("View from Bottom (Z-)")
        self.action_view_bottom.triggered.connect(lambda: self.set_standard_view('bottom'))
        self.action_view_left = view_toolbar.addAction("Left")
        self.action_view_left.setToolTip("View from Left (X-)")
        self.action_view_left.triggered.connect(lambda: self.set_standard_view('left'))
        self.action_view_right = view_toolbar.addAction("Right")
        self.action_view_right.setToolTip("View from Right (X+)")
        self.action_view_right.triggered.connect(lambda: self.set_standard_view('right'))
        self.action_view_front = view_toolbar.addAction("Front")
        self.action_view_front.setToolTip("View from Front (Y+)")
        self.action_view_front.triggered.connect(lambda: self.set_standard_view('front'))
        self.action_view_back = view_toolbar.addAction("Back")
        self.action_view_back.setToolTip("View from Back (Y-)")
        self.action_view_back.triggered.connect(lambda: self.set_standard_view('back'))

        self.slab_thickness = 0.10
        self.slab_actor = None
        self.deformation_actor = None

        # Use the built-in statusBar() method, do not overwrite it
        self.statusBar().showMessage("Ready")
        # Annotation storage
        self.annotations = []

        # Connect annotation buttons
        self.btn_add_point.clicked.connect(self.add_annotation_point)
        self.btn_add_line.clicked.connect(self.add_annotation_line)
        self.btn_add_text.clicked.connect(self.add_annotation_text)
        self.btn_export_ann.clicked.connect(self.export_annotations)

        # Initialize mesh history
        self.mesh_history = []
        self.redo_stack = []
        self.saved_camera = None

    def closeEvent(self, event):
        self.settings.setValue("colormap", self.colormap)
        self.settings.setValue("slab_thickness", self.slab_thickness)
        self.settings.setValue("mesh_vis_mode", self.mesh_vis_mode)
        self.settings.setValue("analysis_direction", self.analysis_direction)
        self.settings.setValue("last_dir", getattr(self, 'last_dir', ""))
        super().closeEvent(event)

    def zoom_to_mesh(self):
        if self.mesh is not None:
            self.plotter.reset_camera()
            self.plotter.render()
            self.statusBar().showMessage("Zoomed to mesh.")

    def save_camera_view(self):
        self.saved_camera = self.plotter.camera_position
        self.statusBar().showMessage("Camera view saved.")

    def restore_camera_view(self):
        if self.saved_camera:
            self.plotter.camera_position = self.saved_camera
            self.plotter.render()
            self.statusBar().showMessage("Camera view restored.")
        else:
            QMessageBox.information(self, "No Camera Saved", "No camera view has been saved yet.")

    def reset_all_overlays(self):
        # Remove slab, deformation, arrows, annotations, colorbars
        if self.slab_actor:
            self.plotter.remove_actor(self.slab_actor)
            self.slab_actor = None
        if self.deformation_actor:
            self.plotter.remove_actor(self.deformation_actor)
            self.deformation_actor = None
        if hasattr(self, 'arrow_actor') and self.arrow_actor:
            self.plotter.remove_actor(self.arrow_actor)
            self.arrow_actor = None
        self.plotter.remove_scalar_bar()
        # Remove annotation actors if any
        for ann in getattr(self, 'annotation_actors', []):
            self.plotter.remove_actor(ann)
        self.annotation_actors = []
        self.statusBar().showMessage("All overlays removed.")
        self.plotter.render()

    def decimate_mesh_dialog(self):
        if self.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        dialog = QDialog(self)
        dialog.setWindowTitle("Mesh Decimation/Remeshing")
        layout = QVBoxLayout(dialog)
        decimate_cb = QCheckBox("Decimate (reduce triangles)")
        decimate_cb.setChecked(True)
        target_spin = QDoubleSpinBox()
        target_spin.setRange(0.01, 1.0)
        target_spin.setSingleStep(0.01)
        target_spin.setValue(0.5)
        layout.addWidget(decimate_cb)
        layout.addWidget(QLabel("Target fraction (0.01-1.0):"))
        layout.addWidget(target_spin)
        remesh_cb = QCheckBox("Remesh (isotropic)")
        remesh_cb.setChecked(False)
        remesh_size = QDoubleSpinBox()
        remesh_size.setRange(0.001, 1.0)
        remesh_size.setValue(0.05)
        layout.addWidget(remesh_cb)
        layout.addWidget(QLabel("Remesh target edge length:"))
        layout.addWidget(remesh_size)
        btns = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        btns.accepted.connect(dialog.accept)
        btns.rejected.connect(dialog.reject)
        layout.addWidget(btns)
        if not dialog.exec():
            return
        mesh = self.mesh
        if decimate_cb.isChecked():
            self.push_mesh_history()
            mesh = mesh.decimate(target_reduction=1.0 - target_spin.value(), preserve_topology=True)
        if remesh_cb.isChecked():
            try:
                mesh = mesh.remesh(remesh_size.value())
            except Exception:
                QMessageBox.warning(self, "Remesh Error", "Remeshing not supported for this mesh or PyVista version.")
        self.mesh = mesh
        self.update_mesh_display()
        self.statusBar().showMessage("Mesh decimation/remeshing complete.")

    def show_mesh_quality_metrics(self):
        if self.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        msg = ""
        try:
            area = self.mesh.area if hasattr(self.mesh, 'area') else None
            volume = self.mesh.volume if hasattr(self.mesh, 'volume') else None
            aspect = self.mesh.compute_cell_quality(quality_measure='aspect_ratio')
            min_ar = np.min(aspect)
            max_ar = np.max(aspect)
            mean_ar = np.mean(aspect)
            msg += f"Area: {area:.2f}\n" if area else ""
            msg += f"Volume: {volume:.2f}\n" if volume else ""
            msg += f"Aspect Ratio: min={min_ar:.2f}, max={max_ar:.2f}, mean={mean_ar:.2f}\n"
        except Exception as e:
            msg += f"Error computing quality: {e}\n"
        QMessageBox.information(self, "Mesh Quality Metrics", msg)

    def advanced_mesh_analysis_dialog(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("Advanced Mesh Analysis")
        layout = QVBoxLayout(dialog)
        if self.mesh is None:
            layout.addWidget(QLabel("No mesh loaded."))
        else:
            # Mesh quality metrics
            from pyvista import PolyData
            mesh = self.mesh
            info = ""
            if isinstance(mesh, PolyData):
                try:
                    aspect = mesh.compute_cell_quality(quality_measure='aspect_ratio')
                    min_aspect = aspect['CellQuality'].min()
                    max_aspect = aspect['CellQuality'].max()
                    mean_aspect = aspect['CellQuality'].mean()
                    info += f"Aspect Ratio (min/mean/max): {min_aspect:.2f} / {mean_aspect:.2f} / {max_aspect:.2f}\n"
                except Exception:
                    info += "Aspect ratio: Not available\n"
                try:
                    min_angle = mesh.compute_cell_quality(quality_measure='min_angle')['CellQuality'].min()
                    max_angle = mesh.compute_cell_quality(quality_measure='max_angle')['CellQuality'].max()
                    info += f"Min Angle: {min_angle:.2f} deg\nMax Angle: {max_angle:.2f} deg\n"
                except Exception:
                    info += "Angle metrics: Not available\n"
                try:
                    from mesh_utils import curvature_analysis
                    curv = curvature_analysis(mesh, curv_type=0, smooth_iter=0)
                    info += f"Curvature (mean): min={curv.min():.4f}, max={curv.max():.4f}, mean={curv.mean():.4f}\n"
                except Exception:
                    info += "Curvature: Not available\n"
            else:
                info += "Mesh is not PolyData.\n"
            text = QTextEdit()
            text.setReadOnly(True)
            text.setPlainText(info)
            layout.addWidget(QLabel("Mesh Quality Metrics:"))
            layout.addWidget(text)
        btns = QDialogButtonBox(QDialogButtonBox.Ok)
        btns.accepted.connect(dialog.accept)
        layout.addWidget(btns)
        dialog.exec()

    def export_gltf_dialog(self):
        if self.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        file_path, _ = QFileDialog.getSaveFileName(self, "Export glTF", os.path.join(self.last_dir, "mesh.glb"), "glTF Files (*.glb *.gltf)")
        if not file_path:
            return
        try:
            if meshio is not None:
                meshio.write(file_path, self.mesh)
                QMessageBox.information(self, "Export Complete", f"Mesh exported as glTF to {file_path}")
            elif pygltflib is not None:
                # TODO: implement pygltflib export if meshio not available
                QMessageBox.warning(self, "glTF Export", "pygltflib export not implemented. Install meshio for glTF export.")
            else:
                QMessageBox.warning(self, "glTF Export", "Install meshio or pygltflib for glTF export support.")
        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Failed to export glTF: {e}\n{traceback.format_exc()}")

    def export_screenshot_with_overlays(self):
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Screenshot with Overlays", os.path.join(self.last_dir, "screenshot_with_overlays.png"), "PNG Files (*.png)")
        if not file_path:
            return
        # Overlays and annotations are already in the PyVista scene
        self.plotter.screenshot(file_path, window_size=(4*self.plotter.width(), 4*self.plotter.height()), return_img=False)
        QMessageBox.information(self, "Export Complete", f"Screenshot with overlays saved to {file_path}")

    def export_session_archive(self):
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Session Archive", os.path.join(self.last_dir, "session.zip"), "Zip Archives (*.zip)")
        if not file_path:
            return
        tmpdir = tempfile.mkdtemp()
        try:
            # Save mesh
            mesh_path = os.path.join(tmpdir, "mesh.vtp")
            self.mesh.save(mesh_path)
            # Save annotations
            ann_path = os.path.join(tmpdir, "annotations.csv")
            with open(ann_path, "w", newline="") as f:
                writer = csv.writer(f)
                writer.writerow(["type", "data"])
                for ann in self.annotations:
                    writer.writerow([ann['type'], ann['data']])
            # Save parameters
            param_path = os.path.join(tmpdir, "params.txt")
            with open(param_path, "w") as f:
                f.write(f"colormap={self.colormap}\nslab_thickness={self.slab_thickness}\nmesh_vis_mode={self.mesh_vis_mode}\nanalysis_direction={self.analysis_direction}\n")
            # Zip all
            with zipfile.ZipFile(file_path, 'w') as z:
                z.write(mesh_path, arcname="mesh.vtp")
                z.write(ann_path, arcname="annotations.csv")
                z.write(param_path, arcname="params.txt")
            QMessageBox.information(self, "Export Complete", f"Session exported to {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Failed to export session: {e}\n{traceback.format_exc()}")
        finally:
            shutil.rmtree(tmpdir)

    def show_manual_dialog(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("Help / Manual")
        layout = QVBoxLayout(dialog)
        text = QTextEdit()
        text.setReadOnly(True)
        text.setHtml("""
        <h2>Deformation Viewer 3D - Manual</h2>
        <ul>
        <li><b>Load 3D Mesh</b>: Open STL, OBJ, PLY, VTK, VTP files.</li>
        <li><b>Define Plane (3 Points)</b>: Pick 3 points to define a reference plane.</li>
        <li><b>Set Slab Thickness</b>: Set the thickness for slab analysis.</li>
        <li><b>Show Deformation Heatmap</b>: Visualize deformation as a heatmap.</li>
        <li><b>Export Deformation CSV</b>: Export per-point deformation data.</li>
        <li><b>Save Screenshot</b>: Save a high-res screenshot.</li>
        <li><b>Process Mesh</b>: Clean, smooth, fill holes, analyze curvature.</li>
        <li><b>Crop/Slice Mesh</b>: Interactively crop or section the mesh.</li>
        <li><b>Export Mesh</b>: Export mesh in various formats.</li>
        <li><b>Export glTF</b>: Export mesh as glTF (.glb/.gltf).</li>
        <li><b>Export Screenshot + Overlays</b>: Save screenshot with overlays/annotations.</li>
        <li><b>Export Session Archive</b>: Save mesh, annotations, and parameters as a zip.</li>
        <li><b>Undo/Redo</b>: Undo/redo mesh operations.</li>
        <li><b>Save/Load Session</b>: Save/load session state.</li>
        <li><b>Camera Controls</b>: Fit to mesh, save/restore camera, standard views.</li>
        <li><b>Annotations</b>: Add points, lines, text; export to CSV.</li>
        <li><b>Batch Process</b>: Process multiple meshes at once.</li>
        <li><b>Advanced Analysis</b>: Curvature, thickness, quality metrics.</li>
        </ul>
        <p>For more help, see the <b>About</b> dialog or contact the developer.</p>
        """)
        layout.addWidget(text)
        # Optionally add screenshots if available
        btns = QDialogButtonBox(QDialogButtonBox.Close)
        btns.rejected.connect(dialog.reject)
        btns.accepted.connect(dialog.accept)
        layout.addWidget(btns)
        dialog.exec()

    def show_help_dialog(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("About Deformation Viewer 3D")
        layout = QVBoxLayout(dialog)
        text = QTextEdit()
        text.setReadOnly(True)
        text.setHtml("""
        <h2>Deformation Viewer 3D</h2>
        <p>Version 1.0.0<br>
        Developed by Gianfranco Morelli<br>
        <a href='https://github.com/gmorelli89/3dHeatmap'>GitHub Repository</a><br>
        <a href='mailto:<EMAIL>'><EMAIL></a></p>
        <p>This application visualizes and analyzes 3D wall deformation for archaeological research.<br>
        Built with PySide6, PyVistaQt, and meshio.<br>
        For help, see the Help/Manual dialog.</p>
        <p>&copy; 2025 Gianfranco Morelli, University of Florence</p>
        """)
        layout.addWidget(text)
        btns = QDialogButtonBox(QDialogButtonBox.Close)
        btns.rejected.connect(dialog.reject)
        btns.accepted.connect(dialog.accept)
        layout.addWidget(btns)
        dialog.exec()

    # --- Camera and overlay state ---
    def fit_to_mesh(self):
        if self.mesh is not None:
            self.plotter.camera_position = 'xy'
            self.plotter.reset_camera()
            self.statusBar().showMessage("Camera fit to mesh.")

    def save_camera(self):
        self._saved_camera = self.plotter.camera_position
        self.statusBar().showMessage("Camera position saved.")

    def restore_camera(self):
        if hasattr(self, '_saved_camera') and self._saved_camera:
            self.plotter.camera_position = self._saved_camera
            self.plotter.render()
            self.statusBar().showMessage("Camera position restored.")
        else:
            QMessageBox.information(self, "No Camera Saved", "No camera position has been saved yet.")

    def reset_overlays(self):
        # Remove slab, arrow, deformation, and colorbar overlays
        if self.slab_actor:
            self.plotter.remove_actor(self.slab_actor)
            self.slab_actor = None
        if hasattr(self, 'arrow_actor') and self.arrow_actor:
            self.plotter.remove_actor(self.arrow_actor)
            self.arrow_actor = None
        if self.deformation_actor:
            self.plotter.remove_actor(self.deformation_actor)
            self.deformation_actor = None
        self.plotter.remove_scalar_bar()
        self.plotter.render()
        self.statusBar().showMessage("Overlays reset.")

    def set_slab_thickness(self):
        if self.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        dialog = QDialog(self)
        dialog.setWindowTitle("Set Slab Thickness")
        layout = QVBoxLayout(dialog)
        label = QLabel("Slab thickness (meters):")
        spin = QDoubleSpinBox()
        spin.setRange(0.001, 10.0)
        spin.setDecimals(3)
        spin.setValue(self.slab_thickness)
        layout.addWidget(label)
        layout.addWidget(spin)
        btns = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        btns.accepted.connect(dialog.accept)
        btns.rejected.connect(dialog.reject)
        layout.addWidget(btns)
        if dialog.exec():
            self.slab_thickness = spin.value()
            self.statusBar().showMessage(f"Slab thickness set to {self.slab_thickness:.3f} m.")

    def calculate_and_show_deformation(self):
        if self.mesh is None or len(self.picked_points) != 3:
            QMessageBox.warning(self, "Missing Data", "Load a mesh and pick 3 points to define the reference plane.")
            return
        mesh = self.mesh
        pts = np.array(self.picked_points)
        # Plane: ax + by + cz + d = 0
        v1 = pts[1] - pts[0]
        v2 = pts[2] - pts[0]
        normal = np.cross(v1, v2)
        normal = normal / np.linalg.norm(normal)
        d = -np.dot(normal, pts[0])
        # Optionally flip normal if analysis_direction is -1
        if getattr(self, 'analysis_direction', 1) == -1:
            normal = -normal
            d = -d
        # Distance from each mesh point to plane
        mesh_points = mesh.points
        distances = (mesh_points @ normal + d)
        # Slab region: within +/- slab_thickness/2
        slab_mask = np.abs(distances) <= (self.slab_thickness / 2)
        # Store for export
        self._deformation_data = {
            'distances': distances,
            'slab_mask': slab_mask,
            'normal': normal,
            'plane_point': pts[0],
        }
        # Visualize: color by distance, mask outside slab
        cmap = getattr(self, 'colormap', 'viridis')
        mesh_copy = mesh.copy()
        mesh_copy["Deformation"] = distances
        # Remove previous overlays
        self.reset_all_overlays()
        # Show slab region as transparent box
        center = pts[0] + normal * (self.slab_thickness/2)
        bounds = mesh.bounds
        slab_box = pv.Box(bounds=bounds)
        # Transform box to align with plane
        from scipy.spatial.transform import Rotation as R
        box_center = np.mean(np.array(slab_box.points), axis=0)
        rot = R.align_vectors([normal], [[0,0,1]])[0]
        slab_box.points = rot.apply(slab_box.points - box_center) + center
        self.slab_actor = self.plotter.add_mesh(slab_box, color='cyan', opacity=0.15, name='slab')
        # Show deformation heatmap
        self.deformation_actor = self.plotter.add_mesh(mesh_copy, scalars="Deformation", cmap=cmap, clim=[-self.slab_thickness/2, self.slab_thickness/2], show_edges=False, name='deformation')
        # Add colorbar (always vertical, right side)
        self.plotter.remove_scalar_bar()
        self.plotter.add_scalar_bar(title="Deformation (m)", n_labels=5, vertical=True, fmt="%.3f", interactive=False, position_x=0.88)
        # Add normal arrow
        arrow = pv.Arrow(start=pts[0], direction=normal, scale=self.slab_thickness, tip_length=0.2)
        self.arrow_actor = self.plotter.add_mesh(arrow, color='red', name='arrow')
        self.plotter.render()
        self.statusBar().showMessage("Deformation heatmap displayed.")

    def export_deformation_csv(self):
        if not hasattr(self, '_deformation_data') or self._deformation_data is None:
            QMessageBox.warning(self, "No Data", "Calculate deformation first.")
            return
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Deformation CSV", os.path.join(self.last_dir or '', "deformation.csv"), "CSV Files (*.csv)")
        if not file_path:
            return
        try:
            distances = self._deformation_data['distances']
            slab_mask = self._deformation_data['slab_mask']
            with open(file_path, "w", newline="") as f:
                writer = csv.writer(f)
                writer.writerow(["x", "y", "z", "distance", "in_slab"])
                for pt, dist, in_slab in zip(self.mesh.points, distances, slab_mask):
                    writer.writerow([*pt, dist, int(in_slab)])
            QMessageBox.information(self, "Export Complete", f"Deformation data exported to {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Failed to export CSV:\n{e}")

    def select_colormap(self):
        cmaps = ["jet", "viridis", "plasma", "inferno", "magma", "cividis", "coolwarm", "bwr", "rainbow"]
        cmap, ok = QInputDialog.getItem(self, "Select Colormap", "Colormap:", cmaps, cmaps.index(self.colormap) if hasattr(self, 'colormap') and self.colormap in cmaps else 0, False)
        if ok:
            self.colormap = cmap
            self.statusBar().showMessage(f"Colormap set to {cmap}")
            if self.mesh is not None and hasattr(self, '_deformation_data'):
                self.calculate_and_show_deformation()

    def select_mesh_vis_mode(self):
        modes = ["surface+edge", "surface", "points", "wireframe"]
        mode, ok = QInputDialog.getItem(self, "Mesh Display Mode", "Mode:", modes, modes.index(self.mesh_vis_mode) if hasattr(self, 'mesh_vis_mode') and self.mesh_vis_mode in modes else 0, False)
        if ok:
            self.mesh_vis_mode = mode
            self.statusBar().showMessage(f"Mesh display mode set to {mode}")
            self.update_mesh_display()

    def process_mesh_dialog(self):
        if self.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        dialog = MeshProcessingDialog(self)
        if not dialog.exec():
            return
        opts = dialog.get_selected_filters()
        mesh = self.mesh
        self.push_mesh_history()
        steps = sum([
            opts['clean'], opts['components'], opts['holes'], opts['smooth'], opts['decimate'], opts['remesh'], opts['curvature']
        ])
        progress = QProgressDialog("Processing mesh...", None, 0, steps, self)
        progress.setWindowTitle("Mesh Processing")
        progress.setWindowModality(Qt.ApplicationModal)
        progress.setMinimumDuration(0)
        progress.setValue(0)
        step = 0
        try:
            if opts['clean']:
                mesh = clean_mesh(mesh)
                step += 1
                progress.setValue(step)
            if opts['components']:
                mesh = remove_small_components(mesh)
                step += 1
                progress.setValue(step)
            if opts['holes']:
                mesh = fill_small_holes(mesh, opts['hole_size'])
                step += 1
                progress.setValue(step)
            if opts['smooth']:
                mesh = smooth_mesh(mesh, n_iter=opts['smooth_iter'], relaxation=opts['smooth_relax'])
                step += 1
                progress.setValue(step)
            if opts['decimate']:
                try:
                    mesh = mesh.decimate(target_reduction=1.0 - opts['decimate_target']/100.0, preserve_topology=True)
                except Exception as e:
                    QMessageBox.warning(self, "Decimation Error", f"Mesh decimation failed: {e}")
                step += 1
                progress.setValue(step)
            if opts['remesh']:
                try:
                    mesh = mesh.remesh(opts['remesh_size'])
                except Exception as e:
                    QMessageBox.warning(self, "Remesh Error", f"Remeshing failed: {e}")
                step += 1
                progress.setValue(step)
            if opts['curvature']:
                try:
                    mesh = curvature_analysis(mesh, curv_type=opts['curv_type'], smooth_iter=opts['curv_smooth'])
                except Exception as e:
                    QMessageBox.warning(self, "Curvature Error", f"Curvature analysis failed: {e}")
                step += 1
                progress.setValue(step)
        finally:
            progress.setValue(steps)
        self.mesh = mesh
        self.update_mesh_display()
        self.statusBar().showMessage("Mesh processing complete.")

    def crop_mesh_dialog(self):
        if self.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        self.push_mesh_history()
        self.statusBar().showMessage("Draw a box to crop the mesh. Press Apply or Cancel.")
        box_widget = self.plotter.add_box_widget(
            callback=None, color="yellow", opacity=0.2, bounds=self.mesh.bounds, use_planes=False
        )
        def apply_crop():
            bounds = box_widget.bounds
            cropped = self.mesh.clip_box(bounds, invert=False)
            self.mesh = cropped
            self.plotter.remove_actor(box_widget)
            self.update_mesh_display()
            self.statusBar().showMessage("Mesh cropped.")
        def cancel_crop():
            self.plotter.remove_actor(box_widget)
            self.statusBar().showMessage("Crop cancelled.")
        # Add Apply/Cancel buttons
        btns = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel, self)
        btns.accepted.connect(apply_crop)
        btns.rejected.connect(cancel_crop)
        # Show dialog
        dlg = QDialog(self)
        dlg.setWindowTitle("Crop Mesh")
        layout = QVBoxLayout(dlg)
        layout.addWidget(QLabel("Draw a box to crop the mesh. Adjust and press Apply."))
        layout.addWidget(btns)
        dlg.exec()

    def slice_mesh_dialog(self):
        if self.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        self.push_mesh_history()
        self.statusBar().showMessage("Draw a plane to slice the mesh. Press Apply or Cancel.")
        plane_widget = self.plotter.add_plane_widget(
            callback=None, color="yellow", origin=self.mesh.center, normal=(0,0,1), assign_to_axis='z', implicit=True
        )
        def apply_slice():
            origin = plane_widget.origin
            normal = plane_widget.normal
            sliced = self.mesh.slice(normal=normal, origin=origin)
            self.mesh = sliced
            self.plotter.remove_actor(plane_widget)
            self.update_mesh_display()
            self.statusBar().showMessage("Mesh sliced.")
        def cancel_slice():
            self.plotter.remove_actor(plane_widget)
            self.statusBar().showMessage("Slice cancelled.")
        btns = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel, self)
        btns.accepted.connect(apply_slice)
        btns.rejected.connect(cancel_slice)
        dlg = QDialog(self)
        dlg.setWindowTitle("Slice Mesh")
        layout = QVBoxLayout(dlg)
        layout.addWidget(QLabel("Draw a plane to slice the mesh. Adjust and press Apply."))
        layout.addWidget(btns)
        dlg.exec()

    def save_session(self):
        file_path, _ = QFileDialog.getSaveFileName(self, "Save Session", os.path.join(self.last_dir or "", "session.json"), "JSON Files (*.json)")
        if not file_path:
            return
        try:
            session = {
                'mesh': self.mesh.save(None) if self.mesh is not None else None,
                'annotations': self.annotations,
                'colormap': self.colormap,
                'slab_thickness': self.slab_thickness,
                'mesh_vis_mode': self.mesh_vis_mode,
                'analysis_direction': self.analysis_direction
            }
            with open(file_path, "w") as f:
                json.dump(session, f)
            self.statusBar().showMessage(f"Session saved to {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Save Error", f"Failed to save session:\n{e}")

    def load_session(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Load Session", self.last_dir or "", "JSON Files (*.json)")
        if not file_path:
            return
        try:
            with open(file_path, "r") as f:
                session = json.load(f)
            if session.get('mesh'):
                self.mesh = pv.read(session['mesh'])
            self.annotations = session.get('annotations', [])
            self.colormap = session.get('colormap', 'viridis')
            self.slab_thickness = session.get('slab_thickness', 0.10)
            self.mesh_vis_mode = session.get('mesh_vis_mode', 'surface+edge')
            self.analysis_direction = session.get('analysis_direction', 1)
            self.update_mesh_display()
            self.update_mesh_info()
            self.statusBar().showMessage(f"Session loaded from {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Load Error", f"Failed to load session:\n{e}")

    def batch_process_dialog(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("Batch Mesh Processing")
        layout = QVBoxLayout(dialog)
        file_btn = QPushButton("Select Mesh Files")
        file_list = QListWidget()
        layout.addWidget(file_btn)
        layout.addWidget(file_list)
        def select_files():
            files, _ = QFileDialog.getOpenFileNames(self, "Select Mesh Files", self.last_dir or "", "Mesh Files (*.stl *.obj *.ply *.vtp *.vtk)")
            if files:
                file_list.clear()
                file_list.addItems(files)
        file_btn.clicked.connect(select_files)
        btns = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        btns.accepted.connect(dialog.accept)
        btns.rejected.connect(dialog.reject)
        layout.addWidget(btns)
        if not dialog.exec():
            return
        files = [file_list.item(i).text() for i in range(file_list.count())]
        if not files:
            return
        progress = QProgressDialog("Batch processing...", None, 0, len(files), self)
        progress.setWindowTitle("Batch Processing")
        progress.setWindowModality(Qt.ApplicationModal)
        progress.setMinimumDuration(0)
        progress.setValue(0)
        for i, fpath in enumerate(files):
            try:
                mesh = pv.read(fpath)
                mesh = clean_mesh(mesh)
                # Optionally more processing here
                out_path = os.path.splitext(fpath)[0] + "_processed.vtp"
                mesh.save(out_path)
                self.history_list.addItem(f"Processed: {os.path.basename(fpath)} -> {os.path.basename(out_path)}")
            except Exception as e:
                self.history_list.addItem(f"Error processing {os.path.basename(fpath)}: {e}")
            progress.setValue(i+1)
        QMessageBox.information(self, "Batch Complete", "Batch processing finished.")

    def add_annotation_point(self):
        if self.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        self.statusBar().showMessage("Click a point on the mesh to annotate.")
        def callback(point):
            ann = {'type': 'point', 'data': list(point)}
            self.annotations.append(ann)
            self.ann_list.addItem(f"Point: {point}")
            actor = self.plotter.add_mesh(pv.Sphere(center=point, radius=0.01*self.mesh.length), color='red')
            if not hasattr(self, 'annotation_actors'):
                self.annotation_actors = []
            self.annotation_actors.append(actor)
            self.plotter.render()
        self.plotter.enable_point_picking(callback=callback, use_mesh=True, show_message=False, show_point=True, left_clicking=True)

    def add_annotation_line(self):
        if self.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        self.statusBar().showMessage("Pick 2 points to define a line annotation.")
        picked = []
        def callback(point):
            picked.append(point)
            if len(picked) == 2:
                ann = {'type': 'line', 'data': [list(picked[0]), list(picked[1])]}
                self.annotations.append(ann)
                self.ann_list.addItem(f"Line: {picked[0]} -> {picked[1]}")
                line = pv.Line(picked[0], picked[1])
                actor = self.plotter.add_mesh(line, color='blue', line_width=4)
                if not hasattr(self, 'annotation_actors'):
                    self.annotation_actors = []
                self.annotation_actors.append(actor)
                self.plotter.render()
                self.plotter.disable_picking()
        self.plotter.enable_point_picking(callback=callback, use_mesh=True, show_message=False, show_point=True, left_clicking=True)

    def add_annotation_text(self):
        if self.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        text, ok = QInputDialog.getText(self, "Add Text Annotation", "Text:")
        if not ok or not text:
            return
        self.statusBar().showMessage("Click a point to place the text annotation.")
        def callback(point):
            ann = {'type': 'text', 'data': {'pos': list(point), 'text': text}}
            self.annotations.append(ann)
            self.ann_list.addItem(f"Text: '{text}' at {point}")
            actor = self.plotter.add_point_labels([point], [text], point_size=0, font_size=16, text_color='green')
            if not hasattr(self, 'annotation_actors'):
                self.annotation_actors = []
            self.annotation_actors.append(actor)
            self.plotter.render()
        self.plotter.enable_point_picking(callback=callback, use_mesh=True, show_message=False, show_point=True, left_clicking=True)

    def export_annotations(self):
        if not self.annotations:
            QMessageBox.warning(self, "No Annotations", "No annotations to export.")
            return
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Annotations", os.path.join(self.last_dir or "", "annotations.csv"), "CSV Files (*.csv)")
        if not file_path:
            return
        try:
            with open(file_path, "w", newline="") as f:
                writer = csv.writer(f)
                writer.writerow(["type", "data"])
                for ann in self.annotations:
                    writer.writerow([ann['type'], json.dumps(ann['data'])])
            self.statusBar().showMessage(f"Annotations exported to {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Failed to export annotations:\n{e}")

    def toggle_projection_mode(self):
        if self.plotter.camera.GetParallelProjection():
            self.plotter.camera.ParallelProjectionOff()
            self.statusBar().showMessage("Perspective projection enabled.")
        else:
            self.plotter.camera.ParallelProjectionOn()
            self.statusBar().showMessage("Orthographic projection enabled.")
        self.plotter.render()

    def set_standard_view(self, view):
        views = {
            'top': (0, 0, 1),
            'bottom': (0, 0, -1),
            'left': (-1, 0, 0),
            'right': (1, 0, 0),
            'front': (0, 1, 0),
            'back': (0, -1, 0)
        }
        if view in views:
            self.plotter.view_vector(views[view])
            self.plotter.reset_camera()
            self.plotter.render()
            self.statusBar().showMessage(f"View set to {view}.")
        else:
            QMessageBox.warning(self, "Invalid View", f"Unknown view: {view}")

    def reset_view(self):
        self.plotter.reset_camera()
        self.plotter.render()
        self.statusBar().showMessage("View reset.")

    def load_mesh(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Load 3D Mesh", self.last_dir or "", "Mesh Files (*.stl *.obj *.ply *.vtp *.vtk)")
        if not file_path:
            return
        try:
            mesh = pv.read(file_path)
            self.mesh = mesh
            self.last_dir = os.path.dirname(file_path)
            self.picked_points = []
            self.picked_point_actors = []
            # Try to auto-load texture for OBJ/PLY if present
            self.texture = None
            if file_path.lower().endswith(('.obj', '.ply')):
                tex_path = os.path.splitext(file_path)[0] + '.jpg'
                if os.path.exists(tex_path):
                    try:
                        self.texture = pv.read_texture(tex_path)
                    except Exception:
                        self.texture = None
            self.update_mesh_display()
            self.update_mesh_info()
            self.statusBar().showMessage(f"Loaded mesh: {os.path.basename(file_path)}")
        except Exception as e:
            QMessageBox.critical(self, "Load Error", f"Failed to load mesh:\n{e}")

    def update_mesh_display(self):
        self.plotter.clear()
        mesh = self.mesh
        if mesh is not None:
            # Show texture if present
            if hasattr(self, 'texture') and self.texture is not None:
                self.plotter.add_mesh(mesh, texture=self.texture, show_edges=(self.mesh_vis_mode=="surface+edge"), name="mesh")
            else:
                # Try to use color arrays if present
                color_arrays = ['RGB', 'Colors', 'rgba', 'red', 'green', 'blue']
                found = False
                for arr_name in color_arrays:
                    if arr_name in mesh.point_data:
                        arr = mesh.point_data[arr_name]
                        if arr.shape[0] == mesh.n_points:
                            self.plotter.add_mesh(mesh, scalars=arr, rgb=(arr.shape[1] in [3,4]), show_edges=(self.mesh_vis_mode=="surface+edge"), name="mesh")
                            found = True
                            break
                    elif arr_name in mesh.cell_data:
                        arr = mesh.cell_data[arr_name]
                        if arr.shape[0] == mesh.n_cells:
                            self.plotter.add_mesh(mesh, scalars=arr, rgb=(arr.shape[1] in [3,4]), show_edges=(self.mesh_vis_mode=="surface+edge"), name="mesh")
                            found = True
                            break
                if not found:
                    # Fallback to default color
                    mode = getattr(self, 'mesh_vis_mode', 'surface+edge')
                    if mode == "surface+edge":
                        self.plotter.add_mesh(mesh, show_edges=True, color='lightgray', name="mesh")
                    elif mode == "surface":
                        self.plotter.add_mesh(mesh, show_edges=False, color='lightgray', name="mesh")
                    elif mode == "points":
                        self.plotter.add_mesh(mesh, render_points_as_spheres=True, point_size=5, color='black', name="mesh")
                    elif mode == "wireframe":
                        self.plotter.add_mesh(mesh, style='wireframe', color='black', name="mesh")
        self.plotter.add_axes()
        self.plotter.reset_camera()
        self.plotter.render()

    def update_mesh_info(self):
        if self.mesh is not None:
            n_points = self.mesh.n_points
            n_faces = self.mesh.n_faces if hasattr(self.mesh, 'n_faces') else self.mesh.n_cells
            bounds = self.mesh.bounds
            info = f"Points: {n_points}\nFaces: {n_faces}\nBounds: {bounds}"
            self.info_text.setPlainText(info)
        else:
            self.info_text.setPlainText("")

    def start_point_picking(self):
        if self.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        self.picked_points = []
        # Remove previous picked point actors
        for actor in getattr(self, 'picked_point_actors', []):
            self.plotter.remove_actor(actor)
        self.picked_point_actors = []
        self.statusBar().showMessage("Pick 3 points on the mesh to define the reference plane.")
        def callback(point):
            if len(self.picked_points) >= 3:
                return
            self.picked_points.append(np.array(point))
            # Show marker
            marker = pv.Sphere(radius=0.01 * np.linalg.norm(self.mesh.length), center=point)
            actor = self.plotter.add_mesh(marker, color='magenta', name=f"picked_point_{len(self.picked_points)}")
            self.picked_point_actors.append(actor)
            if len(self.picked_points) < 3:
                self.statusBar().showMessage(f"Picked point {len(self.picked_points)}. Pick {3-len(self.picked_points)} more.")
            if len(self.picked_points) == 3:
                self.plotter.disable_picking()
                self.statusBar().showMessage("3 points picked. You can now show the deformation heatmap.")
        self.plotter.enable_point_picking(callback=callback, use_mesh=True, show_message=False, show_point=False, left_clicking=True)

    def load_texture(self):
        if self.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        file_path, _ = QFileDialog.getOpenFileName(self, "Select texture", self.last_dir or "", "Images (*.jpg *.jpeg *.png *.bmp *.tif *.tiff)")
        if file_path:
            try:
                self.texture = pv.read_texture(file_path)
                self.update_mesh_display()
                self.plotter.reset_camera()
                QMessageBox.information(self, "Texture loaded", f"Texture applied: {os.path.basename(file_path)}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Error loading texture:\n{str(e)}")

    def save_screenshot(self):
        file_path, _ = QFileDialog.getSaveFileName(self, "Save Screenshot", os.path.join(self.last_dir or '', "screenshot.png"), "PNG Files (*.png)")
        if not file_path:
            return
        try:
            # Save at 4x window resolution for high-res
            w, h = self.plotter.width(), self.plotter.height()
            self.plotter.screenshot(file_path, window_size=(w*4, h*4), return_img=False)
            QMessageBox.information(self, "Screenshot Saved", f"Screenshot saved to {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Screenshot Error", f"Failed to save screenshot:\n{e}")

    def reset_picking(self):
        """Clear picked points, markers, and overlays related to plane picking."""
        self.picked_points = []
        # Remove point markers from plotter
        for actor in getattr(self, 'picked_point_actors', []):
            try:
                self.plotter.remove_actor(actor)
            except Exception:
                pass
        self.picked_point_actors = []
        # Optionally remove overlays related to picking (e.g., plane, arrows)
        if hasattr(self, 'plane_actor') and self.plane_actor:
            try:
                self.plotter.remove_actor(self.plane_actor)
            except Exception:
                pass
            self.plane_actor = None
        self.statusBar().showMessage("Picking reset. Select 3 points to define a plane.")
        self.plotter.render()

    def flip_analysis_direction(self):
        """Flip the analysis direction (+normal/-normal) and update the UI."""
        self.analysis_direction = -1 if getattr(self, 'analysis_direction', 1) == 1 else 1
        self.statusBar().showMessage(f"Analysis direction set to {'+' if self.analysis_direction == 1 else '-'}normal.")
        # Optionally refresh deformation heatmap if already shown
        if hasattr(self, '_deformation_data') and self.mesh is not None and len(self.picked_points) == 3:
            self.calculate_and_show_deformation()

    def export_mesh_dialog(self):
        """Export the current mesh in various formats (STL, OBJ, PLY, VTK, VTP, glTF)."""
        if self.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        filters = (
            "All Supported (*.stl *.obj *.ply *.vtk *.vtp *.glb *.gltf);;"
            "STL Files (*.stl);;OBJ Files (*.obj);;PLY Files (*.ply);;VTK Files (*.vtk);;VTP Files (*.vtp);;glTF Files (*.glb *.gltf)"
        )
        file_path, sel_filter = QFileDialog.getSaveFileName(self, "Export Mesh", os.path.join(self.last_dir, "mesh.vtp"), filters)
        if not file_path:
            return
        ext = os.path.splitext(file_path)[1].lower()
        try:
            if ext in [".stl", ".obj", ".ply", ".vtk", ".vtp"]:
                if meshio is not None:
                    meshio.write(file_path, self.mesh)
                else:
                    self.mesh.save(file_path)
            elif ext in [".glb", ".gltf"]:
                self.export_gltf_dialog()
                return
            else:
                QMessageBox.warning(self, "Export Error", f"Unsupported file extension: {ext}")
                return
            QMessageBox.information(self, "Export Complete", f"Mesh exported to {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Failed to export mesh: {e}\n{traceback.format_exc()}")