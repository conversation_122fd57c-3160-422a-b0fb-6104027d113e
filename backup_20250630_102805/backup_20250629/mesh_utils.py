# mesh_utils.py
"""
Mesh processing utilities for Deformation Viewer 3D
"""
import pyvista as pv
import numpy as np

def clean_mesh(mesh):
    return mesh.clean()

def remove_small_components(mesh):
    sizes = mesh.connectivity(largest=False).cell_data['RegionId']
    unique, counts = np.unique(sizes, return_counts=True)
    largest_region = unique[np.argmax(counts)]
    mask = sizes == largest_region
    return mesh.extract_cells(mask)

def fill_small_holes(mesh, max_area):
    return mesh.fill_holes(max_area)

def smooth_mesh(mesh, n_iter=30, relaxation=0.01):
    return mesh.smooth(n_iter=n_iter, relaxation_factor=relaxation)

def curvature_analysis(mesh, curv_type='mean', smooth_iter=0):
    if smooth_iter > 0:
        mesh = mesh.smooth(n_iter=smooth_iter, relaxation_factor=0.01)
    curv = mesh.curvature(curv_type=curv_type)
    mesh['curvature'] = curv
    return mesh
