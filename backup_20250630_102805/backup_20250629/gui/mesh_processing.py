from PySide6.QtWidgets import <PERSON><PERSON><PERSON>og, QVBoxLayout, QHBoxLayout, QCheckBox, QLabel, QDoubleSpinBox, QSpinBox, QDialogButtonBox

class MeshProcessingDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Mesh Processing Options")
        layout = QVBoxLayout(self)
        # Clean mesh
        self.clean_cb = QCheckBox("Clean mesh (remove duplicate points, degenerate faces)")
        self.clean_cb.setChecked(True)
        layout.addWidget(self.clean_cb)
        # Remove small components
        self.components_cb = QCheckBox("Remove small components")
        layout.addWidget(self.components_cb)
        # Fill small holes
        holes_layout = QHBoxLayout()
        self.holes_cb = QCheckBox("Fill small holes")
        self.hole_size_spin = QDoubleSpinBox()
        self.hole_size_spin.setRange(0.01, 10000.0)
        self.hole_size_spin.setValue(100.0)
        self.hole_size_spin.setSuffix(" area")
        holes_layout.addWidget(self.holes_cb)
        holes_layout.addWidget(QLabel("Max Area:"))
        holes_layout.addWidget(self.hole_size_spin)
        layout.addLayout(holes_layout)
        # Smooth mesh
        smooth_layout = QHBoxLayout()
        self.smooth_cb = QCheckBox("Smooth mesh (Laplacian)")
        self.smooth_iter_spin = QSpinBox()
        self.smooth_iter_spin.setRange(1, 100)
        self.smooth_iter_spin.setValue(20)
        self.smooth_relax_spin = QDoubleSpinBox()
        self.smooth_relax_spin.setRange(0.001, 1.0)
        self.smooth_relax_spin.setValue(0.01)
        self.smooth_relax_spin.setSingleStep(0.01)
        smooth_layout.addWidget(self.smooth_cb)
        smooth_layout.addWidget(QLabel("Iter:"))
        smooth_layout.addWidget(self.smooth_iter_spin)
        smooth_layout.addWidget(QLabel("Relax:"))
        smooth_layout.addWidget(self.smooth_relax_spin)
        layout.addLayout(smooth_layout)
        # Decimate
        decimate_layout = QHBoxLayout()
        self.decimate_cb = QCheckBox("Decimate mesh (reduce # faces)")
        self.decimate_slider = QSpinBox()
        self.decimate_slider.setRange(1, 99)
        self.decimate_slider.setValue(50)
        decimate_layout.addWidget(self.decimate_cb)
        decimate_layout.addWidget(QLabel("Target: % faces"))
        decimate_layout.addWidget(self.decimate_slider)
        layout.addLayout(decimate_layout)
        # Remesh
        remesh_layout = QHBoxLayout()
        self.remesh_cb = QCheckBox("Remesh (isotropic, experimental)")
        self.remesh_size = QDoubleSpinBox()
        self.remesh_size.setRange(0.001, 1.0)
        self.remesh_size.setValue(0.05)
        remesh_layout.addWidget(self.remesh_cb)
        remesh_layout.addWidget(QLabel("Target edge length:"))
        remesh_layout.addWidget(self.remesh_size)
        layout.addLayout(remesh_layout)
        # Dialog buttons
        btns = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        btns.accepted.connect(self.accept)
        btns.rejected.connect(self.reject)
        layout.addWidget(btns)

    def get_selected_filters(self):
        return {
            'clean': self.clean_cb.isChecked(),
            'components': self.components_cb.isChecked(),
            'holes': self.holes_cb.isChecked(),
            'hole_size': self.hole_size_spin.value(),
            'smooth': self.smooth_cb.isChecked(),
            'smooth_iter': self.smooth_iter_spin.value(),
            'smooth_relax': self.smooth_relax_spin.value(),
            'decimate': self.decimate_cb.isChecked(),
            'decimate_target': self.decimate_slider.value(),
            'remesh': self.remesh_cb.isChecked(),
            'remesh_size': self.remesh_size.value(),
        }
