from PySide6.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton, QHBoxLayout

class PlaneDefinitionChoiceDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Define Analysis Plane")
        self.setMinimumWidth(300)
        layout = QVBoxLayout(self)
        layout.addWidget(QLabel("Choose how to define the analysis plane:"))
        btns = QHBoxLayout()
        self.btn_3points = QPushButton("Define by 3 Points")
        self.btn_canonical = QPushButton("Canonical Plane (XY/YZ/ZX)")
        btns.addWidget(self.btn_3points)
        btns.addWidget(self.btn_canonical)
        layout.addLayout(btns)
        self.btn_3points.clicked.connect(lambda: self.done(1))
        self.btn_canonical.clicked.connect(lambda: self.done(2))
