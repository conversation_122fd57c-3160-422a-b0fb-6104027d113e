import sys
import os
# Configurazione ambiente per NVIDIA (deve essere PRIMA di qualsiasi import Qt/PyVista)
os.environ["__GLX_VENDOR_LIBRARY_NAME"] = "nvidia"
os.environ["__NV_PRIME_RENDER_OFFLOAD"] = "1"
os.environ["DISPLAY"] = ":0"

import numpy as np
import pyvista as pv
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, 
    QLabel, QFileDialog, QMessageBox, QHBoxLayout, QSpacerItem, QSizePolicy, QDialog, QFormLayout, QDoubleSpinBox, QDialogButtonBox, QCheckBox, QComboBox, QSpinBox, QGroupBox, QFrame, QToolBar, QInputDialog
)
from PySide6.QtCore import Qt
from pyvistaqt import QtInteractor
from PySide6.QtGui import QIcon, QAction

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Masonry Deformation Analysis (PyVista+NVIDIA)")
        self.setGeometry(100, 100, 900, 700)
        # --- Menu Bar ---
        menubar = self.menuBar()
        # File menu
        file_menu = menubar.addMenu("File")
        action_load_model = QAction("Load 3D Model", self)
        action_load_model.triggered.connect(self.load_model)
        file_menu.addAction(action_load_model)
        action_load_texture = QAction("Load Texture", self)
        action_load_texture.triggered.connect(self.load_texture)
        file_menu.addAction(action_load_texture)
        file_menu.addSeparator()
        action_screenshot = QAction("Save Screenshot", self)
        action_screenshot.triggered.connect(self.save_screenshot)
        file_menu.addAction(action_screenshot)
        file_menu.addSeparator()
        action_exit = QAction("Exit", self)
        action_exit.triggered.connect(self.close)
        file_menu.addAction(action_exit)
        # Process Mesh menu
        process_menu = menubar.addMenu("Process Mesh")
        action_clean_mesh = QAction("Clean Mesh", self)
        action_clean_mesh.triggered.connect(self.show_clean_mesh_dialog)
        process_menu.addAction(action_clean_mesh)
        # --- Collapsible sidebar setup ---
        self.sidebar_widget = QWidget()
        self.sidebar_layout = QVBoxLayout(self.sidebar_widget)
        self.sidebar_layout.setContentsMargins(5, 5, 5, 5)
        self.sidebar_layout.setSpacing(8)
        # Toggle button for sidebar (move outside sidebar)
        self.btn_toggle_sidebar = QPushButton()
        self.btn_toggle_sidebar.setIcon(QIcon.fromTheme("go-previous"))
        self.btn_toggle_sidebar.setToolTip("Show/Hide controls")
        self.btn_toggle_sidebar.setCheckable(True)
        self.btn_toggle_sidebar.setChecked(True)
        self.btn_toggle_sidebar.setFixedWidth(28)
        self.btn_toggle_sidebar.clicked.connect(self.toggle_sidebar)
        # --- Group boxes for sections ---
        # Plane/analysis controls
        self.plane_controls_layout = QVBoxLayout()
        # --- Plane selection for region of interest ---
        self.btn_define_plane = QPushButton("Define Plane (3 Points)")
        self.btn_define_plane.setToolTip("Pick 3 points to define the analysis plane.")
        self.plane_controls_layout.addWidget(self.btn_define_plane)
        # Remove Last Point button
        self.btn_remove_last_point = QPushButton("Remove Last Point")
        self.btn_remove_last_point.setToolTip("Remove the last picked point.")
        self.btn_remove_last_point.clicked.connect(self.remove_last_point)
        self.plane_controls_layout.addWidget(self.btn_remove_last_point)
        # Slab thickness control
        self.thickness_label = QLabel("Slab Thickness:")
        self.thickness_spin = QDoubleSpinBox()
        self.thickness_spin.setRange(0.001, 10.0)
        self.thickness_spin.setSingleStep(0.01)
        self.thickness_spin.setValue(0.10)
        self.thickness_spin.setSuffix(" m")
        self.thickness_spin.setToolTip("Thickness of the region (slab) around the plane.")
        self.plane_controls_layout.addWidget(self.thickness_label)
        self.plane_controls_layout.addWidget(self.thickness_spin)
        # Show Slab Region checkbox
        self.chk_show_slab = QCheckBox("Show Slab Region")
        self.chk_show_slab.setChecked(True)
        self.chk_show_slab.stateChanged.connect(self.toggle_slab_visibility)
        self.plane_controls_layout.addWidget(self.chk_show_slab)
        # Calculate and Visualize buttons
        self.btn_calculate = QPushButton("Calculate Deformations")
        self.btn_calculate.clicked.connect(self.calculate_deformations)
        self.plane_controls_layout.addWidget(self.btn_calculate)
        self.btn_visualize = QPushButton("Show 3D Heatmap")
        self.btn_visualize.clicked.connect(self.visualize_heatmap)
        self.plane_controls_layout.addWidget(self.btn_visualize)
        self.plane_controls_layout.addStretch(1)
        plane_group = QGroupBox("Region Analysis")
        plane_group.setLayout(self.plane_controls_layout)
        # Visualization controls
        self.vis_controls_layout = QVBoxLayout()
        # --- Colormap controls (moved here) ---
        self.cmap_label = QLabel("Colormap:")
        self.cmap_combo = QComboBox()
        self.cmap_combo.addItems([
            "viridis", "plasma", "inferno", "magma", "cividis", "coolwarm", "RdBu", "jet", "gray"
        ])
        self.cmap_combo.setCurrentText("viridis")
        self.cmap_combo.currentTextChanged.connect(self.on_cmap_changed)
        self.n_colors_label = QLabel("# Colors:")
        self.n_colors_spin = QSpinBox()
        self.n_colors_spin.setRange(2, 20)
        self.n_colors_spin.setValue(10)
        self.n_colors_spin.valueChanged.connect(self.on_n_colors_changed)
        self.vis_controls_layout.addWidget(self.cmap_label)
        self.vis_controls_layout.addWidget(self.cmap_combo)
        self.vis_controls_layout.addWidget(self.n_colors_label)
        self.vis_controls_layout.addWidget(self.n_colors_spin)
        # --- Background color control ---
        self.bg_label = QLabel("Background:")
        self.bg_combo = QComboBox()
        self.bg_combo.addItems(["Gray", "White", "Black", "Light Blue"])
        self.bg_combo.setCurrentText("Light Blue")
        self.bg_combo.currentTextChanged.connect(self.on_bg_color_changed)
        self.vis_controls_layout.addWidget(self.bg_label)
        self.vis_controls_layout.addWidget(self.bg_combo)
        # --- View direction controls as ComboBox ---
        self.view_label = QLabel("View Direction:")
        self.view_combo = QComboBox()
        self.view_combo.addItems([
            "Top", "Bottom", "Left", "Right", "Front", "Back"
        ])
        self.view_combo.setCurrentText("Front")
        self.view_combo.currentTextChanged.connect(self.on_view_combo_changed)
        self.vis_controls_layout.addWidget(self.view_label)
        self.vis_controls_layout.addWidget(self.view_combo)
        # --- Projection controls as ComboBox ---
        self.proj_label = QLabel("Projection Mode:")
        self.proj_combo = QComboBox()
        self.proj_combo.addItems(["Perspective", "Orthographic"])
        self.proj_combo.setCurrentText("Perspective")
        self.proj_combo.currentTextChanged.connect(self.on_proj_combo_changed)
        self.vis_controls_layout.addWidget(self.proj_label)
        self.vis_controls_layout.addWidget(self.proj_combo)
        # --- Reset deformation/heatmap button ---
        self.btn_reset_deformation = QPushButton("Reset Deformation/Heatmap")
        self.btn_reset_deformation.setToolTip("Clear deformation results and heatmap, show plain mesh.")
        self.btn_reset_deformation.clicked.connect(self.reset_deformation)
        self.vis_controls_layout.addWidget(self.btn_reset_deformation)
        # --- Model scaling controls ---
        self.scale_group = QGroupBox("Model Scaling")
        self.scale_layout = QVBoxLayout()
        # Global scale
        self.global_scale_label = QLabel("Global Scale Factor:")
        self.global_scale_spin = QDoubleSpinBox()
        self.global_scale_spin.setRange(0.001, 10000.0)
        self.global_scale_spin.setSingleStep(0.01)
        self.global_scale_spin.setValue(1.0)
        self.global_scale_spin.setDecimals(4)
        self.global_scale_spin.setToolTip("Multiply all coordinates by this factor.")
        self.global_scale_spin.valueChanged.connect(self.apply_global_scale)
        self.scale_layout.addWidget(self.global_scale_label)
        self.scale_layout.addWidget(self.global_scale_spin)
        # 2-point scale
        self.btn_2pt_scale = QPushButton("2-Point Scale Tool")
        self.btn_2pt_scale.setToolTip("Pick 2 points, enter real distance, and rescale model.")
        self.btn_2pt_scale.clicked.connect(self.start_2pt_scale)
        self.scale_layout.addWidget(self.btn_2pt_scale)
        self.scale_group.setLayout(self.scale_layout)
        self.vis_controls_layout.addWidget(self.scale_group)
        self.vis_controls_layout.addStretch(1)
        vis_group = QGroupBox("Visualization")
        vis_group.setLayout(self.vis_controls_layout)
        # Add only Region Analysis and Visualization groups to sidebar
        self.sidebar_layout.addWidget(plane_group)
        self.sidebar_layout.addWidget(vis_group)
        self.sidebar_layout.addStretch(1)
        # --- Top controls: Colormap only (remove direction combo) ---
        # self.top_controls_layout = QHBoxLayout()
        # self.top_controls_layout.setContentsMargins(0, 0, 0, 0)
        # self.top_controls_layout.setSpacing(6)
        # self.top_controls_layout.addWidget(self.cmap_label)
        # self.top_controls_layout.addWidget(self.cmap_combo)
        # self.sidebar_layout.insertLayout(0, self.top_controls_layout)
        # --- Main layout: toggle button + sidebar + 3D view ---
        self.central_layout = QHBoxLayout()
        self.central_layout.setContentsMargins(0, 0, 0, 0)
        self.central_layout.setSpacing(0)
        self.central_layout.addWidget(self.btn_toggle_sidebar, alignment=Qt.AlignLeft)
        self.central_layout.addWidget(self.sidebar_widget)
        # 3D view area
        self.viewer_frame = QFrame()
        self.viewer_layout = QVBoxLayout(self.viewer_frame)
        self.viewer_layout.setContentsMargins(0, 0, 0, 0)
        self.viewer_layout.setSpacing(0)
        # Main title label (move to top of viewer)
        title_label = QLabel("3D Masonry Deformation Analysis")
        font = title_label.font()
        font.setPointSize(16)
        font.setBold(True)
        title_label.setFont(font)
        title_label.setAlignment(Qt.AlignCenter)
        self.viewer_layout.addWidget(title_label)
        self.viewer_layout.addSpacing(10)
        # PyVista interactor
        self.plotter = QtInteractor(self.viewer_frame)
        self.plotter.enable_trackball_style()
        self.viewer_layout.addWidget(self.plotter.interactor)
        self.central_layout.addWidget(self.viewer_frame, stretch=1)
        # --- Missing: Initialize main_widget before using it ---
        self.main_widget = QWidget()
        self.main_widget.setLayout(self.central_layout)
        self.setCentralWidget(self.main_widget)
        # Set background color for main widget
        self.main_widget.setStyleSheet("background-color: #f7f7fa;")

        # Add global axes widget to bottom right (persistent)
        self.plotter.add_axes(interactive=False, line_width=2, color='black', x_color='red', y_color='green', z_color='blue')

    # Collega i segnali ai metodi
        self.btn_define_plane.clicked.connect(self.define_plane)
        self.btn_remove_last_point.clicked.connect(self.remove_last_point)
        self.btn_calculate.clicked.connect(self.calculate_deformations)
        self.btn_visualize.clicked.connect(self.visualize_heatmap)
        self.btn_reset_deformation.clicked.connect(self.reset_deformation)
        self.cmap_combo.currentTextChanged.connect(self.on_cmap_changed)
        self.n_colors_spin.valueChanged.connect(self.on_n_colors_changed)
        # self.main_layout.addLayout(self.controls_layout)  # REMOVED: obsolete, sidebar now used
        # Inizializza il plotter PyVista e aggiungi l'interactor al layout
        # self.plotter = QtInteractor(self.main_widget)  # REMOVE this duplicate plotter
        # self.plotter.enable_trackball_style()
        # self.main_layout.addWidget(self.plotter.interactor)
        # self.setCentralWidget(self.main_widget)
        # Configura tema PyVista
        pv.set_plot_theme("document")
        self.plotter.set_background("white")
        self.plotter.enable_anti_aliasing()

        # Variabili di stato (allineate a 1.py)
        self.mesh = None
        self.plane_points = []
        self.point_labels = []
        self.deformations = None
        self.texture = None
        self.use_shading = True
        self.picking_enabled = False
        self.plane = None
        self.arrow = None
        self.show_plane = True
        self.heatmap_direction = 1
        self.picked_points_actors = []
        self.label_actors = []
        self.bbox_actor = None
        self.bbox_bounds = None
        self.bbox_visible = False
        self._box_widget_active = False
        self.show_edges = False

        # Colormap selector
        self.cmap_label = QLabel("Colormap:")
        self.cmap_combo = QComboBox()
        self.cmap_combo.addItems([
            "viridis", "plasma", "inferno", "magma", "cividis", "coolwarm", "RdBu", "jet", "gray"
        ])
        self.cmap_combo.setCurrentText("viridis")
        self.current_cmap = "viridis"
        self.cmap_combo.currentTextChanged.connect(self.on_cmap_changed)
        # Discretization control
        self.n_colors_label = QLabel("# Colors:")
        self.n_colors_spin = QSpinBox()
        self.n_colors_spin.setRange(2, 20)
        self.n_colors_spin.setValue(10)
        self.n_colors_spin.valueChanged.connect(self.on_n_colors_changed)
        self.n_colors = 10

    def retry_point_picking(self):
        self.btn_retry_pick.setEnabled(False)
        self.plotter.enable_point_picking(
            callback=self.on_plane_points_picked,
            show_message=False,
            color="red",
            point_size=10,
            left_clicking=True,
            show_point=True
        )

    def reset_points(self, reset_camera=True):
        """Reset all selected points"""
        self.plane_points = []
        self.point_labels = []
        self.plotter.remove_actor("plane_points")
        for i in range(3):
            self.plotter.remove_actor(f"label_{i}")
        if hasattr(self, 'plane_actor') and self.plane_actor is not None:
            self.plotter.remove_actor(self.plane_actor)
            self.plane_actor = None
        if self.arrow is not None:
            self.plotter.remove_actor(self.arrow)
            self.arrow = None
        # If picking was enabled, re-enable it so user can continue
        if self.picking_enabled:
            self.plotter.disable_picking()
            self.plotter.enable_point_picking(
                callback=self.on_plane_points_picked,
                show_message=False,
                color="red",
                point_size=10,
                left_clicking=True,
                show_point=True
            )
        # Only reset camera if requested
        self.show_current_mesh(reset_camera=reset_camera)
        QMessageBox.information(self, "Points Reset", "All points have been removed.")

    def reset_deformation(self):
        self.deformations = None
        self.show_current_mesh()
        # Ensure mesh is shown with a visible color (light gray) after reset
        if self.mesh is not None:
            self.plotter.add_mesh(
                self.mesh,
                color="lightgray",
                show_edges=self.show_edges,
                smooth_shading=self.use_shading,
                name="mesh_reset"
            )
            self.plotter.render()
        QMessageBox.information(self, "Reset", "Deformation and heatmap have been cleared. Plain mesh is shown.")

    def define_plane(self):
        """Define a plane by selecting 3 points"""
        if self.mesh is None:
            QMessageBox.warning(self, "Warning", "Please load a model first!")
            return
        QMessageBox.information(
            self,
            "Camera Guidance",
            "Rotate the camera to face the wall directly before picking points.\n\nClick OK to start picking."
        )
        self.reset_points(reset_camera=False)  # Do not reset camera when starting picking
        self.plotter.disable_picking()
        self.picking_enabled = True
        self._hover_marker = None
        self.plotter.enable_point_picking(
            callback=self.on_plane_points_picked,
            show_message=False,
            color="yellow",
            point_size=20,
            left_clicking=True,
            show_point=True,
            use_mesh=True
        )
        # Connect mouse move event for hover marker
        self.plotter.interactor.AddObserver('MouseMoveEvent', self.on_mouse_move_pick, 1.0)
        QMessageBox.information(
            self, 
            "Plane Selection", 
            "Click 3 points in the 3D window to define the plane.\nUse Shift+Click for precise selection."
        )

    def on_mouse_move_pick(self, obj, event):
        # Only show hover marker if picking is enabled
        if not self.picking_enabled or self.mesh is None:
            return
        # Snap to nearest visible vertex within tolerance and show marker (no GetPicker)
        if not self.picking_enabled or self.mesh is None:
            return
        # Get mouse position in window coordinates
        mouse_x, mouse_y = self.plotter.interactor.GetEventPosition()
        # Get renderer size
        size = self.plotter.interactor.GetRenderWindow().GetSize()
        # Use mouse_y directly (no Y flip)
        display_x = mouse_x
        display_y = mouse_y
        # Get camera info
        camera = self.plotter.camera
        renderer = self.plotter.renderer
        # Get world coordinates for near and far points
        renderer.SetDisplayPoint(display_x, display_y, 0.0)
        renderer.DisplayToWorld()
        near_point = np.array(renderer.GetWorldPoint()[:3])
        renderer.SetDisplayPoint(display_x, display_y, 1.0)
        renderer.DisplayToWorld()
        far_point = np.array(renderer.GetWorldPoint()[:3])
        # Ray direction
        ray_dir = far_point - near_point
        ray_dir /= np.linalg.norm(ray_dir)
        # For each mesh point, compute distance to ray
        points = self.mesh.points
        diffs = points - near_point
        t = np.dot(diffs, ray_dir)
        proj = near_point + np.outer(t, ray_dir)
        dists = np.linalg.norm(points - proj, axis=1)
        # Only consider points in front of camera (t > 0)
        valid = t > 0
        if not np.any(valid):
            if self._hover_marker is not None:
                self.plotter.remove_actor(self._hover_marker)
                self._hover_marker = None
            return
        idx = np.argmin(np.where(valid, dists, np.inf))
        closest = points[idx]
        # Check normal faces camera
        normal = self.mesh.point_normals[idx]
        camera_pos = np.array(camera.GetPosition())
        to_camera = camera_pos - closest
        to_camera /= np.linalg.norm(to_camera)
        dot = np.dot(normal, to_camera)
        if dot < 0:
            if self._hover_marker is not None:
                self.plotter.remove_actor(self._hover_marker)
                self._hover_marker = None
            return
        # Snap if within tolerance
        tol = self.get_model_scale() * 0.01
        if dists[idx] < tol:
            # Show marker
            if self._hover_marker is not None:
                self.plotter.remove_actor(self._hover_marker)
            sphere = pv.Sphere(radius=tol*0.5, center=closest)
            self._hover_marker = self.plotter.add_mesh(sphere, color="lime", opacity=0.7, name="hover_marker", reset_camera=False)
            self.plotter.render()
        else:
            if self._hover_marker is not None:
                self.plotter.remove_actor(self._hover_marker)
                self._hover_marker = None

    def on_plane_points_picked(self, *args):
        # Robustly extract picked point from PyVista callback (supports multiple signatures)
        if not self.picking_enabled or self.mesh is None:
            return
        # Try to extract point from args
        point = None
        if len(args) == 1 and isinstance(args[0], (list, tuple, np.ndarray)):
            point = np.array(args[0])
        elif len(args) >= 2 and hasattr(args[0], 'points'):
            # Sometimes mesh, event
            if hasattr(args[0], 'points') and len(args[0].points) > 0:
                point = np.array(args[0].points[0])
        elif len(args) >= 2 and isinstance(args[0], (list, tuple, np.ndarray)):
            point = np.array(args[0])
        if point is None:
            QMessageBox.warning(self, "Picking Error", "Could not determine picked point. Please try again.")
            return
        # Find closest mesh point index
        idx = np.argmin(np.linalg.norm(self.mesh.points - point, axis=1))
        # Check if normal faces camera
        camera_pos = np.array(self.plotter.camera.GetPosition())
        picked_pos = self.mesh.points[idx]
        normal = self.mesh.point_normals[idx]
        to_camera = camera_pos - picked_pos
        to_camera /= np.linalg.norm(to_camera)
        dot = np.dot(normal, to_camera)
        if dot < 0:  # Normal points away from camera
            # Do not disable picking or force reset, just warn and let user retry
            QMessageBox.warning(self, "Picking Error", "The selected point is not visible (normal faces away from camera). Please pick a point on the visible surface.")
            self.plotter.enable_trackball_style()  # Re-enable camera interaction
            return
        model_scale = self.get_model_scale()
        min_dist = model_scale * 1e-3
        for prev in self.plane_points:
            if np.linalg.norm(point - np.array(prev)) < min_dist:
                # Do not disable picking or force reset, just warn and let user retry
                QMessageBox.warning(self, "Error", f"The selected point is too close to a previously chosen one (min {min_dist:.4g} units). Please pick a different point.")
                return
        self.plane_points.append(point)
        point_num = len(self.plane_points)
        pos = (float(point[0]), float(point[1]), float(point[2]))
        label = pv.Text3D(f"P{point_num:02d}", depth=0.01, center=pos, height=0.15)
        self.point_labels.append(label)
        # Remove previous 'plane_points' actor so all points are shown
        self.plotter.remove_actor("plane_points")
        self.plotter.add_mesh(
            pv.PolyData(self.plane_points),
            color="yellow",
            point_size=20,
            render_points_as_spheres=True,
            name="plane_points",
            reset_camera=False
        )
        for i, lbl in enumerate(self.point_labels):
            self.plotter.add_mesh(lbl, color="black", name=f"label_{i}", reset_camera=False)
        self.plotter.render()
        # Remove hover marker after pick
        if hasattr(self, '_hover_marker') and self._hover_marker is not None:
            self.plotter.remove_actor(self._hover_marker)
            self._hover_marker = None
        # After 3 points, show slab and disable picking
        if len(self.plane_points) == 3:
            self.show_slab_region()
            self.plotter.disable_picking()
            self.picking_enabled = False
            self.plotter.enable_trackball_style()

    def show_slab_region(self):
        """Display the slab region as a semi-transparent box after plane definition."""
        if len(self.plane_points) != 3:
            return
        p1, p2, p3 = np.array(self.plane_points)
        v1 = p2 - p1
        v2 = p3 - p1
        normal = np.cross(v1, v2)
        normal = normal / np.linalg.norm(normal)
        center = (p1 + p2 + p3) / 3
        thickness = self.thickness_spin.value()
        # Find bounding rectangle in plane coordinates
        plane_axes = np.vstack([v1/np.linalg.norm(v1), v2/np.linalg.norm(v2), normal]).T
        local_points = (np.array(self.plane_points) - center) @ plane_axes
        x_min, x_max = np.min(local_points[:,0]), np.max(local_points[:,0])
        y_min, y_max = np.min(local_points[:,1]), np.max(local_points[:,1])
        # Add margin
        margin = self.get_model_scale() * 0.02
        x_min -= margin
        x_max += margin
        y_min -= margin
        y_max += margin
        # Create box in local plane coordinates
        box = pv.Box(bounds=(x_min, x_max, y_min, y_max, -thickness/2, thickness/2))
        # Transform box to world coordinates
        transform = np.eye(4)
        transform[:3,:3] = plane_axes
        transform[:3,3] = center
        box = box.transform(transform, inplace=False)
        # Remove previous slab if present
        if hasattr(self, '_slab_actor') and self._slab_actor is not None:
            self.plotter.remove_actor(self._slab_actor)
        # Make slab more evident: solid fill, higher opacity, bold edge
        self._slab_actor = self.plotter.add_mesh(
            box, color="#00e6ff", opacity=0.45, style='surface', name="slab_region", reset_camera=False,
            show_edges=True, edge_color="#0080a0", line_width=3
        )
        # Show/hide based on checkbox
        if not self.chk_show_slab.isChecked():
            self.plotter.remove_actor(self._slab_actor)
        self.plotter.render()

    def on_n_colors_changed(self, val):
        self.n_colors = val
        if self.deformations is not None and self.mesh is not None:
            self.visualize_heatmap()

    def get_model_scale(self):
        """Return the largest dimension of the model's bounding box (for scale-aware thresholds)."""
        if self.mesh is not None:
            bounds = self.mesh.bounds
            return max(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4])
        return 1.0

    def report_obb_dimensions(self, center, normal, v1, v2, thickness=6.0):
        """Crea OBB solo per reporting dimensioni, non per masking o selezione."""
        width = np.linalg.norm(v1)
        height = np.linalg.norm(v2)
        msg = f"Dimensioni OBB (solo info):\nLarghezza: {width:.2f}\nAltezza: {height:.2f}\nSpessore: {thickness:.2f}"
        print(msg)
        # Se vuoi visualizzare l'OBB solo come info, puoi aggiungere:
        # (disabilitato per default)
        # box = pv.Box(bounds=(-width/2, width/2, -height/2, height/2, -thickness/2, thickness/2))
        # ... calcola rotazione come prima ...
        # box_transformed = box.transform(rot, inplace=False)
        # self.plotter.add_mesh(box_transformed, color='gray', opacity=0.1, style='wireframe', name='obb_info')

    def toggle_plane_visibility(self, state):
        if hasattr(self, 'plane_actor') and self.plane_actor is not None:
            if state:
                self.plotter.add_actor(self.plane_actor)
            else:
                self.plotter.remove_actor(self.plane_actor)

    def calculate_deformations(self):
        """Calculate deformations for points within a slab (thickness) around the defined plane."""
        if self.mesh is None:
            QMessageBox.warning(self, "Warning", "Please load a 3D model first!")
            return
        if len(self.plane_points) != 3:
            QMessageBox.warning(self, "Warning", "Please define a plane (pick 3 points) first!")
            return
        p1, p2, p3 = np.array(self.plane_points)
        v1 = p2 - p1
        v2 = p3 - p1
        normal = np.cross(v1, v2)
        normal = normal / np.linalg.norm(normal)
        center = (p1 + p2 + p3) / 3
        thickness = self.thickness_spin.value() / 2.0  # half-thickness on each side
        points = self.mesh.points
        # Signed distance from plane
        dists = np.dot(points - center, normal)
        mask = np.abs(dists) <= thickness
        deformations = np.full(points.shape[0], np.nan)
        deformations[mask] = dists[mask]
        self.deformations = deformations
        QMessageBox.information(self, "Deformations calculated", f"Deformations (distance from plane, within ±{thickness*2:.3f} m slab) have been calculated.")

    def show_current_mesh(self, reset_camera=True):
        """Visualizza il mesh corrente con texture, colori o colore di default."""
        if self.mesh is None:
            return
        self.plotter.clear()
        # Texture
        if self.texture is not None:
            self.plotter.add_mesh(
                self.mesh, texture=self.texture, show_edges=self.show_edges, smooth_shading=self.use_shading
            )
        # Color scalars (RGB or RGBA)
        elif self.mesh.active_scalars is not None and self.mesh.active_scalars.ndim == 2 and self.mesh.active_scalars.shape[1] in (3, 4):
            self.plotter.add_mesh(
                self.mesh, scalars=self.mesh.active_scalars_name, rgb=True, show_edges=self.show_edges, smooth_shading=self.use_shading
            )
        else:
            self.plotter.add_mesh(
                self.mesh, color="lightgray", show_edges=self.show_edges, smooth_shading=self.use_shading
            )
        # Only add bounding box if visible and not already present
        if hasattr(self, 'bbox_bounds') and self.bbox_visible:
            if self.bbox_actor is None:
                box = pv.Box(bounds=self.bbox_bounds)
                self.bbox_actor = self.plotter.add_mesh(box, color='blue', opacity=0.2, style='wireframe', name='bbox')
        if reset_camera:
            self.plotter.reset_camera()
        self.plotter.render()

    def load_model(self):
        """Load a 3D model (e.g. .stl, .obj, .ply)"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select 3D model", "", "3D Files (*.stl *.obj *.ply *.vtk *.vtp)"
        )
        if file_path:
            try:
                self.mesh = pv.read(file_path)
                # Ensure mesh has valid point normals
                if self.mesh.point_normals is None or len(self.mesh.point_normals) != self.mesh.n_points:
                    self.mesh.compute_normals(inplace=True)
                self.deformations = None  # Reset deformations when loading a new mesh
                self.texture = None  # Reset texture
                self.show_current_mesh()
                self.plotter.reset_camera()  # Only reset camera here
                QMessageBox.information(self, "Model loaded", f"Model loaded: {os.path.basename(file_path)}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Error loading model:\n{str(e)}")

    def load_texture(self):
        """Load a texture and apply it to the 3D model if present."""
        if self.mesh is None:
            QMessageBox.warning(self, "Warning", "Please load a 3D model first!")
            return
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select texture", "", "Images (*.jpg *.jpeg *.png *.bmp *.tif *.tiff)"
        )
        if file_path:
            try:
                self.texture = pv.read_texture(file_path)
                self.show_current_mesh()
                self.plotter.reset_camera()
                QMessageBox.information(self, "Texture loaded", f"Texture applied: {os.path.basename(file_path)}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Error loading texture:\n{str(e)}")

    def save_screenshot(self):
        """Save a screenshot of the 3D view to an image file."""
        file_path, _ = QFileDialog.getSaveFileName(self, "Save Screenshot", "screenshot.png", "PNG Image (*.png);;JPEG Image (*.jpg *.jpeg)")
        if file_path:
            try:
                img = self.plotter.screenshot(return_img=True)
                import imageio
                imageio.imwrite(file_path, img)
                QMessageBox.information(self, "Screenshot Saved", f"Screenshot saved to: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save screenshot:\n{str(e)}")

    def on_view_combo_changed(self, text):
        view_map = {
            "Top": 'top',
            "Bottom": 'bottom',
            "Left": 'left',
            "Right": 'right',
            "Front": 'front',
            "Back": 'back',
        }
        if text in view_map:
            self.set_view(view_map[text])

    def on_proj_combo_changed(self, text):
        if text == "Perspective":
            self.set_projection('perspective')
        elif text == "Orthographic":
            self.set_projection('orthographic')

    def toggle_slab_visibility(self, state):
        if hasattr(self, '_slab_actor') and self._slab_actor is not None:
            if state:
                self.plotter.add_actor(self._slab_actor)
            else:
                self.plotter.remove_actor(self._slab_actor)
        self.plotter.render()

    def on_bg_color_changed(self, color_name):
        color_map = {
            "Gray": "#b0b0b0",
            "White": "white",
            "Black": "black",
            "Light Blue": "#e0f0ff"
        }
        self.plotter.set_background(color_map.get(color_name, "#e0f0ff"))
        self.plotter.render()

    def set_view(self, view):
        """Set camera to a standard view."""
        if view == 'top':
            self.plotter.view_vector((0, 0, 1), (0, 1, 0))
        elif view == 'bottom':
            self.plotter.view_vector((0, 0, -1), (0, 1, 0))
        elif view == 'left':
            self.plotter.view_vector((-1, 0, 0), (0, 0, 1))
        elif view == 'right':
            self.plotter.view_vector((1, 0, 0), (0, 0, 1))
        elif view == 'front':
            self.plotter.view_vector((0, 1, 0), (0, 0, 1))
        elif view == 'back':
            self.plotter.view_vector((0, -1, 0), (0, 0, 1))
        self.plotter.reset_camera()
        self.plotter.render()

    def set_projection(self, mode):
        """Set camera projection mode."""
        if mode == 'perspective':
            self.plotter.camera.SetParallelProjection(False)
        elif mode == 'orthographic':
            self.plotter.camera.SetParallelProjection(True)
        self.plotter.render()

    def on_cmap_changed(self, cmap_name):
        """Update the current colormap and refresh the heatmap if needed."""
        self.current_cmap = cmap_name
        if self.deformations is not None and self.mesh is not None:
            self.visualize_heatmap()

    def toggle_sidebar(self):
        """Show or hide the sidebar widget and update the toggle button icon."""
        visible = self.sidebar_widget.isVisible()
        self.sidebar_widget.setVisible(not visible)
        if visible:
            self.btn_toggle_sidebar.setIcon(QIcon.fromTheme("go-next"))
        else:
            self.btn_toggle_sidebar.setIcon(QIcon.fromTheme("go-previous"))

    def visualize_heatmap(self):
        """Visualize the deformation heatmap on the mesh using the current colormap and discretization settings."""
        if self.mesh is None or self.deformations is None:
            QMessageBox.warning(self, "Warning", "No deformations to visualize. Please calculate deformations first.")
            return
        # Remove previous mesh and show with heatmap
        self.plotter.clear()
        # Mask points outside the box (deformations is nan there)
        scalars = self.deformations.copy()
        # Show mesh with heatmap
        self.plotter.add_mesh(
            self.mesh,
            scalars=scalars,
            cmap=self.current_cmap,
            nan_color="lightgray",
            show_edges=self.show_edges,
            smooth_shading=self.use_shading,
            clim=[np.nanmin(scalars), np.nanmax(scalars)] if np.any(~np.isnan(scalars)) else None,
            n_colors=self.n_colors,
            name="mesh"
        )
        self.plotter.render()

    def flip_heatmap_direction(self):
        """Stub for flip heatmap direction (plane mode only, currently hidden)."""
        QMessageBox.information(self, "Not available", "Flip direction is only available in plane mode.")

    def show_clean_mesh_dialog(self):
        """Stub for mesh cleaning dialog."""
        QMessageBox.information(self, "Not available", "Mesh cleaning options are not yet implemented.")

    def remove_last_point(self):
        """Remove the last picked point and update the visualization."""
        if not self.plane_points:
            QMessageBox.information(self, "No Points", "No points to remove.")
            return
        self.plane_points.pop()
        if self.point_labels:
            lbl = self.point_labels.pop()
            self.plotter.remove_actor(lbl)
        # Remove and redraw the remaining points
        self.plotter.remove_actor("plane_points")
        if self.plane_points:
            self.plotter.add_mesh(
                pv.PolyData(self.plane_points),
                color="yellow",
                point_size=20,
                render_points_as_spheres=True,
                name="plane_points",
                reset_camera=False
            )
            for i, lbl in enumerate(self.point_labels):
                self.plotter.add_mesh(lbl, color="black", name=f"label_{i}", reset_camera=False)
        self.plotter.render()

    def apply_global_scale(self):
        if self.mesh is None:
            return
        scale = self.global_scale_spin.value()
        if scale == 1.0:
            return
        # Rescale mesh
        self.mesh.points *= scale / getattr(self, '_last_global_scale', 1.0)
        self._last_global_scale = scale
        self.show_current_mesh()
        QMessageBox.information(self, "Model Scaled", f"Model globally scaled by {scale:.4g}.")

    def start_2pt_scale(self):
        if self.mesh is None:
            QMessageBox.warning(self, "Warning", "Please load a model first!")
            return
        self._2pt_scale_points = []
        self.plotter.disable_picking()
        self.picking_enabled = False
        self.plotter.enable_point_picking(
            callback=self.on_2pt_scale_pick,
            show_message=True,
            color="magenta",
            point_size=20,
            left_clicking=True,
            show_point=True,
            use_mesh=True
        )
        QMessageBox.information(self, "2-Point Scale Tool", "Pick 2 points on the model to define the reference distance.")

    def on_2pt_scale_pick(self, *args):
        # Extract picked point
        point = None
        if len(args) == 1 and isinstance(args[0], (list, tuple, np.ndarray)):
            point = np.array(args[0])
        elif len(args) >= 2 and hasattr(args[0], 'points'):
            if hasattr(args[0], 'points') and len(args[0].points) > 0:
                point = np.array(args[0].points[0])
        elif len(args) >= 2 and isinstance(args[0], (list, tuple, np.ndarray)):
            point = np.array(args[0])
        if point is None:
            QMessageBox.warning(self, "Picking Error", "Could not determine picked point. Please try again.")
            return
        if not hasattr(self, '_2pt_scale_points'):
            self._2pt_scale_points = []
        self._2pt_scale_points.append(point)
        if len(self._2pt_scale_points) == 2:
            self.plotter.disable_picking()
            self.picking_enabled = False
            p1, p2 = self._2pt_scale_points
            dist = np.linalg.norm(np.array(p2) - np.array(p1))
            val, ok = QInputDialog.getDouble(self, "2-Point Scale", f"Current distance: {dist:.4g}\nEnter real-world distance:", value=dist, min=1e-6, max=1e6, decimals=6)
            if ok and val > 0:
                scale = val / dist
                self.mesh.points *= scale
                self._last_global_scale = getattr(self, '_last_global_scale', 1.0) * scale
                self.show_current_mesh()
                QMessageBox.information(self, "Model Scaled", f"Model rescaled so selected distance is now {val:.4g}.")
            else:
                QMessageBox.information(self, "2-Point Scale", "Scaling cancelled.")
            del self._2pt_scale_points

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
