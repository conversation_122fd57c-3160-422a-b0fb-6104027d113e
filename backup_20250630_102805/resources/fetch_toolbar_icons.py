import os
import requests

# List of (filename, direct SVG URL) for toolbar icons (Feather only)
ICONS = [
    ("load_mesh.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/upload.svg"),
    ("load_texture.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/image.svg"),
    ("define_plane.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/align-center.svg"),
    ("set_slab_thickness.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/layers.svg"),
    ("show_heatmap.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/activity.svg"),
    ("export_csv.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/file-text.svg"),
    ("save_screenshot.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/camera.svg"),
    ("reset_picking.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/refresh-ccw.svg"),
    ("flip_direction.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/refresh-cw.svg"),
    ("select_colormap.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/droplet.svg"),
    ("mesh_display_mode.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/grid.svg"),
    ("process_mesh.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/settings.svg"),
    ("advanced_analysis.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/bar-chart-2.svg"),
    ("crop_slice_mesh.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/scissors.svg"),
    ("slice_with_plane.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/sliders.svg"),
    ("help_about.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/info.svg"),
    ("reset_view.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/crosshair.svg"),
    ("export_mesh.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/download.svg"),
    ("batch_process.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/repeat.svg"),
    ("undo.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/rotate-ccw.svg"),
    ("redo.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/rotate-cw.svg"),
    ("save_session.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/save.svg"),
    ("load_session.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/folder.svg"),
    ("square.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/square.svg"),
    ("arrow-up.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/arrow-up.svg"),
    ("arrow-down.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/arrow-down.svg"),
    ("arrow-left.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/arrow-left.svg"),
    ("arrow-right.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/arrow-right.svg"),
    ("corner-down-right.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/corner-down-right.svg"),
    ("corner-up-left.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/corner-up-left.svg"),
    ("maximize-2.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/maximize-2.svg"),
    ("search.svg", "https://raw.githubusercontent.com/feathericons/feather/master/icons/search.svg"),
]

ICON_DIR = os.path.join(os.path.dirname(__file__), "icons")
os.makedirs(ICON_DIR, exist_ok=True)

def fetch_icons():
    for fname, url in ICONS:
        out_path = os.path.join(ICON_DIR, fname)
        print(f"Fetching {fname} ...", end=" ")
        try:
            r = requests.get(url, timeout=10)
            r.raise_for_status()
            with open(out_path, "wb") as f:
                f.write(r.content)
            print("OK")
        except Exception as e:
            print(f"FAILED: {e}")

if __name__ == "__main__":
    fetch_icons()
