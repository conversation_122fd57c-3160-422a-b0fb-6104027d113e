#!/usr/bin/env python3
"""
Convert SVG icons to PNG format to bypass GTK SVG loader issues
"""

import os
import sys
from pathlib import Path

def convert_svg_to_png():
    """Convert all SVG icons to PNG format"""
    print("🔄 Converting SVG icons to PNG format...")
    
    icons_dir = Path("src/icons")
    if not icons_dir.exists():
        print("❌ Icons directory not found")
        return False
    
    svg_files = list(icons_dir.glob("*.svg"))
    if not svg_files:
        print("❌ No SVG files found")
        return False
    
    print(f"📁 Found {len(svg_files)} SVG icons to convert")
    
    try:
        # Try using cairosvg (if available)
        try:
            import cairosvg
            print("✅ Using cairosvg for conversion")
            
            for svg_file in svg_files:
                png_file = svg_file.with_suffix('.png')
                print(f"🔄 Converting {svg_file.name} -> {png_file.name}")
                
                cairosvg.svg2png(
                    url=str(svg_file),
                    write_to=str(png_file),
                    output_width=24,
                    output_height=24
                )
            
            print(f"✅ Converted {len(svg_files)} icons to PNG")
            return True
            
        except ImportError:
            print("⚠️ cairosvg not available, trying alternative method...")
            
            # Try using Pillow with svg support
            try:
                from PIL import Image
                import io
                
                # This is a fallback - create simple colored squares as PNG icons
                print("🎨 Creating simple PNG placeholders...")
                
                colors = [
                    (70, 130, 180),   # Steel blue
                    (60, 179, 113),   # Medium sea green  
                    (255, 140, 0),    # Dark orange
                    (220, 20, 60),    # Crimson
                    (138, 43, 226),   # Blue violet
                    (255, 215, 0),    # Gold
                    (105, 105, 105),  # Dim gray
                    (0, 191, 255),    # Deep sky blue
                ]
                
                for i, svg_file in enumerate(svg_files):
                    png_file = svg_file.with_suffix('.png')
                    color = colors[i % len(colors)]
                    
                    # Create a simple colored square
                    img = Image.new('RGBA', (24, 24), color + (255,))
                    img.save(png_file)
                    print(f"🎨 Created placeholder {png_file.name}")
                
                print(f"✅ Created {len(svg_files)} PNG placeholders")
                return True
                
            except ImportError:
                print("❌ No suitable conversion library available")
                return False
                
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
        return False

def update_icon_function():
    """Update the icon function to prefer PNG over SVG"""
    print("🔧 Updating icon loading function...")
    
    mainwindow_file = Path("src/gui/mainwindow.py")
    if not mainwindow_file.exists():
        print("❌ mainwindow.py not found")
        return False
    
    try:
        # Read the file
        with open(mainwindow_file, 'r') as f:
            content = f.read()
        
        # Find and replace the icon function
        old_function = '''        def icon(name):
            # Try src/icons first (new location), then data/icons (legacy)
            icon_paths = [
                os.path.join("src", "icons", name),
                os.path.join("data", "icons", name),
                os.path.join("icons", name)  # Direct icons folder
            ]
            
            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    return QIcon(icon_path)
            
            # Fallback: create a simple colored square as placeholder
            from PySide6.QtGui import QPixmap, QPainter, QColor
            pixmap = QPixmap(24, 24)
            pixmap.fill(QColor(100, 100, 100))  # Gray placeholder
            return QIcon(pixmap)'''
        
        new_function = '''        def icon(name):
            # Try PNG first (no GTK issues), then SVG, then legacy paths
            base_name = name.replace('.svg', '').replace('.png', '')
            icon_paths = [
                os.path.join("src", "icons", base_name + ".png"),  # PNG first
                os.path.join("src", "icons", base_name + ".svg"),  # SVG fallback
                os.path.join("src", "icons", name),                # Original name
                os.path.join("data", "icons", name),               # Legacy location
                os.path.join("icons", name)                        # Direct folder
            ]
            
            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    return QIcon(icon_path)
            
            # Fallback: create a simple colored square as placeholder
            from PySide6.QtGui import QPixmap, QPainter, QColor
            pixmap = QPixmap(24, 24)
            pixmap.fill(QColor(70, 130, 180))  # Steel blue placeholder
            return QIcon(pixmap)'''
        
        if old_function in content:
            content = content.replace(old_function, new_function)
            
            # Write back
            with open(mainwindow_file, 'w') as f:
                f.write(content)
            
            print("✅ Updated icon loading function to prefer PNG")
            return True
        else:
            print("⚠️ Icon function not found or already updated")
            return True
            
    except Exception as e:
        print(f"❌ Failed to update icon function: {e}")
        return False

def main():
    """Main conversion process"""
    print("🎯 DeformViz 3D Icon Conversion Tool")
    print("Converting SVG icons to PNG to bypass GTK issues")
    print("=" * 50)
    
    # Convert icons
    if convert_svg_to_png():
        print("✅ Icon conversion successful")
    else:
        print("❌ Icon conversion failed")
        return 1
    
    # Update icon loading function
    if update_icon_function():
        print("✅ Icon loading function updated")
    else:
        print("❌ Failed to update icon loading function")
        return 1
    
    print("")
    print("🎉 Conversion complete!")
    print("✅ PNG icons created and icon loading updated")
    print("🚀 Try running: ./run_deformviz_clean.sh")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
