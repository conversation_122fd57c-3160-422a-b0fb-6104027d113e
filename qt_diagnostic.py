#!/usr/bin/env python3
"""
Qt Platform Diagnostic Tool for DeformViz 3D
Helps identify and fix Qt platform plugin issues
"""

import sys
import os

def check_qt_installation():
    """Check Qt installation and available platforms"""
    print("🔍 Qt Platform Diagnostic Tool")
    print("=" * 50)
    
    # Check Python and Qt versions
    print(f"Python Version: {sys.version}")
    
    try:
        from PySide6 import QtCore
        print(f"PySide6 Version: {QtCore.__version__}")
        print(f"Qt Version: {QtCore.qVersion()}")
    except ImportError as e:
        print(f"❌ PySide6 Import Error: {e}")
        return False
    
    # Check available Qt platforms
    try:
        from PySide6.QtGui import QGuiApplication
        
        # Create minimal app to check platforms
        app = QGuiApplication.instance()
        if app is None:
            app = QGuiApplication([])
        
        # Get available platforms
        platforms = app.platformName()
        print(f"Current Platform: {platforms}")
        
        # Check platform plugins
        from PySide6.QtCore import QLibraryInfo
        plugin_path = QLibraryInfo.path(QLibraryInfo.LibraryPath.PluginsPath)
        print(f"Plugin Path: {plugin_path}")
        
        if os.path.exists(plugin_path):
            platforms_dir = os.path.join(plugin_path, "platforms")
            if os.path.exists(platforms_dir):
                available_platforms = os.listdir(platforms_dir)
                print(f"Available Platform Plugins: {available_platforms}")
            else:
                print("❌ No platforms directory found")
        else:
            print("❌ Plugin path does not exist")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ Qt Platform Check Error: {e}")
        return False

def test_minimal_qt():
    """Test minimal Qt functionality"""
    print("\n🧪 Testing Minimal Qt Functionality...")
    
    try:
        # Test without GUI
        from PySide6.QtCore import QCoreApplication
        app = QCoreApplication([])
        print("✅ QCoreApplication works")
        app.quit()
        
        # Test with GUI (this might fail)
        from PySide6.QtWidgets import QApplication
        os.environ['QT_QPA_PLATFORM'] = 'minimal'
        app = QApplication([])
        print("✅ QApplication works with minimal platform")
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ Minimal Qt Test Failed: {e}")
        return False

def suggest_fixes():
    """Suggest potential fixes"""
    print("\n💡 Suggested Fixes:")
    print("=" * 30)
    
    print("1. Install Qt platform plugins:")
    print("   sudo apt update")
    print("   sudo apt install qt6-qpa-plugins")
    print("   sudo apt install libxcb-xinerama0")
    print()
    
    print("2. Set Qt platform explicitly:")
    print("   export QT_QPA_PLATFORM=xcb")
    print("   python main.py")
    print()
    
    print("3. Try without NVIDIA Prime:")
    print("   unset __NV_PRIME_RENDER_OFFLOAD")
    print("   python main.py")
    print()
    
    print("4. Use the startup script:")
    print("   ./start_deformviz.sh")
    print()
    
    print("5. For testing without GUI:")
    print("   python test_startup.py")
    print()

def main():
    """Main diagnostic function"""
    print("🚀 DeformViz 3D Qt Platform Diagnostic")
    print("This tool helps identify Qt platform issues")
    print()
    
    # Check basic Qt installation
    qt_ok = check_qt_installation()
    
    # Test minimal functionality
    if qt_ok:
        test_minimal_qt()
    
    # Show environment info
    print("\n🌍 Environment Variables:")
    qt_vars = [var for var in os.environ.keys() if 'QT' in var or 'DISPLAY' in var]
    for var in sorted(qt_vars):
        print(f"  {var} = {os.environ[var]}")
    
    # Show suggestions
    suggest_fixes()
    
    print("\n✅ Icons are working fine - this is just a Qt platform issue!")
    print("The new measurement and quality control features are ready to use")
    print("once the Qt platform is configured correctly.")

if __name__ == "__main__":
    main()
