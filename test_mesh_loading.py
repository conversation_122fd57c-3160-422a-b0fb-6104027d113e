#!/usr/bin/env python3
"""
Test mesh loading functionality to identify crash causes
"""

import sys
import os

def test_basic_imports():
    """Test basic imports"""
    print("🔍 Testing Basic Imports...")
    
    try:
        import numpy as np
        print("✅ NumPy OK")
        
        import pyvista as pv
        print("✅ PyVista OK")
        
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QCoreApplication
        print("✅ PySide6 OK")
        
        return True
    except Exception as e:
        print(f"❌ Import Error: {e}")
        return False

def test_mesh_operations():
    """Test basic mesh operations"""
    print("\n🔍 Testing Mesh Operations...")
    
    try:
        import pyvista as pv
        
        # Test mesh creation
        mesh = pv.Sphere()
        print(f"✅ Sphere creation OK: {mesh.n_points} points")
        
        # Test mesh loading from file (if available)
        test_files = [
            "data/sample.ply",
            "data/test.ply", 
            "output/test.ply"
        ]
        
        for test_file in test_files:
            if os.path.exists(test_file):
                try:
                    mesh = pv.read(test_file)
                    print(f"✅ File loading OK: {test_file}")
                    break
                except Exception as e:
                    print(f"⚠️ File loading failed: {test_file} - {e}")
        
        return True
    except Exception as e:
        print(f"❌ Mesh Operation Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_qt_minimal():
    """Test minimal Qt functionality"""
    print("\n🔍 Testing Qt Functionality...")
    
    try:
        # Set Qt environment like our startup script
        os.environ['QT_PLUGIN_PATH'] = f"{os.environ.get('CONDA_PREFIX', '')}/lib/python3.13/site-packages/PySide6/Qt/plugins"
        os.environ['QT_QPA_PLATFORM'] = 'minimal'  # Use minimal for testing
        
        from PySide6.QtCore import QCoreApplication
        app = QCoreApplication.instance()
        if app is None:
            app = QCoreApplication([])
        
        print("✅ QCoreApplication OK")
        
        # Test file dialog creation (without showing)
        from PySide6.QtWidgets import QApplication, QFileDialog
        
        # Switch to minimal platform for GUI widgets
        os.environ['QT_QPA_PLATFORM'] = 'offscreen'
        
        gui_app = QApplication.instance()
        if gui_app is None:
            gui_app = QApplication([])
        
        print("✅ QApplication OK")
        
        # Don't actually show dialog, just test creation
        dialog = QFileDialog()
        print("✅ QFileDialog creation OK")
        
        return True
    except Exception as e:
        print(f"❌ Qt Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mesh_viewer_components():
    """Test mesh viewer components"""
    print("\n🔍 Testing Mesh Viewer Components...")
    
    try:
        # Test our mesh viewer import
        sys.path.insert(0, '.')
        from src.gui.meshviewer import MeshViewerWidget
        print("✅ MeshViewerWidget import OK")
        
        return True
    except Exception as e:
        print(f"❌ MeshViewer Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def suggest_fixes():
    """Suggest potential fixes"""
    print("\n💡 Potential Solutions:")
    print("=" * 30)
    
    print("1. Graphics/OpenGL Issues:")
    print("   - Try: export MESA_GL_VERSION_OVERRIDE=3.3")
    print("   - Try: export LIBGL_ALWAYS_SOFTWARE=1")
    print()
    
    print("2. VTK Rendering Issues:")
    print("   - Use offscreen rendering")
    print("   - Disable GPU acceleration")
    print()
    
    print("3. File Dialog Issues:")
    print("   - Use command line file loading")
    print("   - Bypass Qt file dialogs")
    print()
    
    print("4. Memory Issues:")
    print("   - Check available memory")
    print("   - Try smaller test files")

def main():
    """Main test function"""
    print("🔍 DeformViz 3D Mesh Loading Diagnostic")
    print("=" * 50)
    
    # Run tests
    tests_passed = 0
    total_tests = 4
    
    if test_basic_imports():
        tests_passed += 1
    
    if test_mesh_operations():
        tests_passed += 1
    
    if test_qt_minimal():
        tests_passed += 1
    
    if test_mesh_viewer_components():
        tests_passed += 1
    
    print(f"\n📊 Test Results: {tests_passed}/{total_tests} passed")
    
    if tests_passed < total_tests:
        suggest_fixes()
    else:
        print("✅ All tests passed - the issue might be environment-specific")
    
    return 0 if tests_passed == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
