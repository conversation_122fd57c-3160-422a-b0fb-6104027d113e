"""
Basic functionality tests for 3D Heatmap Deformation Viewer
"""
import unittest
import sys
from pathlib import Path
import numpy as np

# Add src to path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    import pyvista as pv
    from src.core.mesh_utils import (
        clean_mesh, remove_small_components, fill_small_holes, 
        smooth_mesh, curvature_analysis, ensure_polydata,
        calculate_vector_field, MeshProcessingError
    )
    from src.core.enhanced_analysis import Deformation<PERSON><PERSON>y<PERSON>, AnalysisError
    from src.core.config import config
    PYVISTA_AVAILABLE = True
except ImportError as e:
    print(f"Warning: PyVista or other dependencies not available: {e}")
    PYVISTA_AVAILABLE = False

class TestMeshUtils(unittest.TestCase):
    """Test mesh utility functions"""
    
    def setUp(self):
        """Create a simple test mesh"""
        if not PYVISTA_AVAILABLE:
            self.skipTest("PyVista not available")
        
        # Create a simple sphere mesh for testing
        self.mesh = pv.Sphere(radius=1.0, theta_resolution=20, phi_resolution=20)
    
    def test_clean_mesh(self):
        """Test mesh cleaning functionality"""
        cleaned = clean_mesh(self.mesh)
        self.assertIsInstance(cleaned, pv.DataSet)
        self.assertGreater(cleaned.n_points, 0)
    
    def test_smooth_mesh(self):
        """Test mesh smoothing"""
        smoothed = smooth_mesh(self.mesh, n_iter=5, relaxation=0.01)
        self.assertIsInstance(smoothed, pv.DataSet)
        self.assertEqual(smoothed.n_points, self.mesh.n_points)
    
    def test_ensure_polydata(self):
        """Test PolyData conversion"""
        polydata = ensure_polydata(self.mesh)
        self.assertIsInstance(polydata, pv.PolyData)
        self.assertGreater(polydata.n_points, 0)
    
    def test_calculate_vector_field(self):
        """Test vector field calculation"""
        plane_origin = np.array([0.0, 0.0, 0.0])
        plane_normal = np.array([0.0, 0.0, 1.0])
        
        vectors = calculate_vector_field(self.mesh, plane_origin, plane_normal)
        
        self.assertEqual(vectors.shape[0], self.mesh.n_points)
        self.assertEqual(vectors.shape[1], 3)
    
    def test_curvature_analysis(self):
        """Test curvature analysis"""
        result = curvature_analysis(self.mesh, curv_type='mean')
        self.assertIsInstance(result, pv.DataSet)
        self.assertIn('mean_curvature', result.point_data)
    
    def test_error_handling(self):
        """Test error handling with invalid inputs"""
        with self.assertRaises(MeshProcessingError):
            calculate_vector_field(None, np.array([0, 0, 0]), np.array([0, 0, 1]))

class TestDeformationAnalyzer(unittest.TestCase):
    """Test enhanced analysis functionality"""
    
    def setUp(self):
        """Create test deformation data"""
        if not PYVISTA_AVAILABLE:
            self.skipTest("PyVista not available")
        
        # Create synthetic deformation data
        np.random.seed(42)  # For reproducible tests
        self.deformations = np.random.normal(0, 1, 1000)
        
        # Add some outliers
        self.deformations[0] = 10.0  # Positive outlier
        self.deformations[1] = -10.0  # Negative outlier
        
        self.analyzer = DeformationAnalyzer()
    
    def test_statistical_analysis(self):
        """Test statistical analysis"""
        stats = self.analyzer.statistical_analysis(self.deformations)
        
        # Check that all expected statistics are present
        expected_keys = ['count', 'mean', 'median', 'std', 'var', 'min', 'max', 
                        'range', 'q25', 'q75', 'iqr', 'skewness', 'kurtosis', 'rms']
        
        for key in expected_keys:
            self.assertIn(key, stats)
            self.assertIsInstance(stats[key], (int, float))
        
        # Basic sanity checks
        self.assertEqual(stats['count'], 1000)
        self.assertGreater(stats['std'], 0)
        self.assertEqual(stats['range'], stats['max'] - stats['min'])
    
    def test_anomaly_detection_iqr(self):
        """Test IQR-based anomaly detection"""
        anomaly_mask, info = self.analyzer.detect_anomalies(
            self.deformations, method='iqr', threshold=1.5
        )
        
        self.assertEqual(len(anomaly_mask), len(self.deformations))
        self.assertIsInstance(anomaly_mask, np.ndarray)
        self.assertEqual(anomaly_mask.dtype, bool)
        
        # Should detect our planted outliers
        self.assertGreater(info['anomaly_count'], 0)
        self.assertEqual(info['method'], 'IQR')
    
    def test_anomaly_detection_zscore(self):
        """Test Z-score based anomaly detection"""
        anomaly_mask, info = self.analyzer.detect_anomalies(
            self.deformations, method='zscore', threshold=2.0
        )
        
        self.assertEqual(len(anomaly_mask), len(self.deformations))
        self.assertGreater(info['anomaly_count'], 0)
        self.assertEqual(info['method'], 'Z-Score')
    
    def test_error_handling(self):
        """Test error handling with invalid inputs"""
        with self.assertRaises(AnalysisError):
            self.analyzer.statistical_analysis(np.array([]))
        
        with self.assertRaises(AnalysisError):
            self.analyzer.detect_anomalies(self.deformations, method='invalid_method')

class TestConfiguration(unittest.TestCase):
    """Test configuration management"""
    
    def test_config_defaults(self):
        """Test that configuration has expected defaults"""
        self.assertIsInstance(config.defaults, dict)
        self.assertIn('default_colormap', config.defaults)
        self.assertIn('default_mesh_unit', config.defaults)
    
    def test_config_get_set(self):
        """Test configuration get/set functionality"""
        # Test getting default value
        colormap = config.get('default_colormap')
        self.assertIsNotNone(colormap)
        
        # Test setting and getting custom value
        config.set('test_key', 'test_value')
        self.assertEqual(config.get('test_key'), 'test_value')
    
    def test_config_persistence(self):
        """Test configuration file operations"""
        # This is a basic test - in practice you'd want to use a temporary config file
        original_value = config.get('default_colormap')
        config.set('default_colormap', 'test_colormap')
        
        # Save and reload (this would normally persist to file)
        config.save_settings()
        
        # Restore original value
        config.set('default_colormap', original_value)

def run_tests():
    """Run all tests"""
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test classes
    suite.addTests(loader.loadTestsFromTestCase(TestMeshUtils))
    suite.addTests(loader.loadTestsFromTestCase(TestDeformationAnalyzer))
    suite.addTests(loader.loadTestsFromTestCase(TestConfiguration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
