# DeformViz 3D

**DeformViz 3D - 3D Deformation Analysis Suite**

A powerful, professional 3D visualization tool for analyzing structural deformations in masonry and architectural elements using PyVista and PySide6.

## Features

- **3D Mesh Visualization**: Load and display 3D models in various formats (PLY, OBJ, STL, etc.)
- **Deformation Analysis**: Calculate and visualize deformations relative to reference planes
- **Interactive Heatmaps**: Real-time colormap visualization with customizable color schemes
- **Plane Definition**: Define analysis planes using multiple methods (points, canonical orientations)
- **Mesh Processing**: Clean, smooth, and process meshes with advanced algorithms
- **Export Capabilities**: Save results, screenshots, and cross-sections
- **Advanced Analysis**: Curvature analysis, statistical measurements, and more

## Installation

### Prerequisites

- Python 3.8 or higher
- NVIDIA GPU (recommended for optimal performance)

### Setup

1. Clone or download the project
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Run the application:
   ```bash
   python main.py
   ```

## Project Structure

```
3d-heatmap-viewer/
├── src/
│   ├── core/           # Core functionality
│   │   ├── config.py   # Configuration management
│   │   ├── mesh_utils.py
│   │   ├── analysis.py
│   │   └── ...
│   ├── gui/            # User interface
│   │   ├── mainwindow.py
│   │   ├── meshviewer.py
│   │   └── ...
│   └── utils/          # Utility functions
│       └── logging_config.py
├── data/               # Sample data and resources
├── output/             # Generated outputs
├── tests/              # Unit tests
├── docs/               # Documentation
├── requirements.txt    # Python dependencies
├── README.md          # This file
└── main.py            # Application entry point
```

## Usage

1. **Load a 3D Model**: Use File → Load 3D Model to import your mesh
2. **Define Analysis Plane**: Use Analysis → Define Plane to set reference plane
3. **Calculate Deformations**: Click "Calculate Deformations" to analyze
4. **Visualize Results**: Use "Visualize Heatmap" to display results
5. **Export**: Save screenshots, data, or cross-sections as needed

## Configuration

The application stores settings in `~/.config/3d-heatmap-viewer/settings.json`. You can modify:

- Default colormaps and visualization settings
- Analysis parameters
- File paths and directories
- GPU acceleration settings

## Supported Formats

- **Input**: PLY, OBJ, STL, VTK, GLTF, and more
- **Export**: PNG, JPG (screenshots), CSV (data), DXF (cross-sections)

## Requirements

See `requirements.txt` for complete dependency list. Key dependencies:

- PyVista (3D visualization)
- PySide6 (GUI framework)
- NumPy, SciPy (numerical computing)
- VTK (visualization backend)

## License

This project is for research and educational purposes.

## Contributing

1. Follow PEP 8 style guidelines
2. Add tests for new features
3. Update documentation as needed
4. Use meaningful commit messages

## Troubleshooting

### NVIDIA GPU Issues
If you encounter GPU-related problems:
1. Ensure NVIDIA drivers are installed
2. Check that CUDA is available
3. Modify GPU settings in configuration

### Performance Issues
For large meshes:
1. Enable level-of-detail rendering
2. Reduce mesh resolution if needed
3. Close other GPU-intensive applications

### Import Errors
If you get import errors:
1. Verify all dependencies are installed
2. Check Python path configuration
3. Ensure all `__init__.py` files are present
