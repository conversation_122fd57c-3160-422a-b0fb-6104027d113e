#!/usr/bin/env python3
"""
Test runner for 3D Heatmap Deformation Viewer
"""
import sys
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# Import and run tests
from tests.test_basic_functionality import run_tests

if __name__ == "__main__":
    print("Running 3D Heatmap Deformation Viewer Tests...")
    print("=" * 50)
    
    success = run_tests()
    
    if success:
        print("\n" + "=" * 50)
        print("✅ All tests passed!")
    else:
        print("\n" + "=" * 50)
        print("❌ Some tests failed!")
        sys.exit(1)
