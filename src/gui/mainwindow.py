import sys
from PySide6.QtWidgets import (Q<PERSON><PERSON>lication, QMainWindow, QToolBar, QDockWidget, QTabWidget, QWidget, QVBoxLayout, QLabel, QListWidget, QTextEdit, QHBoxLayout, QPushButton, QFileDialog, QMessageBox, QSlider, QDoubleSpinBox, QFrame, QInputDialog, QDialog, QVBoxLayout, QGroupBox, QFormLayout, QRadioButton, QButtonGroup, QStatusBar)
from PySide6.QtCore import Qt, Signal
from src.gui.meshviewer import MeshViewerWidget
from src.gui.mesh_display import MeshDisplayModeDialog
from src.gui.mesh_processing import MeshProcessingDialog
from src.gui.mesh_advanced_analysis import MeshAdvancedAnalysisDialog
from src.gui.plane_definition_choice import PlaneDefinitionChoiceDialog
from src.gui.plane_by_points import PlaneByPointsDialog
from src.gui.plane_canonical import PlaneCanonicalDialog
from src.gui.crop_slice_mesh import CropSliceMeshDialog
from PySide6.QtGui import QIcon, QColor, QMovie
from PySide6.QtWidgets import QColorDialog
import os
from src.gui.section_export import extract_section_curves, export_section_to_dxf
from src.core import mesh_utils
import numpy as np
import pyvista as pv

def run_app():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        from src.core.config import config
        self.setWindowTitle(config.app_full_name)
        self.setGeometry(100, 100, 1000, 700)

        # Set application icon
        self._set_app_icon()
        self.mesh_unit = "meters"  # Default unit, moved before _init_ui
        self._init_ui()
        self._init_slab_thickness_control()
        self._analysis_plane_action = None
        self._loading_label = None
        self._loading_spinner = None

    def _set_app_icon(self):
        """Set the application icon"""
        try:
            from PySide6.QtGui import QIcon
            icon_path = "data/icons/app_icon.png"
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            print(f"Could not set app icon: {e}")

    def _init_ui(self):
        # Central widget: MeshViewerWidget
        self.mesh_viewer = MeshViewerWidget()
        self.setCentralWidget(self.mesh_viewer)

        # Status bar for projection mode and unit
        self.status = self.statusBar()
        self._projection_label = QLabel()
        self._unit_label = QLabel()
        self.status.addPermanentWidget(self._projection_label)
        self.status.addPermanentWidget(self._unit_label)
        self.update_status_bar()

        # Toolbar
        toolbar = QToolBar("Main Toolbar")
        self.addToolBar(toolbar)
        # Helper function for icons - use our new icon directory
        def icon(name):
            icon_path = os.path.join("data", "icons", name)
            if os.path.exists(icon_path):
                return QIcon(icon_path)
            else:
                return QIcon()  # Empty icon as fallback
        # === CORE WORKFLOW GROUP ===
        # Essential functions that users need immediately
        load_action = toolbar.addAction(icon("load.svg"), "Load 3D Mesh")
        load_action.triggered.connect(self.load_mesh_dialog)
        load_action.setToolTip("Load a 3D mesh file for analysis (Ctrl+O)")
        load_action.setShortcut("Ctrl+O")

        save_action = toolbar.addAction(icon("save.svg"), "Save Project")
        save_action.triggered.connect(self.save_project_dialog)
        save_action.setToolTip("Save the current project (Ctrl+S)")
        save_action.setShortcut("Ctrl+S")

        help_action = toolbar.addAction(icon("help.svg"), "Help")
        help_action.triggered.connect(self.show_help_dialog)
        help_action.setToolTip("Show help and documentation (F1)")
        help_action.setShortcut("F1")
        toolbar.addSeparator()

        # === MESH PROCESSING GROUP ===
        # Tools for preparing and processing the mesh
        mesh_display_action = toolbar.addAction(icon("settings.svg"), "Display Settings")
        mesh_display_action.triggered.connect(self.show_mesh_display_mode_dialog)
        mesh_display_action.setToolTip("Configure mesh visualization settings\n• Change display mode (surface, wireframe, points)\n• Adjust colors and transparency\n• Set shading options")

        process_mesh_action = toolbar.addAction(icon("process_mesh.svg"), "Process Mesh")
        process_mesh_action.triggered.connect(self.show_mesh_processing_dialog)
        process_mesh_action.setToolTip("Clean, smooth, and repair the mesh\n• Remove noise and artifacts\n• Fill holes and fix topology\n• Smooth surface irregularities")

        adv_analysis_action = toolbar.addAction(icon("analyze.svg"), "Advanced Analysis")
        adv_analysis_action.triggered.connect(self.show_advanced_analysis_dialog)
        adv_analysis_action.setToolTip("Perform advanced mesh analysis\n• Calculate curvature and thickness\n• Detect anomalies and defects\n• Generate statistical reports")
        toolbar.addSeparator()

        # === ANALYSIS SETUP GROUP ===
        # Tools for setting up deformation analysis
        self.action_define_plane = toolbar.addAction(icon("plane.svg"), "Define Plane")
        self.action_define_plane.triggered.connect(self.show_plane_definition_choice)
        self.action_define_plane.setToolTip("Define the reference plane for deformation analysis\n• Choose from canonical planes (XY, XZ, YZ)\n• Define by 3 points\n• Set custom orientation")

        flip_direction_action = toolbar.addAction(icon("flip_direction.svg"), "Flip Analysis Direction")
        flip_direction_action.setToolTip("Flip the direction of deformation analysis\n• Reverse positive/negative deformation\n• Change analysis orientation\n• Useful for comparing different perspectives")
        toolbar.addSeparator()

        # === EDITING TOOLS GROUP ===
        # Tools for modifying and editing the mesh
        crop_box_action = toolbar.addAction(icon("crop_box.svg"), "Crop Mesh (Box)")
        crop_box_action.setToolTip("Interactively crop the mesh using a box\n• Select region of interest\n• Remove unwanted parts\n• Focus analysis on specific areas")
        crop_box_action.triggered.connect(self.show_crop_box_dialog)

        slice_with_plane_action = toolbar.addAction(icon("slice_plane.svg"), "Slice with Plane")
        slice_with_plane_action.setToolTip("Slice the mesh with a plane to create cross-sections\n• Generate 2D profiles\n• Export to DXF format\n• Analyze internal structure")
        slice_with_plane_action.triggered.connect(self.show_section_with_plane_dialog)

        undo_action = toolbar.addAction(icon("undo.svg"), "Undo")
        undo_action.setToolTip("Undo last operation (Ctrl+Z)\n• Revert recent changes\n• Restore previous state\n• Multiple undo levels supported")
        undo_action.setShortcut("Ctrl+Z")
        undo_action.triggered.connect(self.undo_last_operation)
        toolbar.addSeparator()

        # === VISUALIZATION GROUP ===
        # Tools for visualizing analysis results
        self.action_show_heatmap = toolbar.addAction(icon("heatmap.svg"), "Show Deformation Heatmap")
        self.action_show_heatmap.setToolTip("Display deformation values as a color-coded heatmap\n• Visualize displacement from reference plane\n• Color-coded from blue (negative) to red (positive)\n• Adjustable scale and transparency")
        self.action_show_heatmap.triggered.connect(self.show_heatmap_clicked)

        self.action_show_vector_field = toolbar.addAction(icon("vector_field.svg"), "Show Vector Field")
        self.action_show_vector_field.setToolTip("Show vector field (direction from plane) for each mesh point\n• Display directional arrows\n• Visualize deformation direction\n• Adjustable arrow scale and density")
        self.action_show_vector_field.triggered.connect(self.show_vector_field_clicked)

        colormap_action = toolbar.addAction(icon("color_picker.svg"), "Select Colormap")
        colormap_action.setToolTip("Choose color scheme for visualization\n• Select from predefined color maps\n• Customize color ranges\n• Adjust contrast and brightness")
        colormap_action.triggered.connect(self.show_colormap_dialog)

        # === EXPORT & RESULTS GROUP ===
        # Tools for exporting and saving results
        export_results_action = toolbar.addAction(icon("export.svg"), "Export Results")
        export_results_action.setToolTip("Export analysis results and data\n• Generate comprehensive reports\n• Export multiple file formats\n• Include charts and statistics")

        export_csv_action = toolbar.addAction(icon("export_csv.svg"), "Export CSV")
        export_csv_action.setToolTip("Export deformation data to CSV file (Ctrl+E)\n• Spreadsheet-compatible format\n• Includes X, Y, Z coordinates\n• Deformation values for each point")
        export_csv_action.setShortcut("Ctrl+E")
        export_csv_action.triggered.connect(self.export_deformation_csv)

        save_cropped_action = toolbar.addAction(icon("export_mesh.svg"), "Save Mesh")
        save_cropped_action.setToolTip("Save the currently displayed mesh to file\n• Multiple formats (PLY, STL, OBJ, VTK)\n• Preserves colors and analysis data\n• Optimized for further processing")
        save_cropped_action.triggered.connect(self.save_cropped_mesh)

        # Screenshot with dropdown menu for options
        screenshot_action = toolbar.addAction(icon("screenshot.svg"), "Screenshot")
        screenshot_action.setToolTip("Take screenshot of current view (F12)")
        screenshot_action.setShortcut("F12")
        screenshot_action.triggered.connect(self.quick_screenshot)

        # Create dropdown menu for screenshot options
        from PySide6.QtWidgets import QMenu
        screenshot_menu = QMenu(self)

        quick_screenshot_action = screenshot_menu.addAction("Quick Screenshot")
        quick_screenshot_action.setToolTip("Take quick screenshot at current resolution")
        quick_screenshot_action.triggered.connect(self.quick_screenshot)

        hq_screenshot_action = screenshot_menu.addAction("High-Quality Screenshot")
        hq_screenshot_action.setToolTip("Take high-resolution screenshot with options")
        hq_screenshot_action.triggered.connect(self.save_screenshot_dialog)

        # Add the menu to the action
        screenshot_button = toolbar.widgetForAction(screenshot_action)
        if hasattr(screenshot_button, 'setMenu'):
            screenshot_button.setMenu(screenshot_menu)
        toolbar.addSeparator()

        # === SESSION MANAGEMENT GROUP ===
        # Tools for saving and loading analysis sessions
        save_session_action = toolbar.addAction(icon("save_session.svg"), "Save Session")
        save_session_action.setToolTip("Save current analysis session\n• Preserve all settings and results\n• Include mesh, plane, and visualization\n• Resume work later from exact state")
        save_session_action.triggered.connect(self.save_session_dialog)

        load_session_action = toolbar.addAction(icon("load_session.svg"), "Load Session")
        load_session_action.setToolTip("Load previous analysis session\n• Restore complete workspace\n• Reload mesh and analysis settings\n• Continue from where you left off")
        load_session_action.triggered.connect(self.load_session_dialog)
        # Remove Load Texture, Set Slab Thickness, Reset Picking, Help/About, Redo, Export Mesh (duplicate), and other unused icons
        # ...existing code...

        # Sidebar (dock widget with tabs)
        self.sidebar = QDockWidget("Tools & Info", self)
        self.sidebar.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)
        self.tab_widget = QTabWidget()

        # === TAB 1: MESH INFO ===
        # Primary tab with mesh information and statistics
        info_tab = QWidget()
        info_layout = QVBoxLayout(info_tab)
        info_layout.addWidget(QLabel("Mesh Info:"))
        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        info_layout.addWidget(self.info_text)
        self.tab_widget.addTab(info_tab, "Mesh Info")

        # === TAB 2: ANNOTATIONS ===
        # Tools for adding annotations and measurements
        annotation_tab = QWidget()
        ann_layout = QVBoxLayout(annotation_tab)
        ann_layout.addWidget(QLabel("Annotations:"))
        ann_layout.addWidget(QListWidget())
        btns_layout = QHBoxLayout()
        btns_layout.addWidget(QPushButton("Add Point"))
        btns_layout.addWidget(QPushButton("Add Line"))
        btns_layout.addWidget(QPushButton("Add Text"))
        ann_layout.addLayout(btns_layout)
        ann_layout.addWidget(QPushButton("Export Annotations"))
        self.tab_widget.addTab(annotation_tab, "Annotations")

        # === TAB 3: HISTORY ===
        # Session history and operation tracking
        history_tab = QWidget()
        hist_layout = QVBoxLayout(history_tab)
        hist_layout.addWidget(QLabel("Session History:"))
        hist_layout.addWidget(QListWidget())
        self.tab_widget.addTab(history_tab, "History")

        self.sidebar.setWidget(self.tab_widget)
        self.addDockWidget(Qt.RightDockWidgetArea, self.sidebar)

        # --- Left sidebar for view controls ---
        self.view_toolbar = QToolBar("View Controls")
        self.view_toolbar.setOrientation(Qt.Vertical)
        self.addToolBar(Qt.LeftToolBarArea, self.view_toolbar)

        # Projection toggle
        self.action_proj_toggle = self.view_toolbar.addAction(icon("view_perspective_ortho.svg"), "Perspective/Ortho")
        self.action_proj_toggle.triggered.connect(self.toggle_projection_mode)
        self.action_proj_toggle.setToolTip("Toggle between perspective and orthographic projection")

        self.view_toolbar.addSeparator()

        # Standard views
        self.action_view_top = self.view_toolbar.addAction(icon("view_top.svg"), "Top View")
        self.action_view_top.triggered.connect(lambda: self.set_standard_view('top'))
        self.action_view_top.setToolTip("View from above (top view)")

        self.action_view_bottom = self.view_toolbar.addAction(icon("view_bottom.svg"), "Bottom View")
        self.action_view_bottom.triggered.connect(lambda: self.set_standard_view('bottom'))
        self.action_view_bottom.setToolTip("View from below (bottom view)")

        self.action_view_front = self.view_toolbar.addAction(icon("view_front.svg"), "Front View")
        self.action_view_front.triggered.connect(lambda: self.set_standard_view('front'))
        self.action_view_front.setToolTip("View from front")

        self.action_view_back = self.view_toolbar.addAction(icon("view_back.svg"), "Back View")
        self.action_view_back.triggered.connect(lambda: self.set_standard_view('back'))
        self.action_view_back.setToolTip("View from back")

        self.action_view_left = self.view_toolbar.addAction(icon("view_left.svg"), "Left View")
        self.action_view_left.triggered.connect(lambda: self.set_standard_view('left'))
        self.action_view_left.setToolTip("View from left side")

        self.action_view_right = self.view_toolbar.addAction(icon("view_right.svg"), "Right View")
        self.action_view_right.triggered.connect(lambda: self.set_standard_view('right'))
        self.action_view_right.setToolTip("View from right side")

        self.view_toolbar.addSeparator()

        # Zoom controls
        self.action_zoom_all = self.view_toolbar.addAction(icon("zoom_all.svg"), "Zoom All")
        self.action_zoom_all.triggered.connect(self.zoom_all)
        self.action_zoom_all.setToolTip("Zoom to fit all objects in view")

        self.action_zoom_window = self.view_toolbar.addAction(icon("zoom_window.svg"), "Zoom Window")
        self.action_zoom_window.triggered.connect(self.zoom_window)
        self.action_zoom_window.setToolTip("Zoom to a selected rectangular region")

        # --- Menu bar ---
        menubar = self.menuBar()
        # --- File menu ---
        file_menu = menubar.addMenu("File")
        self.action_load_mesh = file_menu.addAction("Load Mesh...")
        self.action_load_mesh.triggered.connect(self.load_mesh_dialog)
        self.action_load_session = file_menu.addAction("Load Session...")
        # self.action_load_session.triggered.connect(self.load_session_dialog)  # TODO: implement
        self.action_save_session = file_menu.addAction("Save Session...")
        # self.action_save_session.triggered.connect(self.save_session_dialog)  # TODO: implement
        file_menu.addSeparator()
        self.action_exit = file_menu.addAction("Exit")
        self.action_exit.triggered.connect(self.close)

        # --- Configuration menu ---
        config_menu = menubar.addMenu("Configuration")
        self.action_show_sidebar = config_menu.addAction("Show Sidebar")
        self.action_show_sidebar.setCheckable(True)
        self.action_show_sidebar.setChecked(True)
        self.action_show_sidebar.triggered.connect(self.toggle_sidebar)
        self.action_show_left_toolbar = config_menu.addAction("Show Left Toolbar")
        self.action_show_left_toolbar.setCheckable(True)
        self.action_show_left_toolbar.setChecked(True)
        self.action_show_left_toolbar.triggered.connect(self.toggle_left_toolbar)
        self.action_set_bg_color = config_menu.addAction("Set Background Color...")
        self.action_set_bg_color.triggered.connect(self.pick_background_color)
        config_menu.addSeparator()
        self.action_show_bbox = config_menu.addAction("Show Bounding Box")
        self.action_show_bbox.setCheckable(True)
        self.action_show_bbox.setChecked(False)
        self.action_show_bbox.triggered.connect(lambda checked: self.mesh_viewer.show_bounding_box(checked))
        # --- Show Analysis Plane toggle ---
        self.action_show_analysis_plane = config_menu.addAction("Show Analysis Plane")
        self.action_show_analysis_plane.setCheckable(True)
        self.action_show_analysis_plane.setChecked(True)
        self.action_show_analysis_plane.triggered.connect(self.toggle_analysis_plane)
        # --- Set Unit action ---
        self.action_set_unit = config_menu.addAction("Set Unit...")
        self.action_set_unit.triggered.connect(self.show_set_unit_dialog)

    def show_set_unit_dialog(self):
        units = ["meters", "centimeters", "millimeters"]
        current = units.index(self.mesh_unit) if self.mesh_unit in units else 0
        unit, ok = QInputDialog.getItem(self, "Select Unit", "Choose the mesh unit:", units, current, False)
        if ok and unit:
            self.set_mesh_unit(unit)

    def toggle_sidebar(self, checked=None):
        if checked is None:
            checked = self.action_show_sidebar.isChecked()
        self.sidebar.setVisible(checked)
        self.action_show_sidebar.setChecked(self.sidebar.isVisible())

    def toggle_left_toolbar(self, checked=None):
        if checked is None:
            checked = self.action_show_left_toolbar.isChecked()
        self.view_toolbar.setVisible(checked)
        self.action_show_left_toolbar.setChecked(self.view_toolbar.isVisible())

    def pick_background_color(self):
        color = QColorDialog.getColor(QColor('#888888'), self, "Select 3D Background Color")
        if color.isValid():
            hex_color = color.name()
            self.mesh_viewer.plotter.set_background(hex_color)
            self.mesh_viewer.plotter.render()

    def show_loading_status(self, message="Loading mesh ..."):
        if self._loading_label is None:
            self._loading_label = QLabel()
            self._loading_label.setStyleSheet("color: #0077cc; font-weight: bold; background: #eaf6ff; padding: 2px 8px; border-radius: 6px;")
            # Add spinner
            self._loading_spinner = QLabel()
            spinner_movie = QMovie(":/icons/spinner.gif")  # You need to provide a spinner.gif in your resources
            self._loading_spinner.setMovie(spinner_movie)
            spinner_movie.start()
            self.status.addPermanentWidget(self._loading_spinner)
            self.status.addPermanentWidget(self._loading_label)
        self._loading_label.setText(message)
        self._loading_label.show()
        if self._loading_spinner:
            self._loading_spinner.show()

    def hide_loading_status(self):
        if self._loading_label:
            self._loading_label.hide()
        if self._loading_spinner:
            self._loading_spinner.hide()

    def load_mesh_dialog(self):
        from PySide6.QtWidgets import QDialog, QLabel, QVBoxLayout
        from PySide6.QtCore import Qt
        filters = (
            "3D Mesh Files (*.stl *.obj *.ply *.vtk *.vtp *.off *.gltf *.glb *.3ds *.dae *.fbx *.x *.wrl *.mesh *.msh *.ctm *.gts *.usd *.usda *.usdc *.usdz);;"
            "All Files (*)"
        )
        last_dir = os.path.expanduser("~")
        file_path, _ = QFileDialog.getOpenFileName(self, "Open 3D Mesh", last_dir, filters)
        if file_path:
            # --- OBJ pre-check for malformed lines ---
            malformed_lines = []
            if file_path.lower().endswith('.obj'):
                with open(file_path, 'r', errors='ignore') as f:
                    for i, line in enumerate(f, 1):
                        if len(line.split()) > 10 or ('\0' in line):
                            malformed_lines.append(i)
                        if i > 100000:
                            break
                if malformed_lines:
                    msg = f"Warning: OBJ file contains unexpected data at end of line (e.g. line {malformed_lines[0]})\nThis may cause loading issues or memory problems.\nFirst 5 problematic lines: {malformed_lines[:5]}"
                    QMessageBox.warning(self, "OBJ File Warning", msg)
            # --- Status bar loading label ---
            self.show_loading_status()
            QApplication.processEvents()
            try:
                self.mesh_viewer.load_mesh(file_path)
                # After loading mesh, ask user for unit
                units = ["meters", "centimeters", "millimeters"]
                unit, ok = QInputDialog.getItem(self, "Select Mesh Unit", "What is the unit of the mesh coordinates?", units, 0, False)
                if ok:
                    self.mesh_unit = unit
                else:
                    self.mesh_unit = "meters"  # fallback
                self.update_mesh_info()
            except Exception as e:
                QMessageBox.critical(self, "Load Error", str(e))
            self.hide_loading_status()

    def update_mesh_info(self):
        mesh = self.mesh_viewer.mesh
        if mesh is None:
            self.info_text.setPlainText("No mesh loaded.")
            return
        info = []
        info.append(f"Type: {type(mesh).__name__}")
        info.append(f"Points: {mesh.n_points}")
        info.append(f"Cells: {mesh.n_cells}")
        if hasattr(mesh, 'faces') and mesh.faces is not None:
            info.append(f"Faces: {len(mesh.faces) // 4 if mesh.faces is not None else 'N/A'}")
        if hasattr(mesh, 'n_faces'):
            info.append(f"n_faces: {mesh.n_faces}")
        if hasattr(mesh, 'n_edges'):
            info.append(f"n_edges: {mesh.n_edges}")
        if hasattr(mesh, 'area'):
            info.append(f"Area: {getattr(mesh, 'area', 'N/A'):.4f}")
        if hasattr(mesh, 'volume'):
            info.append(f"Volume: {getattr(mesh, 'volume', 'N/A'):.4f}")
        info.append(f"Bounds: {mesh.bounds}")
        info.append(f"Arrays: {list(mesh.point_data.keys())}")
        # Add mesh unit to info
        info.append(f"Mesh Unit: {self.mesh_unit}")
        self.info_text.setPlainText("\n".join(info))

    def toggle_projection_mode(self):
        plotter = self.mesh_viewer.plotter
        if plotter.camera.GetParallelProjection():
            plotter.camera.ParallelProjectionOff()
        else:
            plotter.camera.ParallelProjectionOn()
        plotter.render()
        self.update_status_bar()

    def set_standard_view(self, view):
        plotter = self.mesh_viewer.plotter
        views = {
            'top': (0, 0, 1),
            'bottom': (0, 0, -1),
            'left': (-1, 0, 0),
            'right': (1, 0, 0),
            'front': (0, 1, 0),
            'back': (0, -1, 0),
        }
        if view in views:
            plotter.view_vector(views[view])
            plotter.reset_camera()
            plotter.render()

    def zoom_all(self):
        self.mesh_viewer.plotter.reset_camera()
        self.mesh_viewer.plotter.render()

    def zoom_window(self):
        # Use the correct PyVistaQt method for rubber band zoom
        self.mesh_viewer.plotter.enable_rubber_band_2d_style()
        self.mesh_viewer.plotter.render()

    def show_mesh_display_mode_dialog(self):
        current_mode = getattr(self.mesh_viewer, 'display_mode', 'surface')
        dialog = MeshDisplayModeDialog(current_mode, parent=self, mesh_viewer=self.mesh_viewer)
        from PySide6.QtWidgets import QDialog
        if dialog.exec() == QDialog.Accepted:
            mode = dialog.selected_mode()
            wire_color = dialog.get_wire_color()
            edge_color = dialog.get_edge_color()
            point_color = dialog.get_point_color()
            edge_width = dialog.get_edge_width()
            point_size = dialog.get_point_size()
            shading = dialog.get_shading()
            self.mesh_viewer.set_display_mode(
                mode,
                wire_color=wire_color,
                point_color=point_color,
                point_size=point_size,
                edge_color=edge_color,
                edge_width=edge_width,
                shading=shading
            )

    def show_mesh_processing_dialog(self):
        if self.mesh_viewer.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        dialog = MeshProcessingDialog(self)
        if dialog.exec():
            params = dialog.get_selected_filters()
            self.process_mesh(params)

    def process_mesh(self, params):
        from PySide6.QtWidgets import QProgressDialog, QMessageBox, QApplication
        mesh = self.mesh_viewer.mesh
        # Always convert mesh to PolyData for processing
        try:
            mesh = mesh_utils.ensure_polydata(mesh)
        except Exception as e:
            QMessageBox.warning(self, "Mesh Processing Error", f"Mesh processing requires a PolyData mesh. Conversion failed: {e}")
            return
        # Now create the progress dialog
        steps = [
            ('clean', params['clean']),
            ('components', params['components']),
            ('holes', params['holes']),
            ('smooth', params['smooth']),
            ('decimate', params['decimate'])
            # remesh removed
        ]
        total = sum(1 for _, enabled in steps if enabled)
        progress = QProgressDialog("Processing mesh...", None, 0, total, self)
        progress.setWindowTitle("Mesh Processing")
        progress.setWindowModality(Qt.WindowModal)
        progress.setValue(0)
        progress.show()
        QApplication.processEvents()
        step = 0
        # Clean
        if params['clean']:
            progress.setLabelText("Cleaning mesh...")
            QApplication.processEvents()
            try:
                mesh = mesh_utils.clean_mesh(mesh)
            except Exception as e:
                QMessageBox.warning(self, "Clean Mesh Error", f"Mesh cleaning failed: {e}")
            step += 1
            progress.setValue(step)
        # Remove small components
        if params['components']:
            progress.setLabelText("Removing small components...")
            QApplication.processEvents()
            try:
                mesh = mesh_utils.remove_small_components(mesh)
            except Exception as e:
                QMessageBox.warning(self, "Remove Components Error", f"Removing small components failed: {e}")
            step += 1
            progress.setValue(step)
        # Fill small holes
        if params['holes']:
            progress.setLabelText("Filling small holes...")
            QApplication.processEvents()
            try:
                mesh = mesh_utils.fill_small_holes(mesh, max_area=params['hole_size'])
            except Exception as e:
                QMessageBox.warning(self, "Fill Holes Error", f"Filling small holes failed: {e}")
            step += 1
            progress.setValue(step)
        # Smooth
        if params['smooth']:
            progress.setLabelText("Smoothing mesh...")
            QApplication.processEvents()
            try:
                new_mesh = mesh_utils.smooth_mesh(mesh, n_iter=params['smooth_iter'], relaxation=params['smooth_relax'])
                if new_mesh is None:
                    raise RuntimeError("Smoothing did not return a valid mesh.")
                mesh = new_mesh
            except Exception as e:
                QMessageBox.warning(self, "Smooth Error", f"Mesh smoothing failed: {e}")
            step += 1
            progress.setValue(step)
        # Decimate
        if params['decimate']:
            progress.setLabelText("Decimating mesh...")
            QApplication.processEvents()
            reduction = params['decimate_target'] / 100.0
            try:
                new_mesh = None
                try:
                    new_mesh = mesh.decimate(target_reduction=1.0 - reduction)
                except TypeError:
                    new_mesh = mesh.decimate(1.0 - reduction)
                if new_mesh is None:
                    raise RuntimeError("Decimation did not return a valid mesh.")
                mesh = new_mesh
            except Exception as e:
                QMessageBox.warning(self, "Decimate Error", f"Mesh decimation failed: {e}")
            step += 1
            progress.setValue(step)
        # Remesh logic removed
        progress.setValue(total)
        progress.setLabelText("Done.")
        QApplication.processEvents()
        self.mesh_viewer.mesh = mesh
        self.mesh_viewer.set_display_mode(getattr(self.mesh_viewer, 'display_mode', 'surface'))
        self.update_mesh_info()

    def show_advanced_analysis_dialog(self):
        if self.mesh_viewer.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        dialog = MeshAdvancedAnalysisDialog(self)
        # Connect the Apply button for live/interactive analysis
        dialog.analysis_apply.connect(self.run_advanced_analysis)
        dialog.show()
        # Optionally, connect accepted to run analysis one last time or just close
        # dialog.accepted.connect(lambda: self.run_advanced_analysis(dialog.get_selected_analyses()))

    def run_advanced_analysis(self, params):
        mesh = self.mesh_viewer.mesh
        results = []
        # Mean Curvature
        if params.get('mean_curvature'):
            try:
                scalars = mesh.curvature(curv_type='mean')
                mesh.point_data['MeanCurvature'] = scalars
                # Debug: show min/max/mean
                minv, maxv, meanv = np.nanmin(scalars), np.nanmax(scalars), np.nanmean(scalars)
                QMessageBox.information(self, "Mean Curvature Stats", f"min: {minv:.4g}\nmax: {maxv:.4g}\nmean: {meanv:.4g}")
                self.mesh_viewer.set_colormap('MeanCurvature')
                results.append('Mean Curvature')
            except Exception as e:
                QMessageBox.critical(self, "Curvature Error", str(e))
        # Gaussian Curvature
        if params.get('gaussian_curvature'):
            try:
                scalars = mesh.curvature(curv_type='gaussian')
                mesh.point_data['GaussianCurvature'] = scalars
                self.mesh_viewer.set_colormap('GaussianCurvature')
                results.append('Gaussian Curvature')
            except Exception as e:
                QMessageBox.critical(self, "Curvature Error", f"Gaussian: {e}")
        # Thickness
        if params.get('thickness'):
            try:
                normals = mesh.point_normals
                points = mesh.points
                ray_length = params.get('thickness_ray_length', 100.0)
                thickness = np.zeros(points.shape[0])
                for i, (p, n) in enumerate(zip(points, normals)):
                    hits = mesh.ray_trace(p, p + n * ray_length, first_point=False)[0]
                    if len(hits) > 0:
                        thickness[i] = np.linalg.norm(hits[0] - p)
                    else:
                        thickness[i] = 0
                mesh.point_data['Thickness'] = thickness
                self.mesh_viewer.set_colormap('Thickness')
                results.append('Thickness')
            except Exception as e:
                QMessageBox.critical(self, "Thickness Error", str(e))
        # Geodesic Distance
        if params.get('geodesic'):
            try:
                source = params.get('geodesic_source', 0)
                dists = mesh.geodesic_distance(source)
                mesh.point_data['GeodesicDist'] = dists
                self.mesh_viewer.set_colormap('GeodesicDist')
                results.append('Geodesic Distance')
            except Exception as e:
                QMessageBox.critical(self, "Geodesic Error", str(e))
        # Custom Scalar Field
        if params.get('custom'):
            try:
                mesh.point_data['RandomField'] = np.random.rand(mesh.n_points)
                self.mesh_viewer.set_colormap('RandomField')
                results.append('Custom Scalar Field')
            except Exception as e:
                QMessageBox.critical(self, "Custom Field Error", str(e))
        # Contact/Proximity Analysis (minimum distance between surfaces)
        if params.get('contact'):
            try:
                # For now, compute min distance between all points (mesh to itself, as a stub)
                from scipy.spatial import cKDTree
                points = mesh.points
                tree = cKDTree(points)
                dists, idxs = tree.query(points, k=2)  # k=2: nearest neighbor (self is 0)
                min_dist = np.min(dists[:, 1])  # skip self (0)
                mesh.field_data['MinContactDistance'] = min_dist
                QMessageBox.information(self, "Contact/Proximity Analysis", f"Minimum distance between mesh points: {min_dist:.4g}")
                results.append('Contact/Proximity (min distance)')
            except Exception as e:
                QMessageBox.critical(self, "Contact/Proximity Error", str(e))
        # Export Results
        if params.get('export_results'):
            try:
                import csv
                fname, _ = QFileDialog.getSaveFileName(self, "Export Results", "mesh_analysis.csv", "CSV Files (*.csv);;All Files (*)")
                if fname:
                    with open(fname, 'w', newline='') as f:
                        writer = csv.writer(f)
                        # Write header: point_data arrays
                        arrays = list(mesh.point_data.keys())
                        writer.writerow(['PointIndex'] + arrays)
                        for i in range(mesh.n_points):
                            row = [i] + [mesh.point_data[a][i] if a in mesh.point_data else '' for a in arrays]
                            writer.writerow(row)
                    QMessageBox.information(self, "Export Results", f"Exported point data arrays to {fname}")
                    results.append('Exported Results')
            except Exception as e:
                QMessageBox.critical(self, "Export Error", str(e))
        if results:
            QMessageBox.information(self, "Analysis Complete", f"Computed: {', '.join(results)}. Showing last result.")
        self.update_mesh_info()

    def show_plane_definition_choice(self):
        if self.mesh_viewer.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        # --- Directly open the new tabbed dialog, no message box ---
        outside_opacity = getattr(self, '_outside_opacity', 0.18)
        dialog = PlaneByPointsDialog(self.mesh_viewer, self, outside_opacity=outside_opacity)
        def on_done():
            plane_params = getattr(dialog, 'plane_params', None)
            slab_thickness = getattr(dialog, 'slab_thickness', None)
            slab_side = getattr(dialog, '_slab_side', 'centered')
            if plane_params is not None and slab_thickness is not None:
                self._last_plane_params = plane_params
                self._last_slab_thickness = slab_thickness
                self._last_slab_side = slab_side
                # --- Add persistent analysis plane ---
                origin = np.array(plane_params['origin'])
                normal = np.array(plane_params['normal'])
                mesh = self.mesh_viewer.mesh
                bounds = np.array(mesh.bounds).reshape(3, 2)
                size = np.linalg.norm(bounds[:, 1] - bounds[:, 0]) * 0.6 if mesh is not None else 1.0
                plane = pv.Plane(center=origin, direction=normal, i_size=size, j_size=size)
                self.mesh_viewer.set_analysis_plane(plane, color='yellow', opacity=0.4)
                self.action_show_analysis_plane.setChecked(True)
            try:
                self.opacity_spin.valueChanged.disconnect(dialog.set_outside_opacity)
            except Exception:
                pass
            dialog.deleteLater()
        dialog.finished.connect(on_done)
        self.opacity_spin.valueChanged.connect(dialog.set_outside_opacity)
        dialog.show()
        # Show slab thickness controls as soon as plane is defined
        self.slab_control_frame.setVisible(True)

    def show_plane_canonical(self):
        dialog = PlaneCanonicalDialog(self.mesh_viewer, self)
        def on_done():
            plane_params = getattr(dialog, 'plane_params', None)
            slab_thickness = getattr(dialog, 'slab_thickness', None)
            if plane_params is not None and slab_thickness is not None:
                self._last_plane_params = plane_params
                self._last_slab_thickness = slab_thickness
                # --- Add persistent analysis plane ---
                origin = np.array(plane_params['origin'])
                normal = np.array(plane_params['normal'])
                mesh = self.mesh_viewer.mesh
                bounds = np.array(mesh.bounds).reshape(3, 2)
                size = np.linalg.norm(bounds[:, 1] - bounds[:, 0]) * 0.6 if mesh is not None else 1.0
                plane = pv.Plane(center=origin, direction=normal, i_size=size, j_size=size)
                self.mesh_viewer.set_analysis_plane(plane, color='yellow', opacity=0.4)
                self.action_show_analysis_plane.setChecked(True)
            dialog.deleteLater()
        dialog.finished.connect(on_done)
        dialog.show()

    def run_deformation_analysis(self, plane_params, slab_thickness, slab_side='centered'):
        # plane_params: dict with 'origin' and 'normal' (numpy arrays)
        # slab_thickness: float
        # slab_side: 'centered', 'positive', or 'negative'
        mesh = self.mesh_viewer.mesh
        if mesh is None:
            QMessageBox.warning(self, "No Mesh", "Please load a mesh first.")
            return
        import numpy as np
        points = mesh.points
        origin = np.array(plane_params['origin'])
        normal = np.array(plane_params['normal']) / np.linalg.norm(plane_params['normal'])
        distances = np.dot(points - origin, normal)
        # Mask for points within slab
        if slab_side == 'centered':
            mask = np.abs(distances) <= (slab_thickness / 2)
        elif slab_side == 'positive':
            mask = (distances >= 0) & (distances <= slab_thickness)
        elif slab_side == 'negative':
            mask = (distances <= 0) & (distances >= -slab_thickness)
        else:
            mask = np.abs(distances) <= (slab_thickness / 2)
        mesh.point_data['Deformation'] = distances
        deformation_masked = distances.copy()
        deformation_masked[~mask] = np.nan
        mesh.point_data['DeformationMasked'] = deformation_masked
        # Set color for outside-slab region (nan_color)
        prev_mask = getattr(self, '_last_deformation_mask', None)
        self.mesh_viewer.set_colormap('DeformationMasked', nan_color="#e0e0e0", nan_opacity=getattr(self, '_outside_opacity', 0.15), slab_aware=True)
        # Always update colorbar here, not in _on_slab_thickness_changed
        try:
            self.mesh_viewer.plotter.remove_scalar_bar()
        except Exception:
            pass
        self.mesh_viewer.plotter.render()  # Ensure mesh is rendered before adding colorbar
        self.mesh_viewer.plotter.add_scalar_bar(title="Deformation (m)", vertical=True, fmt="%.3f", interactive=False, position_x=0.88)
        self._last_deformation_mask = mask.copy()
        self.mesh_viewer.plotter.render()
        self.statusBar().showMessage("Deformation heatmap computed and displayed.", 4000)
        self.update_mesh_info()

    def _init_slab_thickness_control(self):
        # Add slab thickness slider/spinbox below the 3D viewer, initially hidden
        self.slab_control_frame = QFrame(self)
        self.slab_control_frame.setFrameShape(QFrame.StyledPanel)
        self.slab_control_frame.setVisible(False)
        layout = QHBoxLayout(self.slab_control_frame)
        layout.setContentsMargins(8, 2, 8, 2)
        layout.addWidget(QLabel("Slab Thickness:"))
        self.slab_slider = QSlider(Qt.Horizontal)
        self.slab_slider.setMinimum(1)
        self.slab_slider.setMaximum(200)
        self.slab_slider.setValue(20)
        self.slab_slider.setSingleStep(1)
        self.slab_slider.setTickInterval(10)
        self.slab_slider.setTickPosition(QSlider.TicksBelow)
        layout.addWidget(self.slab_slider)
        self.slab_spin = QDoubleSpinBox()
        self.slab_spin.setRange(0.01, 2.0)  # Match slider range: 1-200 maps to 0.01-2.0
        self.slab_spin.setDecimals(3)
        self.slab_spin.setSingleStep(0.01)
        self.slab_spin.setValue(0.20)
        self.slab_spin.setSuffix(" m")
        layout.addWidget(self.slab_spin)
        # --- Add outside opacity control ---
        layout.addWidget(QLabel("Outside Opacity:"))
        self.opacity_slider = QSlider(Qt.Horizontal)
        self.opacity_slider.setMinimum(0)
        self.opacity_slider.setMaximum(100)
        self.opacity_slider.setValue(15)
        self.opacity_slider.setSingleStep(1)
        self.opacity_spin = QDoubleSpinBox()
        self.opacity_spin.setRange(0.0, 1.0)
        self.opacity_spin.setDecimals(2)
        self.opacity_spin.setSingleStep(0.01)
        self.opacity_spin.setValue(0.15)
        layout.addWidget(self.opacity_slider)
        layout.addWidget(self.opacity_spin)
        # Sync slider and spinbox with proper scaling
        # Opacity: slider 0-100 maps to spinbox 0.0-1.0
        self.opacity_slider.valueChanged.connect(lambda v: self.opacity_spin.setValue(v/100.0))
        self.opacity_spin.valueChanged.connect(lambda v: self.opacity_slider.setValue(int(v*100)))
        self.opacity_spin.valueChanged.connect(self._on_opacity_changed)

        # Slab thickness: slider 1-200 maps to spinbox 0.01-2.0 (in meters)
        self.slab_slider.valueChanged.connect(lambda v: self.slab_spin.setValue(v/100.0))
        self.slab_spin.valueChanged.connect(lambda v: self.slab_slider.setValue(int(v*100)))
        self.slab_spin.valueChanged.connect(self._on_slab_thickness_changed)

        # Debug: Add some logging to verify connections work
        self.slab_slider.valueChanged.connect(lambda v: print(f"[DEBUG] Slab slider changed to {v}, spinbox will be {v/100.0}"))
        self.opacity_slider.valueChanged.connect(lambda v: print(f"[DEBUG] Opacity slider changed to {v}, spinbox will be {v/100.0}"))
        # Add below central widget
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0,0,0,0)
        main_layout.setSpacing(0)
        main_layout.addWidget(self.mesh_viewer)
        main_layout.addWidget(self.slab_control_frame)
        container = QWidget()
        container.setLayout(main_layout)
        self.setCentralWidget(container)

    def _on_opacity_changed(self, value):
        self._outside_opacity = float(value)
        # Live update the heatmap if active
        if hasattr(self, '_last_plane_params') and self.mesh_viewer.mesh is not None:
            self._on_slab_thickness_changed(self.slab_spin.value())

    def _on_slab_thickness_changed(self, value):
        # Live update the heatmap when slab thickness changes
        if not hasattr(self, '_last_plane_params') or self.mesh_viewer.mesh is None:
            return
        import numpy as np
        mesh = self.mesh_viewer.mesh
        plane_params = self._last_plane_params
        slab_thickness = float(value)
        slab_side = getattr(self, '_last_slab_side', 'centered')
        points = mesh.points
        origin = np.array(plane_params['origin'])
        normal = np.array(plane_params['normal']) / np.linalg.norm(plane_params['normal'])
        distances = np.dot(points - origin, normal)
        if slab_side == 'centered':
            mask = np.abs(distances) <= (slab_thickness / 2)
        elif slab_side == 'positive':
            mask = (distances >= 0) & (distances <= slab_thickness)
        elif slab_side == 'negative':
            mask = (distances <= 0) & (distances >= -slab_thickness)
        else:
            mask = np.abs(distances) <= (slab_thickness / 2)
        mesh.point_data['Deformation'] = distances
        deformation_masked = distances.copy()
        deformation_masked[~mask] = np.nan
        mesh.point_data['DeformationMasked'] = deformation_masked
        self.mesh_viewer.set_colormap('DeformationMasked', nan_color="#e0e0e0", nan_opacity=getattr(self, '_outside_opacity', 0.15), slab_aware=True)
        self._last_slab_thickness = slab_thickness
        # Always update colorbar here to ensure it stays on the right
        try:
            self.mesh_viewer.plotter.remove_scalar_bar()
        except Exception:
            pass
        self.mesh_viewer.plotter.render()  # Ensure mesh is rendered before adding colorbar
        self.mesh_viewer.plotter.add_scalar_bar(title="Deformation (m)", vertical=True, fmt="%.3f", interactive=False, position_x=0.88)
        self.mesh_viewer.plotter.render()

    def show_heatmap_clicked(self):
        # Called when the user clicks the Show Deformation Heatmap button
        if not hasattr(self, '_last_plane_params') or not hasattr(self, '_last_slab_thickness'):
            QMessageBox.warning(self, "No Plane Defined", "Please define a plane and slab thickness first.")
            return
        if self._last_plane_params is None or self._last_slab_thickness is None:
            QMessageBox.warning(self, "No Plane Defined", "Please define a plane and slab thickness first.")
            return
        slab_side = getattr(self, '_last_slab_side', 'centered')
        self.run_deformation_analysis(self._last_plane_params, self._last_slab_thickness, slab_side)

    def show_vector_field_clicked(self):
        """
        Show the vector field (direction from plane) for each mesh point in the 3D view, using slab logic.
        """
        # Get plane parameters and slab settings
        plane_params = getattr(self, '_last_plane_params', None)
        slab_thickness = getattr(self, '_last_slab_thickness', None)
        slab_side = getattr(self, '_last_slab_side', 'centered')

        if plane_params is None or slab_thickness is None:
            QMessageBox.warning(self, "No Plane Defined",
                              "Please define an analysis plane first before showing vector field.")
            return

        self.mesh_viewer.show_vector_field("Displacement", scale=1.0, color="red",
                                         plane_params=plane_params,
                                         slab_thickness=slab_thickness,
                                         slab_side=slab_side)

    def show_crop_box_dialog(self):
        if self.mesh_viewer.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        from src.gui.crop_box_dialog import CropBoxDialog
        dialog = CropBoxDialog(self.mesh_viewer, parent=self)
        dialog.show()  # Use show() for non-modal, non-blocking dialog

    def save_cropped_mesh(self):
        from src.gui.save_cropped_mesh import save_cropped_mesh
        mesh = getattr(self.mesh_viewer, 'mesh', None)
        save_cropped_mesh(mesh, parent=self)

    def save_screenshot_dialog(self):
        from gui.screenshot_dialog import ScreenshotDialog
        from PySide6.QtWidgets import QDialog
        dialog = ScreenshotDialog(self)
        if dialog.exec() == QDialog.Accepted:
            mode = dialog.get_mode()
            transparent = dialog.is_transparent()
            file_path, _ = QFileDialog.getSaveFileName(self, "Save Screenshot", "screenshot.png", "PNG Image (*.png);;JPEG Image (*.jpg *.jpeg)")
            if file_path:
                try:
                    scale = 1 if mode == 'normal' else 3
                    img = self.mesh_viewer.plotter.screenshot(return_img=True, scale=scale, transparent_background=transparent)
                    import imageio
                    imageio.imwrite(file_path, img)
                    QMessageBox.information(self, "Screenshot Saved", f"Screenshot saved to: {file_path}")
                except Exception as e:
                    QMessageBox.critical(self, "Error", f"Failed to save screenshot:\n{str(e)}")

    def show_section_with_plane_dialog(self):
        if self.mesh_viewer.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        # Reuse plane definition dialog
        dialog = PlaneDefinitionChoiceDialog(self)
        result = dialog.exec()
        if result == 1:
            plane_dialog = PlaneByPointsDialog(self.mesh_viewer, self)
            if plane_dialog.exec() == QDialog.Accepted:
                plane_params = getattr(plane_dialog, 'plane_params', None)
                if plane_params is not None:
                    origin = np.array(plane_params['origin'])
                    normal = np.array(plane_params['normal'])
                    self.extract_and_export_section(origin, normal)
        elif result == 2:
            plane_dialog = PlaneCanonicalDialog(self.mesh_viewer, self)
            if plane_dialog.exec() == QDialog.Accepted:
                plane_params = getattr(plane_dialog, 'plane_params', None)
                if plane_params is not None:
                    origin = np.array(plane_params['origin'])
                    normal = np.array(plane_params['normal'])
                    self.extract_and_export_section(origin, normal)

    def extract_and_export_section(self, origin, normal):
        mesh = self.mesh_viewer.mesh
        section = extract_section_curves(mesh, origin, normal)
        if section.n_points == 0:
            QMessageBox.warning(self, "Section", "No intersection found.")
            return
        # Preview section in viewer
        self.mesh_viewer.plotter.clear()
        self.mesh_viewer.plotter.add_mesh(section, color="red", line_width=3, render_lines_as_tubes=True)
        self.mesh_viewer.plotter.reset_camera()
        self.mesh_viewer.plotter.render()
        # Ask to export
        ret = QMessageBox.question(self, "Export Section", "Export section to DXF?", QMessageBox.Yes | QMessageBox.No)
        if ret == QMessageBox.Yes:
            fname, _ = QFileDialog.getSaveFileName(self, "Export Section DXF", "section.dxf", "DXF Files (*.dxf);;All Files (*)")
            if fname:
                try:
                    export_section_to_dxf(section, fname)
                    QMessageBox.information(self, "Export", f"Section exported to {fname}")
                except Exception as e:
                    QMessageBox.critical(self, "Export Error", str(e))

    def toggle_analysis_plane(self, checked=None):
        if checked is None:
            checked = self.action_show_analysis_plane.isChecked()
        self.mesh_viewer.show_analysis_plane(checked)
        self.action_show_analysis_plane.setChecked(self.mesh_viewer._analysis_plane_visible)

    def update_mask_and_colorbar(self):
        # Centralized update for mask and colorbar, reflecting only values present in the selected slab region
        mask = self.get_current_mask()
        if mask is None or not mask.any():
            self.mesh_viewer.clear_colorbar()
            self.mesh_viewer.show_message("No points in the selected slab region.")
            return
        self.mesh_viewer.update_mask(mask)
        self.mesh_viewer.update_colorbar(mask)

    def get_current_mask(self):
        # Determine the current mask based on the last used plane parameters and slab thickness
        if not hasattr(self, '_last_plane_params') or self.mesh_viewer.mesh is None:
            return None
        import numpy as np
        mesh = self.mesh_viewer.mesh
        plane_params = self._last_plane_params
        slab_thickness = getattr(self, '_last_slab_thickness', 1.0)
        slab_side = getattr(self, '_last_slab_side', 'centered')
        points = mesh.points
        origin = np.array(plane_params['origin'])
        normal = np.array(plane_params['normal']) / np.linalg.norm(plane_params['normal'])
        distances = np.dot(points - origin, normal)
        if slab_side == 'centered':
            mask = np.abs(distances) <= (slab_thickness / 2)
        elif slab_side == 'positive':
            mask = (distances >= 0) & (distances <= slab_thickness)
        elif slab_side == 'negative':
            mask = (distances <= 0) & (distances >= -slab_thickness)
        else:
            mask = np.abs(distances) <= (slab_thickness / 2)
        return mask

    def update_status_bar(self):
        # Projection mode
        proj = self.get_projection_mode()
        self._projection_label.setText(f"View: {proj}")
        # Unit
        self._unit_label.setText(f"Unit: {self.mesh_unit}")

    def get_projection_mode(self):
        # Try to get projection mode from mesh_viewer.plotter
        try:
            camera = self.mesh_viewer.plotter.camera
            if hasattr(camera, 'GetParallelProjection'):
                if camera.GetParallelProjection():
                    return "Orthogonal"
                else:
                    return "Perspective"
        except Exception:
            pass
        return "Unknown"

    def set_mesh_unit(self, unit):
        self.mesh_unit = unit
        self.update_status_bar()

    def set_projection_mode(self, mode):
        # mode: 'perspective' or 'orthogonal'
        cam = self.mesh_viewer.plotter.camera
        if mode == 'orthogonal':
            cam.ParallelProjectionOn()
        else:
            cam.ParallelProjectionOff()
        self.mesh_viewer.plotter.render()
        self.update_status_bar()

    def export_deformation_csv(self):
        """Export deformation data to CSV file"""
        try:
            if not hasattr(self.mesh_viewer, 'mesh') or self.mesh_viewer.mesh is None:
                QMessageBox.warning(self, "No Data", "No mesh data available to export.")
                return

            mesh = self.mesh_viewer.mesh
            if 'DeformationMasked' not in mesh.point_data and 'deformation' not in mesh.point_data:
                QMessageBox.warning(self, "No Deformation Data", "No deformation data available to export.")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export Deformation CSV", "deformation_data.csv",
                "CSV Files (*.csv);;All Files (*)"
            )

            if file_path:
                import csv
                deformation_data = mesh.point_data.get('DeformationMasked', mesh.point_data.get('deformation'))
                points = mesh.points

                with open(file_path, 'w', newline='') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(['X', 'Y', 'Z', 'Deformation'])

                    for i, (point, deform) in enumerate(zip(points, deformation_data)):
                        writer.writerow([point[0], point[1], point[2], deform])

                QMessageBox.information(self, "Export Complete", f"Deformation data exported to {file_path}")

        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Failed to export CSV: {e}")

    def save_session_dialog(self):
        """Save current analysis session"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Save Session", "analysis_session.json",
                "Session Files (*.json);;All Files (*)"
            )

            if file_path:
                # TODO: Implement session saving
                QMessageBox.information(self, "Save Session", "Session saving feature coming soon!")

        except Exception as e:
            QMessageBox.critical(self, "Save Error", f"Failed to save session: {e}")

    def load_session_dialog(self):
        """Load previous analysis session"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Load Session", "",
                "Session Files (*.json);;All Files (*)"
            )

            if file_path:
                # TODO: Implement session loading
                QMessageBox.information(self, "Load Session", "Session loading feature coming soon!")

        except Exception as e:
            QMessageBox.critical(self, "Load Error", f"Failed to load session: {e}")

    def undo_last_operation(self):
        """Undo the last operation"""
        try:
            # TODO: Implement undo functionality
            QMessageBox.information(self, "Undo", "Undo feature coming soon!")

        except Exception as e:
            QMessageBox.critical(self, "Undo Error", f"Failed to undo operation: {e}")

    def save_project_dialog(self):
        """Save current project"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Save Project", "project.json",
                "Project Files (*.json);;All Files (*)"
            )

            if file_path:
                # TODO: Implement project saving
                QMessageBox.information(self, "Save Project", "Project saving feature coming soon!")

        except Exception as e:
            QMessageBox.critical(self, "Save Error", f"Failed to save project: {e}")

    def show_help_dialog(self):
        """Show help and documentation"""
        try:
            help_text = """
DeformViz 3D - Help & Documentation

WORKFLOW:
1. Load 3D Mesh (Ctrl+O) - Load your mesh file
2. Process Mesh - Clean and prepare the mesh
3. Define Plane - Set reference plane for analysis
4. Crop/Slice - Edit the mesh as needed
5. Show Heatmap - Visualize deformation data
6. Export Results - Save your analysis

KEYBOARD SHORTCUTS:
• Ctrl+O - Load mesh
• Ctrl+S - Save project
• Ctrl+Z - Undo
• Ctrl+E - Export CSV
• F1 - Show this help
• F12 - Take screenshot

VIEW CONTROLS:
• Use left toolbar for standard views
• Mouse: Rotate, zoom, pan
• Toggle perspective/orthographic projection

For more help, visit the documentation or contact support.
"""
            QMessageBox.information(self, "DeformViz 3D - Help", help_text)

        except Exception as e:
            QMessageBox.critical(self, "Help Error", f"Failed to show help: {e}")

    def quick_screenshot(self):
        """Take a quick screenshot at current resolution"""
        try:
            import os
            from datetime import datetime

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"
            file_path = os.path.join(os.path.expanduser("~"), filename)

            # Take screenshot at current resolution
            img = self.mesh_viewer.plotter.screenshot(return_img=True, scale=1)

            import imageio
            imageio.imwrite(file_path, img)

            QMessageBox.information(self, "Screenshot Saved",
                                  f"Quick screenshot saved to:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "Screenshot Error", f"Failed to take screenshot: {e}")

    def show_colormap_dialog(self):
        """Show colormap selection dialog"""
        try:
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox, QSlider, QCheckBox

            # Check if we have mesh data to apply colormap to
            if self.mesh_viewer.mesh is None:
                QMessageBox.warning(self, "No Mesh", "Load a mesh first before selecting colormap.")
                return

            if 'DeformationMasked' not in self.mesh_viewer.mesh.point_data:
                QMessageBox.warning(self, "No Analysis Data", "Perform deformation analysis first before changing colormap.")
                return

            dialog = QDialog(self)
            dialog.setWindowTitle("Select Colormap")
            dialog.setMinimumWidth(400)
            layout = QVBoxLayout(dialog)

            # Colormap selection
            layout.addWidget(QLabel("Choose Colormap:"))
            colormap_combo = QComboBox()
            colormaps = [
                'viridis', 'plasma', 'inferno', 'magma', 'cividis',
                'coolwarm', 'bwr', 'seismic', 'RdBu', 'RdYlBu',
                'jet', 'rainbow', 'turbo', 'hot', 'cool'
            ]
            colormap_combo.addItems(colormaps)
            colormap_combo.setCurrentText('coolwarm')  # Default
            layout.addWidget(colormap_combo)

            # Reverse colormap option
            reverse_checkbox = QCheckBox("Reverse Colormap")
            layout.addWidget(reverse_checkbox)

            # Preview and buttons
            layout.addWidget(QLabel("Click 'Apply' to see changes:"))

            button_layout = QHBoxLayout()
            apply_btn = QPushButton("Apply")
            cancel_btn = QPushButton("Cancel")
            button_layout.addWidget(apply_btn)
            button_layout.addWidget(cancel_btn)
            layout.addLayout(button_layout)

            def apply_colormap():
                try:
                    colormap_name = colormap_combo.currentText()
                    if reverse_checkbox.isChecked():
                        colormap_name += '_r'  # Reverse colormap

                    # Apply the new colormap
                    mesh = self.mesh_viewer.mesh
                    if mesh and 'DeformationMasked' in mesh.point_data:
                        # Update the colormap
                        self.mesh_viewer.set_colormap('DeformationMasked',
                                                    nan_color="#e0e0e0",
                                                    nan_opacity=getattr(self, '_outside_opacity', 0.15),
                                                    slab_aware=True)

                        # Update the scalar bar with new colormap
                        try:
                            self.mesh_viewer.plotter.update_scalar_bar_range(
                                mesh.point_data['DeformationMasked'].min(),
                                mesh.point_data['DeformationMasked'].max()
                            )
                            # Set the colormap on the mesh
                            actors = self.mesh_viewer.plotter.renderer.actors
                            for actor in actors.values():
                                if hasattr(actor, 'mapper') and actor.mapper:
                                    actor.mapper.lookup_table.SetColorScheme(colormap_name)
                        except Exception as e:
                            print(f"Warning: Could not update colormap: {e}")

                        self.mesh_viewer.plotter.render()
                        QMessageBox.information(dialog, "Colormap Applied", f"Applied colormap: {colormap_name}")

                except Exception as e:
                    QMessageBox.critical(dialog, "Error", f"Failed to apply colormap: {e}")

            apply_btn.clicked.connect(apply_colormap)
            cancel_btn.clicked.connect(dialog.reject)

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "Colormap Error", f"Failed to show colormap dialog: {e}")

def run_app():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
