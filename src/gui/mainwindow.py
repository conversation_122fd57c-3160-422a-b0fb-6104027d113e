import sys
import os
from PySide6.QtWidgets import (QA<PERSON>lication, QMainWindow, QToolBar, QDockWidget, QTabWidget, QWidget, QVBoxLayout, QLabel, QListWidget, QTextEdit, QHBoxLayout, QPushButton, QFileDialog, QMessageBox, QSlider, QDoubleSpinBox, QFrame, QInputDialog, QDialog, QVBoxLayout, QGroupBox, QFormLayout, QRadioButton, QButtonGroup, QStatusBar)
from PySide6.QtCore import Qt, Signal, QSettings, QDateTime, QTimer
from src.gui.meshviewer import MeshViewerWidget
from src.gui.mesh_display import MeshDisplayModeDialog
from src.gui.mesh_processing import MeshProcessingDialog
from src.gui.mesh_advanced_analysis import MeshAdvancedAnalysisDialog
from src.gui.plane_definition_choice import PlaneDefinitionChoiceDialog
from src.gui.plane_by_points import PlaneByPointsDialog
from src.gui.plane_canonical import PlaneCanonicalDialog
from src.gui.crop_slice_mesh import CropSliceMeshDialog
from src.gui.analysis_wizard import AnalysisWizard
from PySide6.QtGui import QIcon, QColor, QMovie
from PySide6.QtWidgets import QColorDialog
import os
from src.gui.section_export import extract_section_curves, export_section_to_dxf
from src.core import mesh_utils
import numpy as np
import pyvista as pv

def run_app():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        from src.core.config import config
        self.setWindowTitle(config.app_full_name)
        self.setGeometry(100, 100, 1000, 700)

        # Enable drag and drop
        self.setAcceptDrops(True)

        # Set application icon
        self._set_app_icon()
        self.mesh_unit = "meters"  # Default unit, moved before _init_ui

        # Initialize settings for recent files
        self.settings = QSettings("DeformViz3D", "DeformViz3D")
        self.max_recent_files = 10
        self.recent_files_actions = []

        # Initialize auto-save functionality
        self._setup_auto_save()
        self._init_ui()
        self._init_menu_bar()
        self._init_slab_thickness_control()
        self._update_recent_files_menu()
        self._analysis_plane_action = None

        # Initialize enhanced status feedback
        self._setup_enhanced_status()

        # Initialize split-screen functionality
        self._setup_split_screen()

        self._loading_label = None
        self._loading_spinner = None

    def _set_app_icon(self):
        """Set the application icon"""
        try:
            from PySide6.QtGui import QIcon
            icon_path = "data/icons/app_icon.png"
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            print(f"Could not set app icon: {e}")

    def _init_menu_bar(self):
        """Initialize the menu bar with File menu and recent files"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu('&File')

        # Open action
        open_action = file_menu.addAction('&Open Mesh...')
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.load_mesh_dialog)

        # Recent files submenu
        self.recent_files_menu = file_menu.addMenu('Recent Files')

        file_menu.addSeparator()

        # Save actions
        save_session_action = file_menu.addAction('&Save Session...')
        save_session_action.setShortcut('Ctrl+S')
        save_session_action.triggered.connect(self.save_session_dialog)

        load_session_action = file_menu.addAction('&Load Session...')
        load_session_action.setShortcut('Ctrl+L')
        load_session_action.triggered.connect(self.load_session_dialog)

        file_menu.addSeparator()

        # Export actions
        export_menu = file_menu.addMenu('&Export')

        export_screenshot_action = export_menu.addAction('Screenshot...')
        export_screenshot_action.triggered.connect(self.save_screenshot_dialog)

        export_csv_action = export_menu.addAction('Analysis Data (CSV)...')
        export_csv_action.triggered.connect(self.export_deformation_csv)

        file_menu.addSeparator()

        # Exit action
        exit_action = file_menu.addAction('E&xit')
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)

        # Configuration menu
        config_menu = menubar.addMenu('&Configuration')

        self.action_show_sidebar = config_menu.addAction("Show Sidebar")
        self.action_show_sidebar.setCheckable(True)
        self.action_show_sidebar.setChecked(True)
        self.action_show_sidebar.triggered.connect(self.toggle_sidebar)

        self.action_show_left_toolbar = config_menu.addAction("Show Left Toolbar")
        self.action_show_left_toolbar.setCheckable(True)
        self.action_show_left_toolbar.setChecked(True)
        self.action_show_left_toolbar.triggered.connect(self.toggle_left_toolbar)

        self.action_set_bg_color = config_menu.addAction("Set Background Color...")
        self.action_set_bg_color.triggered.connect(self.pick_background_color)

        config_menu.addSeparator()

        self.action_show_bbox = config_menu.addAction("Show Bounding Box")
        self.action_show_bbox.setCheckable(True)
        self.action_show_bbox.setChecked(False)
        self.action_show_bbox.triggered.connect(lambda checked: self.mesh_viewer.show_bounding_box(checked))

        self.action_show_analysis_plane = config_menu.addAction("Show Analysis Plane")
        self.action_show_analysis_plane.setCheckable(True)
        self.action_show_analysis_plane.setChecked(True)
        self.action_show_analysis_plane.triggered.connect(self.toggle_analysis_plane)

        self.action_set_unit = config_menu.addAction("Set Unit...")
        self.action_set_unit.triggered.connect(self.show_set_unit_dialog)

        # Help menu
        help_menu = menubar.addMenu('&Help')

        # Quick Start Guide
        quick_start_action = help_menu.addAction('&Quick Start Guide')
        quick_start_action.setShortcut('F1')
        quick_start_action.triggered.connect(self.show_quick_start_guide)

        # Detailed Workflow
        workflow_action = help_menu.addAction('&Detailed Workflow...')
        workflow_action.triggered.connect(self.show_detailed_workflow)

        # Analysis Wizard
        wizard_help_action = help_menu.addAction('&Analysis Wizard Help')
        wizard_help_action.triggered.connect(self.show_wizard_help)

        help_menu.addSeparator()

        # Keyboard Shortcuts
        shortcuts_action = help_menu.addAction('&Keyboard Shortcuts')
        shortcuts_action.triggered.connect(self.show_keyboard_shortcuts)

        # Troubleshooting
        troubleshooting_action = help_menu.addAction('&Troubleshooting')
        troubleshooting_action.triggered.connect(self.show_troubleshooting)

        help_menu.addSeparator()

        # About
        about_action = help_menu.addAction('&About DeformViz 3D')
        about_action.triggered.connect(self.show_about_dialog)

    def _init_ui(self):
        # Central widget: MeshViewerWidget
        self.mesh_viewer = MeshViewerWidget()
        self.setCentralWidget(self.mesh_viewer)

        # Status bar for projection mode and unit
        self.status = self.statusBar()
        self._projection_label = QLabel()
        self._unit_label = QLabel()
        self.status.addPermanentWidget(self._projection_label)
        self.status.addPermanentWidget(self._unit_label)
        self.update_status_bar()

        # Toolbar
        toolbar = QToolBar("Main Toolbar")
        self.addToolBar(toolbar)
        # Helper function for icons - use our new icon directory
        def icon(name):
            # Try src/icons first (new location), then data/icons (legacy)
            icon_paths = [
                os.path.join("src", "icons", name),
                os.path.join("data", "icons", name),
                os.path.join("icons", name)  # Direct icons folder
            ]

            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    return QIcon(icon_path)

            # Fallback: create a simple colored square as placeholder
            from PySide6.QtGui import QPixmap, QPainter, QColor
            pixmap = QPixmap(24, 24)
            pixmap.fill(QColor(100, 100, 100))  # Gray placeholder
            return QIcon(pixmap)
        # === CORE WORKFLOW GROUP ===
        # Essential functions that users need immediately

        # Analysis Wizard - First button for new users
        wizard_action = toolbar.addAction(icon("wizard.svg"), "Analysis Wizard")
        wizard_action.setToolTip("Guided analysis workflow for new users\n• Step-by-step instructions\n• Load mesh → Define plane → Run analysis\n• Perfect for first-time users")
        wizard_action.triggered.connect(self.show_analysis_wizard)

        toolbar.addSeparator()  # Separate wizard from other tools

        load_action = toolbar.addAction(icon("load.svg"), "Load 3D Mesh")
        load_action.triggered.connect(self.load_mesh_dialog)
        load_action.setToolTip("Load a 3D mesh file for analysis (Ctrl+O)")
        load_action.setShortcut("Ctrl+O")

        save_action = toolbar.addAction(icon("save.svg"), "Save Project")
        save_action.triggered.connect(self.save_project_dialog)
        save_action.setToolTip("Save the current project (Ctrl+S)")
        save_action.setShortcut("Ctrl+S")

        help_action = toolbar.addAction(icon("help.svg"), "Help")
        help_action.triggered.connect(self.show_help_dialog)
        help_action.setToolTip("Show help and documentation (F1)")
        help_action.setShortcut("F1")
        toolbar.addSeparator()

        # === MESH PROCESSING GROUP ===
        # Tools for preparing and processing the mesh
        mesh_display_action = toolbar.addAction(icon("settings.svg"), "Display Settings")
        mesh_display_action.triggered.connect(self.show_mesh_display_mode_dialog)
        mesh_display_action.setToolTip("Configure mesh visualization settings\n• Change display mode (surface, wireframe, points)\n• Adjust colors and transparency\n• Set shading options")

        process_mesh_action = toolbar.addAction(icon("process_mesh.svg"), "Process Mesh")
        process_mesh_action.triggered.connect(self.show_mesh_processing_dialog)
        process_mesh_action.setToolTip("Clean, smooth, and repair the mesh\n• Remove noise and artifacts\n• Fill holes and fix topology\n• Smooth surface irregularities")

        adv_analysis_action = toolbar.addAction(icon("analyze.svg"), "Advanced Analysis")
        adv_analysis_action.triggered.connect(self.show_advanced_analysis_dialog)
        adv_analysis_action.setToolTip("Perform advanced mesh analysis\n• Calculate curvature and thickness\n• Detect anomalies and defects\n• Generate statistical reports")

        # Batch processing
        batch_process_action = toolbar.addAction(icon("batch_process.svg"), "Batch Processing")
        batch_process_action.triggered.connect(self.show_batch_processing_dialog)
        batch_process_action.setToolTip("Process multiple files automatically\n• Batch analysis workflows\n• Automated report generation\n• Time-saving for large datasets")

        toolbar.addSeparator()

        # === MEASUREMENT & QUALITY CONTROL GROUP ===
        # Measurement tools
        measurement_action = toolbar.addAction(icon("measurement.svg"), "Measurement Tools")
        measurement_action.triggered.connect(self.show_measurement_tools)
        measurement_action.setToolTip("Essential measurement capabilities\n• Point-to-point distances\n• Angle measurements\n• Surface area and volume\n• Geometric properties")

        # Quality control
        quality_control_action = toolbar.addAction(icon("quality_control.svg"), "Quality Control")
        quality_control_action.triggered.connect(self.show_quality_control)
        quality_control_action.setToolTip("Quality control and tolerance checking\n• Tolerance compliance\n• Statistical analysis\n• Automated reporting\n• Process capability")

        toolbar.addSeparator()

        # === ADVANCED REPORTING GROUP ===
        # Advanced reporting
        advanced_report_action = toolbar.addAction(icon("advanced_report.svg"), "Advanced Reports")
        advanced_report_action.triggered.connect(self.show_advanced_reporting)
        advanced_report_action.setToolTip("Comprehensive report generation\n• PDF, HTML, Excel, Word, PowerPoint\n• Professional layouts with charts\n• Customizable templates\n• Multi-format export")
        toolbar.addSeparator()

        # === ANALYSIS SETUP GROUP ===
        # Tools for setting up deformation analysis
        self.action_define_plane = toolbar.addAction(icon("plane.svg"), "Define Plane")
        self.action_define_plane.triggered.connect(self.show_plane_definition_choice)
        self.action_define_plane.setToolTip("Define the reference plane for deformation analysis\n• Choose from canonical planes (XY, XZ, YZ)\n• Define by 3 points\n• Set custom orientation")

        flip_direction_action = toolbar.addAction(icon("flip_direction.svg"), "Flip Analysis Direction")
        flip_direction_action.setToolTip("Flip the direction of deformation analysis\n• Reverse positive/negative deformation\n• Change analysis orientation\n• Useful for comparing different perspectives")
        toolbar.addSeparator()

        # === EDITING TOOLS GROUP ===
        # Tools for modifying and editing the mesh
        crop_box_action = toolbar.addAction(icon("crop_box.svg"), "Crop Mesh (Box)")
        crop_box_action.setToolTip("Interactively crop the mesh using a box\n• Select region of interest\n• Remove unwanted parts\n• Focus analysis on specific areas")
        crop_box_action.triggered.connect(self.show_crop_box_dialog)

        slice_with_plane_action = toolbar.addAction(icon("slice_plane.svg"), "Slice with Plane")
        slice_with_plane_action.setToolTip("Slice the mesh with a plane to create cross-sections\n• Generate 2D profiles\n• Export to DXF format\n• Analyze internal structure")
        slice_with_plane_action.triggered.connect(self.show_section_with_plane_dialog)

        undo_action = toolbar.addAction(icon("undo.svg"), "Undo")
        undo_action.setToolTip("Undo last operation (Ctrl+Z)\n• Revert recent changes\n• Restore previous state\n• Multiple undo levels supported")
        undo_action.setShortcut("Ctrl+Z")
        undo_action.triggered.connect(self.undo_last_operation)
        toolbar.addSeparator()

        # === VISUALIZATION GROUP ===
        # Tools for visualizing analysis results
        self.action_show_heatmap = toolbar.addAction(icon("heatmap.svg"), "Show Deformation Heatmap")
        self.action_show_heatmap.setToolTip("Display deformation values as a color-coded heatmap\n• Visualize displacement from reference plane\n• Color-coded from blue (negative) to red (positive)\n• Adjustable scale and transparency")
        self.action_show_heatmap.triggered.connect(self.show_heatmap_clicked)

        self.action_show_vector_field = toolbar.addAction(icon("vector_field.svg"), "Show Vector Field")
        self.action_show_vector_field.setToolTip("Show vector field (direction from plane) for each mesh point\n• Display directional arrows\n• Visualize deformation direction\n• Adjustable arrow scale and density")
        self.action_show_vector_field.triggered.connect(self.show_vector_field_clicked)

        colormap_action = toolbar.addAction(icon("color_picker.svg"), "Select Colormap")
        colormap_action.setToolTip("Choose color scheme for visualization\n• Select from predefined color maps\n• Customize color ranges\n• Adjust contrast and brightness")
        colormap_action.triggered.connect(self.show_colormap_dialog)

        # Split-screen comparison
        split_screen_action = toolbar.addAction(icon("split_screen.svg"), "Split-Screen")
        split_screen_action.setToolTip("Toggle split-screen comparison mode\n• Compare different analysis results\n• Side-by-side visualization\n• Copy states between panels")
        split_screen_action.triggered.connect(self.toggle_split_screen)
        split_screen_action.setCheckable(True)

        # === EXPORT & RESULTS GROUP ===
        # Tools for exporting and saving results
        export_results_action = toolbar.addAction(icon("export.svg"), "Export Results")
        export_results_action.triggered.connect(self.export_analysis_results)
        export_results_action.setToolTip("Export analysis results and data\n• Generate comprehensive reports\n• Export multiple file formats\n• Include charts and statistics")

        export_csv_action = toolbar.addAction(icon("export_csv.svg"), "Export CSV")
        export_csv_action.setToolTip("Export deformation data to CSV file (Ctrl+E)\n• Spreadsheet-compatible format\n• Includes X, Y, Z coordinates\n• Deformation values for each point")
        export_csv_action.setShortcut("Ctrl+E")
        export_csv_action.triggered.connect(self.export_deformation_csv)

        save_cropped_action = toolbar.addAction(icon("export_mesh.svg"), "Save Mesh")
        save_cropped_action.setToolTip("Save the currently displayed mesh to file\n• Multiple formats (PLY, STL, OBJ, VTK)\n• Preserves colors and analysis data\n• Optimized for further processing")
        save_cropped_action.triggered.connect(self.save_cropped_mesh)

        # Screenshot with dropdown menu for options
        screenshot_action = toolbar.addAction(icon("screenshot.svg"), "Screenshot")
        screenshot_action.setToolTip("Take screenshot of current view (F12)")
        screenshot_action.setShortcut("F12")
        screenshot_action.triggered.connect(self.quick_screenshot)

        # Create dropdown menu for screenshot options
        from PySide6.QtWidgets import QMenu
        screenshot_menu = QMenu(self)

        quick_screenshot_action = screenshot_menu.addAction("Quick Screenshot")
        quick_screenshot_action.setToolTip("Take quick screenshot at current resolution")
        quick_screenshot_action.triggered.connect(self.quick_screenshot)

        hq_screenshot_action = screenshot_menu.addAction("High-Quality Screenshot")
        hq_screenshot_action.setToolTip("Take high-resolution screenshot with options")
        hq_screenshot_action.triggered.connect(self.save_screenshot_dialog)

        # Add the menu to the action
        screenshot_button = toolbar.widgetForAction(screenshot_action)
        if hasattr(screenshot_button, 'setMenu'):
            screenshot_button.setMenu(screenshot_menu)
        toolbar.addSeparator()

        # === SESSION MANAGEMENT GROUP ===
        # Tools for saving and loading analysis sessions
        save_session_action = toolbar.addAction(icon("save_session.svg"), "Save Session")
        save_session_action.setToolTip("Save current analysis session\n• Preserve all settings and results\n• Include mesh, plane, and visualization\n• Resume work later from exact state")
        save_session_action.triggered.connect(self.save_session_dialog)

        load_session_action = toolbar.addAction(icon("load_session.svg"), "Load Session")
        load_session_action.setToolTip("Load previous analysis session\n• Restore complete workspace\n• Reload mesh and analysis settings\n• Continue from where you left off")
        load_session_action.triggered.connect(self.load_session_dialog)
        # Remove Load Texture, Set Slab Thickness, Reset Picking, Help/About, Redo, Export Mesh (duplicate), and other unused icons
        # ...existing code...

        # Sidebar (dock widget with tabs)
        self.sidebar = QDockWidget("Tools & Info", self)
        self.sidebar.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)
        self.tab_widget = QTabWidget()

        # === TAB 1: MESH INFO ===
        # Primary tab with mesh information and statistics
        info_tab = QWidget()
        info_layout = QVBoxLayout(info_tab)
        info_layout.addWidget(QLabel("Mesh Info:"))
        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        info_layout.addWidget(self.info_text)
        self.tab_widget.addTab(info_tab, "Mesh Info")

        # === TAB 2: ANNOTATIONS ===
        # Advanced annotation system with comprehensive tools
        try:
            from src.gui.annotation_system import AnnotationWidget
            self.annotation_widget = AnnotationWidget(self.mesh_viewer, self)
            self.annotation_widget.annotation_added.connect(self._on_annotation_added)
            self.tab_widget.addTab(self.annotation_widget, "Annotations")
        except ImportError as e:
            # Fallback to basic annotation tab if import fails
            annotation_tab = QWidget()
            ann_layout = QVBoxLayout(annotation_tab)
            ann_layout.addWidget(QLabel("Annotations (Basic Mode):"))
            ann_layout.addWidget(QLabel(f"Advanced features unavailable: {e}"))
            ann_layout.addWidget(QListWidget())
            btns_layout = QHBoxLayout()
            btns_layout.addWidget(QPushButton("Add Point"))
            btns_layout.addWidget(QPushButton("Add Line"))
            btns_layout.addWidget(QPushButton("Add Text"))
            ann_layout.addLayout(btns_layout)
            ann_layout.addWidget(QPushButton("Export Annotations"))
            self.tab_widget.addTab(annotation_tab, "Annotations")

        # === TAB 3: HISTORY ===
        # Session history and operation tracking
        history_tab = QWidget()
        hist_layout = QVBoxLayout(history_tab)
        hist_layout.addWidget(QLabel("Session History:"))
        hist_layout.addWidget(QListWidget())
        self.tab_widget.addTab(history_tab, "History")

        self.sidebar.setWidget(self.tab_widget)
        self.addDockWidget(Qt.RightDockWidgetArea, self.sidebar)

        # --- Left sidebar for view controls ---
        self.view_toolbar = QToolBar("View Controls")
        self.view_toolbar.setOrientation(Qt.Vertical)
        self.addToolBar(Qt.LeftToolBarArea, self.view_toolbar)

        # Projection toggle
        self.action_proj_toggle = self.view_toolbar.addAction(icon("view_perspective_ortho.svg"), "Perspective/Ortho")
        self.action_proj_toggle.triggered.connect(self.toggle_projection_mode)
        self.action_proj_toggle.setToolTip("Toggle between perspective and orthographic projection")

        self.view_toolbar.addSeparator()

        # Standard views
        self.action_view_top = self.view_toolbar.addAction(icon("view_top.svg"), "Top View")
        self.action_view_top.triggered.connect(lambda: self.set_standard_view('top'))
        self.action_view_top.setToolTip("View from above (top view)")

        self.action_view_bottom = self.view_toolbar.addAction(icon("view_bottom.svg"), "Bottom View")
        self.action_view_bottom.triggered.connect(lambda: self.set_standard_view('bottom'))
        self.action_view_bottom.setToolTip("View from below (bottom view)")

        self.action_view_front = self.view_toolbar.addAction(icon("view_front.svg"), "Front View")
        self.action_view_front.triggered.connect(lambda: self.set_standard_view('front'))
        self.action_view_front.setToolTip("View from front")

        self.action_view_back = self.view_toolbar.addAction(icon("view_back.svg"), "Back View")
        self.action_view_back.triggered.connect(lambda: self.set_standard_view('back'))
        self.action_view_back.setToolTip("View from back")

        self.action_view_left = self.view_toolbar.addAction(icon("view_left.svg"), "Left View")
        self.action_view_left.triggered.connect(lambda: self.set_standard_view('left'))
        self.action_view_left.setToolTip("View from left side")

        self.action_view_right = self.view_toolbar.addAction(icon("view_right.svg"), "Right View")
        self.action_view_right.triggered.connect(lambda: self.set_standard_view('right'))
        self.action_view_right.setToolTip("View from right side")

        self.view_toolbar.addSeparator()

        # Zoom controls
        self.action_zoom_all = self.view_toolbar.addAction(icon("zoom_all.svg"), "Zoom All")
        self.action_zoom_all.triggered.connect(self.zoom_all)
        self.action_zoom_all.setToolTip("Zoom to fit all objects in view")

        self.action_zoom_window = self.view_toolbar.addAction(icon("zoom_window.svg"), "Zoom Window")
        self.action_zoom_window.triggered.connect(self.zoom_window)
        self.action_zoom_window.setToolTip("Zoom to a selected rectangular region")

        # Note: Menu bar is now initialized in _init_menu_bar() - Configuration menu moved there too

    def show_set_unit_dialog(self):
        units = ["meters", "centimeters", "millimeters"]
        current = units.index(self.mesh_unit) if self.mesh_unit in units else 0
        unit, ok = QInputDialog.getItem(self, "Select Unit", "Choose the mesh unit:", units, current, False)
        if ok and unit:
            self.set_mesh_unit(unit)

    def toggle_sidebar(self, checked=None):
        if checked is None:
            checked = self.action_show_sidebar.isChecked()
        self.sidebar.setVisible(checked)
        self.action_show_sidebar.setChecked(self.sidebar.isVisible())

    def toggle_left_toolbar(self, checked=None):
        if checked is None:
            checked = self.action_show_left_toolbar.isChecked()
        self.view_toolbar.setVisible(checked)
        self.action_show_left_toolbar.setChecked(self.view_toolbar.isVisible())

    def pick_background_color(self):
        color = QColorDialog.getColor(QColor('#888888'), self, "Select 3D Background Color")
        if color.isValid():
            hex_color = color.name()
            self.mesh_viewer.plotter.set_background(hex_color)
            self.mesh_viewer.plotter.render()

    def show_loading_status(self, message="Loading mesh ..."):
        if self._loading_label is None:
            self._loading_label = QLabel()
            self._loading_label.setStyleSheet("color: #0077cc; font-weight: bold; background: #eaf6ff; padding: 2px 8px; border-radius: 6px;")
            # Add spinner
            self._loading_spinner = QLabel()
            spinner_movie = QMovie(":/icons/spinner.gif")  # You need to provide a spinner.gif in your resources
            self._loading_spinner.setMovie(spinner_movie)
            spinner_movie.start()
            self.status.addPermanentWidget(self._loading_spinner)
            self.status.addPermanentWidget(self._loading_label)
        self._loading_label.setText(message)
        self._loading_label.show()
        if self._loading_spinner:
            self._loading_spinner.show()

    def hide_loading_status(self):
        if self._loading_label:
            self._loading_label.hide()
        if self._loading_spinner:
            self._loading_spinner.hide()

    def show_progress_dialog(self, title, message, maximum=0, cancellable=True):
        """Show a progress dialog with optional cancellation"""
        from PySide6.QtWidgets import QProgressDialog

        progress = QProgressDialog(message, "Cancel" if cancellable else None, 0, maximum, self)
        progress.setWindowTitle(title)
        progress.setWindowModality(Qt.WindowModal)
        progress.setMinimumDuration(500)  # Show after 500ms
        progress.setValue(0)

        if not cancellable:
            progress.setCancelButton(None)

        return progress

    def update_progress(self, progress_dialog, value, message=None):
        """Update progress dialog with new value and optional message"""
        if progress_dialog:
            progress_dialog.setValue(value)
            if message:
                progress_dialog.setLabelText(message)
            QApplication.processEvents()
            return not progress_dialog.wasCanceled()
        return True

    def load_mesh_dialog(self):
        from PySide6.QtWidgets import QDialog, QLabel, QVBoxLayout
        from PySide6.QtCore import Qt
        filters = (
            "3D Mesh Files (*.stl *.obj *.ply *.vtk *.vtp *.off *.gltf *.glb *.3ds *.dae *.fbx *.x *.wrl *.mesh *.msh *.ctm *.gts *.usd *.usda *.usdc *.usdz);;"
            "All Files (*)"
        )
        last_dir = os.path.expanduser("~")
        file_path, _ = QFileDialog.getOpenFileName(self, "Open 3D Mesh", last_dir, filters)
        if file_path:
            # --- OBJ pre-check for malformed lines ---
            malformed_lines = []
            if file_path.lower().endswith('.obj'):
                with open(file_path, 'r', errors='ignore') as f:
                    for i, line in enumerate(f, 1):
                        if len(line.split()) > 10 or ('\0' in line):
                            malformed_lines.append(i)
                        if i > 100000:
                            break
                if malformed_lines:
                    msg = f"Warning: OBJ file contains unexpected data at end of line (e.g. line {malformed_lines[0]})\nThis may cause loading issues or memory problems.\nFirst 5 problematic lines: {malformed_lines[:5]}"
                    QMessageBox.warning(self, "OBJ File Warning", msg)
            # --- Status bar loading label ---
            self.show_loading_status()
            QApplication.processEvents()
            try:
                self.mesh_viewer.load_mesh(file_path)
                # After loading mesh, ask user for unit
                units = ["meters", "centimeters", "millimeters"]
                unit, ok = QInputDialog.getItem(self, "Select Mesh Unit", "What is the unit of the mesh coordinates?", units, 0, False)
                if ok:
                    self.mesh_unit = unit
                else:
                    self.mesh_unit = "meters"  # fallback
                self.update_mesh_info()
            except Exception as e:
                QMessageBox.critical(self, "Load Error", str(e))
            self.hide_loading_status()

    def update_mesh_info(self):
        mesh = self.mesh_viewer.mesh
        if mesh is None:
            self.info_text.setPlainText("No mesh loaded.")
            return
        info = []
        info.append(f"Type: {type(mesh).__name__}")
        info.append(f"Points: {mesh.n_points}")
        info.append(f"Cells: {mesh.n_cells}")
        if hasattr(mesh, 'faces') and mesh.faces is not None:
            info.append(f"Faces: {len(mesh.faces) // 4 if mesh.faces is not None else 'N/A'}")
        if hasattr(mesh, 'n_faces'):
            info.append(f"n_faces: {mesh.n_faces}")
        if hasattr(mesh, 'n_edges'):
            info.append(f"n_edges: {mesh.n_edges}")
        if hasattr(mesh, 'area'):
            info.append(f"Area: {getattr(mesh, 'area', 'N/A'):.4f}")
        if hasattr(mesh, 'volume'):
            info.append(f"Volume: {getattr(mesh, 'volume', 'N/A'):.4f}")
        info.append(f"Bounds: {mesh.bounds}")
        info.append(f"Arrays: {list(mesh.point_data.keys())}")
        # Add mesh unit to info
        info.append(f"Mesh Unit: {self.mesh_unit}")
        self.info_text.setPlainText("\n".join(info))

    def toggle_projection_mode(self):
        plotter = self.mesh_viewer.plotter
        if plotter.camera.GetParallelProjection():
            plotter.camera.ParallelProjectionOff()
        else:
            plotter.camera.ParallelProjectionOn()
        plotter.render()
        self.update_status_bar()

    def set_standard_view(self, view):
        plotter = self.mesh_viewer.plotter
        views = {
            'top': (0, 0, 1),
            'bottom': (0, 0, -1),
            'left': (-1, 0, 0),
            'right': (1, 0, 0),
            'front': (0, 1, 0),
            'back': (0, -1, 0),
        }
        if view in views:
            plotter.view_vector(views[view])
            plotter.reset_camera()
            plotter.render()

    def zoom_all(self):
        self.mesh_viewer.plotter.reset_camera()
        self.mesh_viewer.plotter.render()

    def zoom_window(self):
        # Use the correct PyVistaQt method for rubber band zoom
        self.mesh_viewer.plotter.enable_rubber_band_2d_style()
        self.mesh_viewer.plotter.render()

    def show_mesh_display_mode_dialog(self):
        current_mode = getattr(self.mesh_viewer, 'display_mode', 'surface')
        dialog = MeshDisplayModeDialog(current_mode, parent=self, mesh_viewer=self.mesh_viewer)
        from PySide6.QtWidgets import QDialog
        if dialog.exec() == QDialog.Accepted:
            mode = dialog.selected_mode()
            wire_color = dialog.get_wire_color()
            edge_color = dialog.get_edge_color()
            point_color = dialog.get_point_color()
            edge_width = dialog.get_edge_width()
            point_size = dialog.get_point_size()
            shading = dialog.get_shading()
            self.mesh_viewer.set_display_mode(
                mode,
                wire_color=wire_color,
                point_color=point_color,
                point_size=point_size,
                edge_color=edge_color,
                edge_width=edge_width,
                shading=shading
            )

    def show_mesh_processing_dialog(self):
        if self.mesh_viewer.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        dialog = MeshProcessingDialog(self)
        if dialog.exec():
            params = dialog.get_selected_filters()
            self.process_mesh(params)

    def process_mesh(self, params):
        from PySide6.QtWidgets import QProgressDialog, QMessageBox, QApplication
        mesh = self.mesh_viewer.mesh
        # Always convert mesh to PolyData for processing
        try:
            mesh = mesh_utils.ensure_polydata(mesh)
        except Exception as e:
            QMessageBox.warning(self, "Mesh Processing Error", f"Mesh processing requires a PolyData mesh. Conversion failed: {e}")
            return
        # Now create the progress dialog
        steps = [
            ('clean', params['clean']),
            ('components', params['components']),
            ('holes', params['holes']),
            ('smooth', params['smooth']),
            ('decimate', params['decimate'])
            # remesh removed
        ]
        total = sum(1 for _, enabled in steps if enabled)
        progress = QProgressDialog("Processing mesh...", None, 0, total, self)
        progress.setWindowTitle("Mesh Processing")
        progress.setWindowModality(Qt.WindowModal)
        progress.setValue(0)
        progress.show()
        QApplication.processEvents()
        step = 0
        # Clean
        if params['clean']:
            progress.setLabelText("Cleaning mesh...")
            QApplication.processEvents()
            try:
                mesh = mesh_utils.clean_mesh(mesh)
            except Exception as e:
                QMessageBox.warning(self, "Clean Mesh Error", f"Mesh cleaning failed: {e}")
            step += 1
            progress.setValue(step)
        # Remove small components
        if params['components']:
            progress.setLabelText("Removing small components...")
            QApplication.processEvents()
            try:
                mesh = mesh_utils.remove_small_components(mesh)
            except Exception as e:
                QMessageBox.warning(self, "Remove Components Error", f"Removing small components failed: {e}")
            step += 1
            progress.setValue(step)
        # Fill small holes
        if params['holes']:
            progress.setLabelText("Filling small holes...")
            QApplication.processEvents()
            try:
                mesh = mesh_utils.fill_small_holes(mesh, max_area=params['hole_size'])
            except Exception as e:
                QMessageBox.warning(self, "Fill Holes Error", f"Filling small holes failed: {e}")
            step += 1
            progress.setValue(step)
        # Smooth
        if params['smooth']:
            progress.setLabelText("Smoothing mesh...")
            QApplication.processEvents()
            try:
                new_mesh = mesh_utils.smooth_mesh(mesh, n_iter=params['smooth_iter'], relaxation=params['smooth_relax'])
                if new_mesh is None:
                    raise RuntimeError("Smoothing did not return a valid mesh.")
                mesh = new_mesh
            except Exception as e:
                QMessageBox.warning(self, "Smooth Error", f"Mesh smoothing failed: {e}")
            step += 1
            progress.setValue(step)
        # Decimate
        if params['decimate']:
            progress.setLabelText("Decimating mesh...")
            QApplication.processEvents()
            reduction = params['decimate_target'] / 100.0
            try:
                new_mesh = None
                try:
                    new_mesh = mesh.decimate(target_reduction=1.0 - reduction)
                except TypeError:
                    new_mesh = mesh.decimate(1.0 - reduction)
                if new_mesh is None:
                    raise RuntimeError("Decimation did not return a valid mesh.")
                mesh = new_mesh
            except Exception as e:
                QMessageBox.warning(self, "Decimate Error", f"Mesh decimation failed: {e}")
            step += 1
            progress.setValue(step)
        # Remesh logic removed
        progress.setValue(total)
        progress.setLabelText("Done.")
        QApplication.processEvents()
        self.mesh_viewer.mesh = mesh
        self.mesh_viewer.set_display_mode(getattr(self.mesh_viewer, 'display_mode', 'surface'))
        self.update_mesh_info()

    def show_advanced_analysis_dialog(self):
        if self.mesh_viewer.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        dialog = MeshAdvancedAnalysisDialog(self)
        # Connect the Apply button for live/interactive analysis
        dialog.analysis_apply.connect(self.run_advanced_analysis)
        dialog.show()
        # Optionally, connect accepted to run analysis one last time or just close
        # dialog.accepted.connect(lambda: self.run_advanced_analysis(dialog.get_selected_analyses()))

    def run_advanced_analysis(self, params):
        mesh = self.mesh_viewer.mesh
        results = []
        # Mean Curvature
        if params.get('mean_curvature'):
            try:
                scalars = mesh.curvature(curv_type='mean')
                mesh.point_data['MeanCurvature'] = scalars
                # Debug: show min/max/mean
                minv, maxv, meanv = np.nanmin(scalars), np.nanmax(scalars), np.nanmean(scalars)
                QMessageBox.information(self, "Mean Curvature Stats", f"min: {minv:.4g}\nmax: {maxv:.4g}\nmean: {meanv:.4g}")
                self.mesh_viewer.set_colormap('MeanCurvature')
                results.append('Mean Curvature')
            except Exception as e:
                QMessageBox.critical(self, "Curvature Error", str(e))
        # Gaussian Curvature
        if params.get('gaussian_curvature'):
            try:
                scalars = mesh.curvature(curv_type='gaussian')
                mesh.point_data['GaussianCurvature'] = scalars
                self.mesh_viewer.set_colormap('GaussianCurvature')
                results.append('Gaussian Curvature')
            except Exception as e:
                QMessageBox.critical(self, "Curvature Error", f"Gaussian: {e}")
        # Thickness
        if params.get('thickness'):
            try:
                normals = mesh.point_normals
                points = mesh.points
                ray_length = params.get('thickness_ray_length', 100.0)
                thickness = np.zeros(points.shape[0])
                for i, (p, n) in enumerate(zip(points, normals)):
                    hits = mesh.ray_trace(p, p + n * ray_length, first_point=False)[0]
                    if len(hits) > 0:
                        thickness[i] = np.linalg.norm(hits[0] - p)
                    else:
                        thickness[i] = 0
                mesh.point_data['Thickness'] = thickness
                self.mesh_viewer.set_colormap('Thickness')
                results.append('Thickness')
            except Exception as e:
                QMessageBox.critical(self, "Thickness Error", str(e))
        # Geodesic Distance
        if params.get('geodesic'):
            try:
                source = params.get('geodesic_source', 0)
                dists = mesh.geodesic_distance(source)
                mesh.point_data['GeodesicDist'] = dists
                self.mesh_viewer.set_colormap('GeodesicDist')
                results.append('Geodesic Distance')
            except Exception as e:
                QMessageBox.critical(self, "Geodesic Error", str(e))
        # Custom Scalar Field
        if params.get('custom'):
            try:
                mesh.point_data['RandomField'] = np.random.rand(mesh.n_points)
                self.mesh_viewer.set_colormap('RandomField')
                results.append('Custom Scalar Field')
            except Exception as e:
                QMessageBox.critical(self, "Custom Field Error", str(e))
        # Contact/Proximity Analysis (minimum distance between surfaces)
        if params.get('contact'):
            try:
                # For now, compute min distance between all points (mesh to itself, as a stub)
                from scipy.spatial import cKDTree
                points = mesh.points
                tree = cKDTree(points)
                dists, idxs = tree.query(points, k=2)  # k=2: nearest neighbor (self is 0)
                min_dist = np.min(dists[:, 1])  # skip self (0)
                mesh.field_data['MinContactDistance'] = min_dist
                QMessageBox.information(self, "Contact/Proximity Analysis", f"Minimum distance between mesh points: {min_dist:.4g}")
                results.append('Contact/Proximity (min distance)')
            except Exception as e:
                QMessageBox.critical(self, "Contact/Proximity Error", str(e))
        # Export Results
        if params.get('export_results'):
            try:
                import csv
                fname, _ = QFileDialog.getSaveFileName(self, "Export Results", "mesh_analysis.csv", "CSV Files (*.csv);;All Files (*)")
                if fname:
                    with open(fname, 'w', newline='') as f:
                        writer = csv.writer(f)
                        # Write header: point_data arrays
                        arrays = list(mesh.point_data.keys())
                        writer.writerow(['PointIndex'] + arrays)
                        for i in range(mesh.n_points):
                            row = [i] + [mesh.point_data[a][i] if a in mesh.point_data else '' for a in arrays]
                            writer.writerow(row)
                    QMessageBox.information(self, "Export Results", f"Exported point data arrays to {fname}")
                    results.append('Exported Results')
            except Exception as e:
                QMessageBox.critical(self, "Export Error", str(e))
        if results:
            QMessageBox.information(self, "Analysis Complete", f"Computed: {', '.join(results)}. Showing last result.")
        self.update_mesh_info()

    def show_plane_definition_choice(self):
        if self.mesh_viewer.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        # --- Directly open the new tabbed dialog, no message box ---
        outside_opacity = getattr(self, '_outside_opacity', 0.18)
        dialog = PlaneByPointsDialog(self.mesh_viewer, self, outside_opacity=outside_opacity)
        def on_done():
            plane_params = getattr(dialog, 'plane_params', None)
            slab_thickness = getattr(dialog, 'slab_thickness', None)
            slab_side = getattr(dialog, '_slab_side', 'centered')
            if plane_params is not None and slab_thickness is not None:
                self._last_plane_params = plane_params
                self._last_slab_thickness = slab_thickness
                self._last_slab_side = slab_side
                # --- Add persistent analysis plane ---
                origin = np.array(plane_params['origin'])
                normal = np.array(plane_params['normal'])
                mesh = self.mesh_viewer.mesh
                bounds = np.array(mesh.bounds).reshape(3, 2)
                size = np.linalg.norm(bounds[:, 1] - bounds[:, 0]) * 0.6 if mesh is not None else 1.0
                plane = pv.Plane(center=origin, direction=normal, i_size=size, j_size=size)
                self.mesh_viewer.set_analysis_plane(plane, color='yellow', opacity=0.4)
                self.action_show_analysis_plane.setChecked(True)
            try:
                self.opacity_spin.valueChanged.disconnect(dialog.set_outside_opacity)
            except Exception:
                pass
            dialog.deleteLater()
        dialog.finished.connect(on_done)
        self.opacity_spin.valueChanged.connect(dialog.set_outside_opacity)
        dialog.show()
        # Show slab thickness controls as soon as plane is defined
        self.slab_control_frame.setVisible(True)

    def show_plane_canonical(self):
        dialog = PlaneCanonicalDialog(self.mesh_viewer, self)
        def on_done():
            plane_params = getattr(dialog, 'plane_params', None)
            slab_thickness = getattr(dialog, 'slab_thickness', None)
            if plane_params is not None and slab_thickness is not None:
                self._last_plane_params = plane_params
                self._last_slab_thickness = slab_thickness
                # --- Add persistent analysis plane ---
                origin = np.array(plane_params['origin'])
                normal = np.array(plane_params['normal'])
                mesh = self.mesh_viewer.mesh
                bounds = np.array(mesh.bounds).reshape(3, 2)
                size = np.linalg.norm(bounds[:, 1] - bounds[:, 0]) * 0.6 if mesh is not None else 1.0
                plane = pv.Plane(center=origin, direction=normal, i_size=size, j_size=size)
                self.mesh_viewer.set_analysis_plane(plane, color='yellow', opacity=0.4)
                self.action_show_analysis_plane.setChecked(True)
            dialog.deleteLater()
        dialog.finished.connect(on_done)
        dialog.show()

    def run_deformation_analysis(self, plane_params, slab_thickness, slab_side='centered'):
        # plane_params: dict with 'origin' and 'normal' (numpy arrays)
        # slab_thickness: float
        # slab_side: 'centered', 'positive', or 'negative'
        mesh = self.mesh_viewer.mesh
        if mesh is None:
            QMessageBox.warning(self, "No Mesh", "Please load a mesh first.")
            return

        # Show progress dialog
        progress = self.show_progress_dialog("Deformation Analysis", "Initializing analysis...", 5, False)

        try:
            # Step 1: Initialize analysis
            self.update_progress(progress, 1, "Calculating point distances...")
            import numpy as np
            points = mesh.points
            origin = np.array(plane_params['origin'])
            normal = np.array(plane_params['normal']) / np.linalg.norm(plane_params['normal'])

            # Step 2: Compute distances
            self.update_progress(progress, 2, "Computing deformation values...")
            distances = np.dot(points - origin, normal)

            # Step 3: Apply SLAB filtering
            self.update_progress(progress, 3, f"Applying {slab_side} SLAB filtering...")
            if slab_side == 'centered':
                mask = np.abs(distances) <= (slab_thickness / 2)
            elif slab_side == 'positive':
                mask = (distances >= 0) & (distances <= slab_thickness)
            elif slab_side == 'negative':
                mask = (distances <= 0) & (distances >= -slab_thickness)
            else:
                mask = np.abs(distances) <= (slab_thickness / 2)

            # Step 4: Prepare visualization data
            self.update_progress(progress, 4, "Preparing visualization...")
            mesh.point_data['Deformation'] = distances
            deformation_masked = distances.copy()
            deformation_masked[~mask] = np.nan
            mesh.point_data['DeformationMasked'] = deformation_masked

            # Step 5: Generate visualization
            self.update_progress(progress, 5, "Generating heatmap visualization...")

            # Set color for outside-slab region (nan_color)
            prev_mask = getattr(self, '_last_deformation_mask', None)
            self.mesh_viewer.set_colormap('DeformationMasked', nan_color="#e0e0e0", nan_opacity=getattr(self, '_outside_opacity', 0.15), slab_aware=True)

            # Always update colorbar here, not in _on_slab_thickness_changed
            try:
                self.mesh_viewer.plotter.remove_scalar_bar()
            except Exception:
                pass
            self.mesh_viewer.plotter.render()  # Ensure mesh is rendered before adding colorbar
            self.mesh_viewer.plotter.add_scalar_bar(title="Deformation (m)", vertical=True, fmt="%.3f", interactive=False, position_x=0.88)
            self._last_deformation_mask = mask.copy()
            self.mesh_viewer.plotter.render()

            # Complete progress
            progress.setValue(5)
            progress.close()

            self.show_enhanced_status_message("Deformation heatmap computed and displayed", 4000, "success")
            self.update_mesh_info()
            self._update_enhanced_status()

        except Exception as e:
            progress.close()
            QMessageBox.critical(self, "Analysis Error", f"Failed to complete deformation analysis: {e}")
            raise

    def _init_slab_thickness_control(self):
        # Add slab thickness slider/spinbox below the 3D viewer, initially hidden
        self.slab_control_frame = QFrame(self)
        self.slab_control_frame.setFrameShape(QFrame.StyledPanel)
        self.slab_control_frame.setVisible(False)
        layout = QHBoxLayout(self.slab_control_frame)
        layout.setContentsMargins(8, 2, 8, 2)
        layout.addWidget(QLabel("Slab Thickness:"))
        self.slab_slider = QSlider(Qt.Horizontal)
        self.slab_slider.setMinimum(1)
        self.slab_slider.setMaximum(200)
        self.slab_slider.setValue(20)
        self.slab_slider.setSingleStep(1)
        self.slab_slider.setTickInterval(10)
        self.slab_slider.setTickPosition(QSlider.TicksBelow)
        layout.addWidget(self.slab_slider)
        self.slab_spin = QDoubleSpinBox()
        self.slab_spin.setRange(0.01, 2.0)  # Match slider range: 1-200 maps to 0.01-2.0
        self.slab_spin.setDecimals(3)
        self.slab_spin.setSingleStep(0.01)
        self.slab_spin.setValue(0.20)
        self.slab_spin.setSuffix(" m")
        layout.addWidget(self.slab_spin)
        # --- Add outside opacity control ---
        layout.addWidget(QLabel("Outside Opacity:"))
        self.opacity_slider = QSlider(Qt.Horizontal)
        self.opacity_slider.setMinimum(0)
        self.opacity_slider.setMaximum(100)
        self.opacity_slider.setValue(15)
        self.opacity_slider.setSingleStep(1)
        self.opacity_spin = QDoubleSpinBox()
        self.opacity_spin.setRange(0.0, 1.0)
        self.opacity_spin.setDecimals(2)
        self.opacity_spin.setSingleStep(0.01)
        self.opacity_spin.setValue(0.15)
        layout.addWidget(self.opacity_slider)
        layout.addWidget(self.opacity_spin)
        # Sync slider and spinbox with proper scaling
        # Opacity: slider 0-100 maps to spinbox 0.0-1.0
        self.opacity_slider.valueChanged.connect(lambda v: self.opacity_spin.setValue(v/100.0))
        self.opacity_spin.valueChanged.connect(lambda v: self.opacity_slider.setValue(int(v*100)))
        self.opacity_spin.valueChanged.connect(self._on_opacity_changed)

        # Slab thickness: slider 1-200 maps to spinbox 0.01-2.0 (in meters)
        self.slab_slider.valueChanged.connect(lambda v: self.slab_spin.setValue(v/100.0))
        self.slab_spin.valueChanged.connect(lambda v: self.slab_slider.setValue(int(v*100)))
        self.slab_spin.valueChanged.connect(self._on_slab_thickness_changed)

        # Debug: Add some logging to verify connections work
        self.slab_slider.valueChanged.connect(lambda v: print(f"[DEBUG] Slab slider changed to {v}, spinbox will be {v/100.0}"))
        self.opacity_slider.valueChanged.connect(lambda v: print(f"[DEBUG] Opacity slider changed to {v}, spinbox will be {v/100.0}"))
        # Add below central widget
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0,0,0,0)
        main_layout.setSpacing(0)
        main_layout.addWidget(self.mesh_viewer)
        main_layout.addWidget(self.slab_control_frame)
        container = QWidget()
        container.setLayout(main_layout)
        self.setCentralWidget(container)

    def _on_opacity_changed(self, value):
        self._outside_opacity = float(value)
        # Live update the heatmap if active
        if hasattr(self, '_last_plane_params') and self.mesh_viewer.mesh is not None:
            self._on_slab_thickness_changed(self.slab_spin.value())

    def _on_slab_thickness_changed(self, value):
        # Live update the heatmap when slab thickness changes
        if not hasattr(self, '_last_plane_params') or self.mesh_viewer.mesh is None:
            return
        import numpy as np
        mesh = self.mesh_viewer.mesh
        plane_params = self._last_plane_params
        slab_thickness = float(value)
        slab_side = getattr(self, '_last_slab_side', 'centered')
        points = mesh.points
        origin = np.array(plane_params['origin'])
        normal = np.array(plane_params['normal']) / np.linalg.norm(plane_params['normal'])
        distances = np.dot(points - origin, normal)
        if slab_side == 'centered':
            mask = np.abs(distances) <= (slab_thickness / 2)
        elif slab_side == 'positive':
            mask = (distances >= 0) & (distances <= slab_thickness)
        elif slab_side == 'negative':
            mask = (distances <= 0) & (distances >= -slab_thickness)
        else:
            mask = np.abs(distances) <= (slab_thickness / 2)
        mesh.point_data['Deformation'] = distances
        deformation_masked = distances.copy()
        deformation_masked[~mask] = np.nan
        mesh.point_data['DeformationMasked'] = deformation_masked
        self.mesh_viewer.set_colormap('DeformationMasked', nan_color="#e0e0e0", nan_opacity=getattr(self, '_outside_opacity', 0.15), slab_aware=True)
        self._last_slab_thickness = slab_thickness
        # Always update colorbar here to ensure it stays on the right
        try:
            self.mesh_viewer.plotter.remove_scalar_bar()
        except Exception:
            pass
        self.mesh_viewer.plotter.render()  # Ensure mesh is rendered before adding colorbar
        self.mesh_viewer.plotter.add_scalar_bar(title="Deformation (m)", vertical=True, fmt="%.3f", interactive=False, position_x=0.88)
        self.mesh_viewer.plotter.render()

    def show_heatmap_clicked(self):
        # Called when the user clicks the Show Deformation Heatmap button
        if not hasattr(self, '_last_plane_params') or not hasattr(self, '_last_slab_thickness'):
            QMessageBox.warning(self, "No Plane Defined", "Please define a plane and slab thickness first.")
            return
        if self._last_plane_params is None or self._last_slab_thickness is None:
            QMessageBox.warning(self, "No Plane Defined", "Please define a plane and slab thickness first.")
            return
        slab_side = getattr(self, '_last_slab_side', 'centered')
        self.run_deformation_analysis(self._last_plane_params, self._last_slab_thickness, slab_side)

    def show_vector_field_clicked(self):
        """
        Show the vector field (direction from plane) for each mesh point in the 3D view, using slab logic.
        """
        # Get plane parameters and slab settings
        plane_params = getattr(self, '_last_plane_params', None)
        slab_thickness = getattr(self, '_last_slab_thickness', None)
        slab_side = getattr(self, '_last_slab_side', 'centered')

        if plane_params is None or slab_thickness is None:
            QMessageBox.warning(self, "No Plane Defined",
                              "Please define an analysis plane first before showing vector field.")
            return

        self.mesh_viewer.show_vector_field("Displacement", scale=1.0, color="red",
                                         plane_params=plane_params,
                                         slab_thickness=slab_thickness,
                                         slab_side=slab_side)

    def show_crop_box_dialog(self):
        if self.mesh_viewer.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        from src.gui.crop_box_dialog import CropBoxDialog
        dialog = CropBoxDialog(self.mesh_viewer, parent=self)
        dialog.show()  # Use show() for non-modal, non-blocking dialog

    def save_cropped_mesh(self):
        from src.gui.save_cropped_mesh import save_cropped_mesh
        mesh = getattr(self.mesh_viewer, 'mesh', None)
        save_cropped_mesh(mesh, parent=self)

    def save_screenshot_dialog(self):
        from gui.screenshot_dialog import ScreenshotDialog
        from PySide6.QtWidgets import QDialog
        dialog = ScreenshotDialog(self)
        if dialog.exec() == QDialog.Accepted:
            mode = dialog.get_mode()
            transparent = dialog.is_transparent()
            file_path, _ = QFileDialog.getSaveFileName(self, "Save Screenshot", "screenshot.png", "PNG Image (*.png);;JPEG Image (*.jpg *.jpeg)")
            if file_path:
                try:
                    scale = 1 if mode == 'normal' else 3
                    img = self.mesh_viewer.plotter.screenshot(return_img=True, scale=scale, transparent_background=transparent)
                    import imageio
                    imageio.imwrite(file_path, img)
                    QMessageBox.information(self, "Screenshot Saved", f"Screenshot saved to: {file_path}")
                except Exception as e:
                    QMessageBox.critical(self, "Error", f"Failed to save screenshot:\n{str(e)}")

    def show_section_with_plane_dialog(self):
        if self.mesh_viewer.mesh is None:
            QMessageBox.warning(self, "No Mesh", "Load a mesh first.")
            return
        # Reuse plane definition dialog
        dialog = PlaneDefinitionChoiceDialog(self)
        result = dialog.exec()
        if result == 1:
            plane_dialog = PlaneByPointsDialog(self.mesh_viewer, self)
            if plane_dialog.exec() == QDialog.Accepted:
                plane_params = getattr(plane_dialog, 'plane_params', None)
                if plane_params is not None:
                    origin = np.array(plane_params['origin'])
                    normal = np.array(plane_params['normal'])
                    self.extract_and_export_section(origin, normal)
        elif result == 2:
            plane_dialog = PlaneCanonicalDialog(self.mesh_viewer, self)
            if plane_dialog.exec() == QDialog.Accepted:
                plane_params = getattr(plane_dialog, 'plane_params', None)
                if plane_params is not None:
                    origin = np.array(plane_params['origin'])
                    normal = np.array(plane_params['normal'])
                    self.extract_and_export_section(origin, normal)

    def extract_and_export_section(self, origin, normal):
        mesh = self.mesh_viewer.mesh
        section = extract_section_curves(mesh, origin, normal)
        if section.n_points == 0:
            QMessageBox.warning(self, "Section", "No intersection found.")
            return
        # Preview section in viewer
        self.mesh_viewer.plotter.clear()
        self.mesh_viewer.plotter.add_mesh(section, color="red", line_width=3, render_lines_as_tubes=True)
        self.mesh_viewer.plotter.reset_camera()
        self.mesh_viewer.plotter.render()
        # Ask to export
        ret = QMessageBox.question(self, "Export Section", "Export section to DXF?", QMessageBox.Yes | QMessageBox.No)
        if ret == QMessageBox.Yes:
            fname, _ = QFileDialog.getSaveFileName(self, "Export Section DXF", "section.dxf", "DXF Files (*.dxf);;All Files (*)")
            if fname:
                try:
                    export_section_to_dxf(section, fname)
                    QMessageBox.information(self, "Export", f"Section exported to {fname}")
                except Exception as e:
                    QMessageBox.critical(self, "Export Error", str(e))

    def toggle_analysis_plane(self, checked=None):
        if checked is None:
            checked = self.action_show_analysis_plane.isChecked()
        self.mesh_viewer.show_analysis_plane(checked)
        self.action_show_analysis_plane.setChecked(self.mesh_viewer._analysis_plane_visible)

    def update_mask_and_colorbar(self):
        # Centralized update for mask and colorbar, reflecting only values present in the selected slab region
        mask = self.get_current_mask()
        if mask is None or not mask.any():
            self.mesh_viewer.clear_colorbar()
            self.mesh_viewer.show_message("No points in the selected slab region.")
            return
        self.mesh_viewer.update_mask(mask)
        self.mesh_viewer.update_colorbar(mask)

    def get_current_mask(self):
        # Determine the current mask based on the last used plane parameters and slab thickness
        if not hasattr(self, '_last_plane_params') or self.mesh_viewer.mesh is None:
            return None
        import numpy as np
        mesh = self.mesh_viewer.mesh
        plane_params = self._last_plane_params
        slab_thickness = getattr(self, '_last_slab_thickness', 1.0)
        slab_side = getattr(self, '_last_slab_side', 'centered')
        points = mesh.points
        origin = np.array(plane_params['origin'])
        normal = np.array(plane_params['normal']) / np.linalg.norm(plane_params['normal'])
        distances = np.dot(points - origin, normal)
        if slab_side == 'centered':
            mask = np.abs(distances) <= (slab_thickness / 2)
        elif slab_side == 'positive':
            mask = (distances >= 0) & (distances <= slab_thickness)
        elif slab_side == 'negative':
            mask = (distances <= 0) & (distances >= -slab_thickness)
        else:
            mask = np.abs(distances) <= (slab_thickness / 2)
        return mask

    def update_status_bar(self):
        # Projection mode
        proj = self.get_projection_mode()
        self._projection_label.setText(f"View: {proj}")
        # Unit
        self._unit_label.setText(f"Unit: {self.mesh_unit}")

    def get_projection_mode(self):
        # Try to get projection mode from mesh_viewer.plotter
        try:
            camera = self.mesh_viewer.plotter.camera
            if hasattr(camera, 'GetParallelProjection'):
                if camera.GetParallelProjection():
                    return "Orthogonal"
                else:
                    return "Perspective"
        except Exception:
            pass
        return "Unknown"

    def set_mesh_unit(self, unit):
        self.mesh_unit = unit
        self.update_status_bar()

    def set_projection_mode(self, mode):
        # mode: 'perspective' or 'orthogonal'
        cam = self.mesh_viewer.plotter.camera
        if mode == 'orthogonal':
            cam.ParallelProjectionOn()
        else:
            cam.ParallelProjectionOff()
        self.mesh_viewer.plotter.render()
        self.update_status_bar()

    def export_deformation_csv(self):
        """Export deformation data to CSV file"""
        try:
            if not hasattr(self.mesh_viewer, 'mesh') or self.mesh_viewer.mesh is None:
                QMessageBox.warning(self, "No Data", "No mesh data available to export.")
                return

            mesh = self.mesh_viewer.mesh
            if 'DeformationMasked' not in mesh.point_data and 'deformation' not in mesh.point_data:
                QMessageBox.warning(self, "No Deformation Data", "No deformation data available to export.")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export Deformation CSV", "deformation_data.csv",
                "CSV Files (*.csv);;All Files (*)"
            )

            if file_path:
                import csv
                deformation_data = mesh.point_data.get('DeformationMasked', mesh.point_data.get('deformation'))
                points = mesh.points

                with open(file_path, 'w', newline='') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(['X', 'Y', 'Z', 'Deformation'])

                    for i, (point, deform) in enumerate(zip(points, deformation_data)):
                        writer.writerow([point[0], point[1], point[2], deform])

                QMessageBox.information(self, "Export Complete", f"Deformation data exported to {file_path}")

        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Failed to export CSV: {e}")

    def save_session_dialog(self):
        """Save current analysis session"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Save Session", "analysis_session.json",
                "Session Files (*.json);;All Files (*)"
            )

            if file_path:
                self._save_session_to_file(file_path)

        except Exception as e:
            QMessageBox.critical(self, "Save Error", f"Failed to save session: {e}")

    def load_session_dialog(self):
        """Load previous analysis session"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Load Session", "",
                "Session Files (*.json);;All Files (*)"
            )

            if file_path:
                self._load_session_from_file(file_path)

        except Exception as e:
            QMessageBox.critical(self, "Load Error", f"Failed to load session: {e}")

    def undo_last_operation(self):
        """Undo the last operation"""
        try:
            # TODO: Implement undo functionality
            QMessageBox.information(self, "Undo", "Undo feature coming soon!")

        except Exception as e:
            QMessageBox.critical(self, "Undo Error", f"Failed to undo operation: {e}")

    def save_project_dialog(self):
        """Save current project"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Save Project", "project.json",
                "Project Files (*.json);;All Files (*)"
            )

            if file_path:
                # TODO: Implement project saving
                QMessageBox.information(self, "Save Project", "Project saving feature coming soon!")

        except Exception as e:
            QMessageBox.critical(self, "Save Error", f"Failed to save project: {e}")

    def show_help_dialog(self):
        """Show help and documentation"""
        try:
            help_text = """
DeformViz 3D - Help & Documentation

WORKFLOW:
1. Load 3D Mesh (Ctrl+O) - Load your mesh file
2. Process Mesh - Clean and prepare the mesh
3. Define Plane - Set reference plane for analysis
4. Crop/Slice - Edit the mesh as needed
5. Show Heatmap - Visualize deformation data
6. Export Results - Save your analysis

KEYBOARD SHORTCUTS:
• Ctrl+O - Load mesh
• Ctrl+S - Save project
• Ctrl+Z - Undo
• Ctrl+E - Export CSV
• F1 - Show this help
• F12 - Take screenshot

VIEW CONTROLS:
• Use left toolbar for standard views
• Mouse: Rotate, zoom, pan
• Toggle perspective/orthographic projection

For more help, visit the documentation or contact support.
"""
            QMessageBox.information(self, "DeformViz 3D - Help", help_text)

        except Exception as e:
            QMessageBox.critical(self, "Help Error", f"Failed to show help: {e}")

    def quick_screenshot(self):
        """Take a quick screenshot at current resolution"""
        try:
            import os
            from datetime import datetime

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"
            file_path = os.path.join(os.path.expanduser("~"), filename)

            # Take screenshot at current resolution
            img = self.mesh_viewer.plotter.screenshot(return_img=True, scale=1)

            import imageio
            imageio.imwrite(file_path, img)

            QMessageBox.information(self, "Screenshot Saved",
                                  f"Quick screenshot saved to:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "Screenshot Error", f"Failed to take screenshot: {e}")

    def show_colormap_dialog(self):
        """Show colormap selection dialog"""
        try:
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox, QSlider, QCheckBox

            # Check if we have mesh data to apply colormap to
            if self.mesh_viewer.mesh is None:
                QMessageBox.warning(self, "No Mesh", "Load a mesh first before selecting colormap.")
                return

            if 'DeformationMasked' not in self.mesh_viewer.mesh.point_data:
                QMessageBox.warning(self, "No Analysis Data", "Perform deformation analysis first before changing colormap.")
                return

            dialog = QDialog(self)
            dialog.setWindowTitle("Select Colormap")
            dialog.setMinimumWidth(400)
            layout = QVBoxLayout(dialog)

            # Colormap selection
            layout.addWidget(QLabel("Choose Colormap:"))
            colormap_combo = QComboBox()
            colormaps = [
                'viridis', 'plasma', 'inferno', 'magma', 'cividis',
                'coolwarm', 'bwr', 'seismic', 'RdBu', 'RdYlBu',
                'jet', 'rainbow', 'turbo', 'hot', 'cool'
            ]
            colormap_combo.addItems(colormaps)
            colormap_combo.setCurrentText('coolwarm')  # Default
            layout.addWidget(colormap_combo)

            # Reverse colormap option
            reverse_checkbox = QCheckBox("Reverse Colormap")
            layout.addWidget(reverse_checkbox)

            # Preview and buttons
            layout.addWidget(QLabel("Click 'Apply' to see changes:"))

            button_layout = QHBoxLayout()
            apply_btn = QPushButton("Apply")
            cancel_btn = QPushButton("Cancel")
            button_layout.addWidget(apply_btn)
            button_layout.addWidget(cancel_btn)
            layout.addLayout(button_layout)

            def apply_colormap():
                try:
                    colormap_name = colormap_combo.currentText()
                    if reverse_checkbox.isChecked():
                        colormap_name += '_r'  # Reverse colormap

                    # Apply the new colormap
                    mesh = self.mesh_viewer.mesh
                    if mesh and 'DeformationMasked' in mesh.point_data:
                        # Update the colormap
                        self.mesh_viewer.set_colormap('DeformationMasked',
                                                    nan_color="#e0e0e0",
                                                    nan_opacity=getattr(self, '_outside_opacity', 0.15),
                                                    slab_aware=True)

                        # Update the scalar bar with new colormap
                        try:
                            self.mesh_viewer.plotter.update_scalar_bar_range(
                                mesh.point_data['DeformationMasked'].min(),
                                mesh.point_data['DeformationMasked'].max()
                            )
                            # Set the colormap on the mesh
                            actors = self.mesh_viewer.plotter.renderer.actors
                            for actor in actors.values():
                                if hasattr(actor, 'mapper') and actor.mapper:
                                    actor.mapper.lookup_table.SetColorScheme(colormap_name)
                        except Exception as e:
                            print(f"Warning: Could not update colormap: {e}")

                        self.mesh_viewer.plotter.render()
                        QMessageBox.information(dialog, "Colormap Applied", f"Applied colormap: {colormap_name}")

                except Exception as e:
                    QMessageBox.critical(dialog, "Error", f"Failed to apply colormap: {e}")

            apply_btn.clicked.connect(apply_colormap)
            cancel_btn.clicked.connect(dialog.reject)

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "Colormap Error", f"Failed to show colormap dialog: {e}")

    def show_analysis_wizard(self):
        """Show the analysis wizard for guided workflow"""
        try:
            wizard = AnalysisWizard(self)

            # Connect wizard signals to main window methods
            wizard.mesh_load_requested.connect(self._wizard_load_mesh)
            wizard.plane_definition_requested.connect(self._wizard_define_plane)
            wizard.analysis_requested.connect(self._wizard_run_analysis)

            # Store reference to wizard for communication
            self._current_wizard = wizard

            wizard.show()

        except Exception as e:
            QMessageBox.critical(self, "Wizard Error", f"Failed to show analysis wizard: {e}")

    def _wizard_load_mesh(self, file_path):
        """Load mesh from wizard request"""
        try:
            self.show_loading_status("Loading mesh...")
            QApplication.processEvents()

            self.mesh_viewer.load_mesh(file_path)

            # Ask user for unit
            units = ["meters", "centimeters", "millimeters"]
            unit, ok = QInputDialog.getItem(self, "Select Mesh Unit",
                                          "What is the unit of the mesh coordinates?",
                                          units, 0, False)
            if ok:
                self.mesh_unit = unit
            else:
                self.mesh_unit = "meters"  # fallback

            self.update_mesh_info()
            self.hide_loading_status()

        except Exception as e:
            self.hide_loading_status()
            QMessageBox.critical(self, "Load Error", f"Failed to load mesh: {e}")

    def _wizard_define_plane(self):
        """Open plane definition from wizard"""
        self.show_plane_definition_choice()

        # Mark plane as defined in wizard (simplified - in real implementation,
        # you'd check if plane was actually defined successfully)
        if hasattr(self, '_current_wizard'):
            # Find the plane definition page and mark it complete
            for i in range(self._current_wizard.pageCount()):
                page = self._current_wizard.page(i)
                if hasattr(page, 'mark_plane_defined'):
                    page.mark_plane_defined()
                    break

    def _wizard_run_analysis(self):
        """Run analysis from wizard"""
        if hasattr(self, '_last_plane_params') and hasattr(self, '_last_slab_thickness'):
            slab_side = getattr(self, '_last_slab_side', 'centered')
            self.run_deformation_analysis(self._last_plane_params, self._last_slab_thickness, slab_side)

            # Mark analysis complete in wizard
            if hasattr(self, '_current_wizard'):
                for i in range(self._current_wizard.pageCount()):
                    page = self._current_wizard.page(i)
                    if hasattr(page, 'mark_analysis_complete'):
                        page.mark_analysis_complete()
                        break
        else:
            QMessageBox.warning(self, "Analysis Error", "Please define a plane first.")

    def _update_recent_files_menu(self):
        """Update the recent files menu with stored file paths"""
        self.recent_files_menu.clear()

        # Get recent files from settings
        recent_files = self.settings.value("recent_files", [])
        if not isinstance(recent_files, list):
            recent_files = []

        # Remove non-existent files
        recent_files = [f for f in recent_files if os.path.exists(f)]

        if not recent_files:
            # Show "No recent files" if empty
            no_files_action = self.recent_files_menu.addAction("No recent files")
            no_files_action.setEnabled(False)
            return

        # Add recent files to menu
        for i, file_path in enumerate(recent_files[:self.max_recent_files]):
            filename = os.path.basename(file_path)
            action_text = f"&{i+1} {filename}"
            action = self.recent_files_menu.addAction(action_text)
            action.setToolTip(file_path)
            action.triggered.connect(lambda checked, path=file_path: self._load_recent_file(path))

        # Add separator and clear action
        if recent_files:
            self.recent_files_menu.addSeparator()
            clear_action = self.recent_files_menu.addAction("Clear Recent Files")
            clear_action.triggered.connect(self._clear_recent_files)

    def _add_to_recent_files(self, file_path):
        """Add a file to the recent files list"""
        recent_files = self.settings.value("recent_files", [])
        if not isinstance(recent_files, list):
            recent_files = []

        # Remove if already exists
        if file_path in recent_files:
            recent_files.remove(file_path)

        # Add to beginning
        recent_files.insert(0, file_path)

        # Limit to max_recent_files
        recent_files = recent_files[:self.max_recent_files]

        # Save to settings
        self.settings.setValue("recent_files", recent_files)

        # Update menu
        self._update_recent_files_menu()

    def _load_recent_file(self, file_path):
        """Load a file from recent files list"""
        try:
            if not os.path.exists(file_path):
                QMessageBox.warning(self, "File Not Found",
                                  f"The file '{file_path}' no longer exists.")
                # Remove from recent files
                recent_files = self.settings.value("recent_files", [])
                if file_path in recent_files:
                    recent_files.remove(file_path)
                    self.settings.setValue("recent_files", recent_files)
                    self._update_recent_files_menu()
                return

            # Load the mesh
            self.show_loading_status("Loading mesh...")
            QApplication.processEvents()

            self.mesh_viewer.load_mesh(file_path)
            self.update_mesh_info()
            self.hide_loading_status()

            # Add to recent files
            self._add_to_recent_files(file_path)

            # Add to recent files (moves to top)
            self._add_to_recent_files(file_path)

        except Exception as e:
            self.hide_loading_status()
            QMessageBox.critical(self, "Load Error", f"Failed to load mesh: {e}")

    def _clear_recent_files(self):
        """Clear all recent files"""
        reply = QMessageBox.question(self, "Clear Recent Files",
                                   "Are you sure you want to clear all recent files?",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.settings.setValue("recent_files", [])
            self._update_recent_files_menu()

    def _save_session_to_file(self, file_path, silent=False):
        """Save complete session state to JSON file"""
        try:
            import json

            session_data = {
                "version": "1.0",
                "timestamp": str(QDateTime.currentDateTime().toString()),
                "mesh_info": {},
                "plane_params": {},
                "analysis_settings": {},
                "visualization_settings": {},
                "ui_state": {}
            }

            # Save mesh information
            if self.mesh_viewer.mesh is not None:
                mesh = self.mesh_viewer.mesh
                session_data["mesh_info"] = {
                    "has_mesh": True,
                    "n_points": int(mesh.n_points),
                    "n_cells": int(mesh.n_cells),
                    "bounds": list(mesh.bounds),
                    "unit": getattr(self, 'mesh_unit', 'meters'),
                    "point_data_arrays": list(mesh.point_data.keys()),
                    "cell_data_arrays": list(mesh.cell_data.keys())
                }

                # Note: We don't save the actual mesh data, just metadata
                # Users should save the mesh separately if needed

            # Save plane parameters
            if hasattr(self, '_last_plane_params'):
                plane_params = self._last_plane_params
                session_data["plane_params"] = {
                    "has_plane": True,
                    "origin": plane_params['origin'].tolist() if hasattr(plane_params['origin'], 'tolist') else list(plane_params['origin']),
                    "normal": plane_params['normal'].tolist() if hasattr(plane_params['normal'], 'tolist') else list(plane_params['normal']),
                    "slab_thickness": getattr(self, '_last_slab_thickness', 0.2),
                    "slab_side": getattr(self, '_last_slab_side', 'centered')
                }

            # Save analysis settings
            session_data["analysis_settings"] = {
                "outside_opacity": getattr(self, '_outside_opacity', 0.15),
                "current_colormap": "coolwarm"  # Default, could be enhanced to track actual colormap
            }

            # Save visualization settings
            if self.mesh_viewer.plotter:
                try:
                    camera = self.mesh_viewer.plotter.camera
                    session_data["visualization_settings"] = {
                        "camera_position": camera.position,
                        "camera_focal_point": camera.focal_point,
                        "camera_view_up": camera.up,
                        "camera_zoom": camera.zoom if hasattr(camera, 'zoom') else 1.0
                    }
                except Exception:
                    pass  # Camera info not critical

            # Save UI state
            session_data["ui_state"] = {
                "window_geometry": {
                    "x": self.x(),
                    "y": self.y(),
                    "width": self.width(),
                    "height": self.height()
                },
                "sidebar_visible": self.sidebar.isVisible(),
                "current_tab": self.tabs.currentIndex() if hasattr(self, 'tabs') else 0
            }

            # Write to file
            with open(file_path, 'w') as f:
                json.dump(session_data, f, indent=2)

            if not silent:
                QMessageBox.information(self, "Session Saved",
                                      f"Session saved successfully to:\n{file_path}\n\n"
                                      "Note: Mesh data is not included in session files. "
                                      "The mesh file will need to be reloaded when restoring the session.")

        except Exception as e:
            QMessageBox.critical(self, "Save Session Error", f"Failed to save session: {e}")

    def _load_session_from_file(self, file_path):
        """Load complete session state from JSON file"""
        try:
            import json

            # Read session file
            with open(file_path, 'r') as f:
                session_data = json.load(f)

            # Validate session file
            if "version" not in session_data:
                QMessageBox.warning(self, "Invalid Session", "This does not appear to be a valid session file.")
                return

            # Show loading dialog with mesh requirement
            mesh_info = session_data.get("mesh_info", {})
            if mesh_info.get("has_mesh", False):
                reply = QMessageBox.question(self, "Load Session",
                    f"This session requires a mesh with:\n"
                    f"• {mesh_info.get('n_points', 'Unknown')} points\n"
                    f"• {mesh_info.get('n_cells', 'Unknown')} cells\n"
                    f"• Unit: {mesh_info.get('unit', 'Unknown')}\n\n"
                    f"Please load the appropriate mesh file first, then continue loading the session.\n\n"
                    f"Do you want to load a mesh file now?",
                    QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel)

                if reply == QMessageBox.Yes:
                    # Load mesh first
                    self.load_mesh_dialog()
                    if self.mesh_viewer.mesh is None:
                        QMessageBox.warning(self, "Session Load Cancelled", "Session loading cancelled - no mesh loaded.")
                        return
                elif reply == QMessageBox.Cancel:
                    return
                # If No, continue without mesh (partial restore)

            # Restore plane parameters
            plane_params = session_data.get("plane_params", {})
            if plane_params.get("has_plane", False):
                try:
                    self._last_plane_params = {
                        'origin': np.array(plane_params["origin"]),
                        'normal': np.array(plane_params["normal"])
                    }
                    self._last_slab_thickness = plane_params.get("slab_thickness", 0.2)
                    self._last_slab_side = plane_params.get("slab_side", "centered")

                    # Show plane controls
                    if hasattr(self, 'slab_control_frame'):
                        self.slab_control_frame.setVisible(True)

                    # Update slab controls
                    if hasattr(self, 'slab_spin'):
                        self.slab_spin.setValue(self._last_slab_thickness)

                except Exception as e:
                    print(f"Warning: Could not restore plane parameters: {e}")

            # Restore analysis settings
            analysis_settings = session_data.get("analysis_settings", {})
            if "outside_opacity" in analysis_settings:
                self._outside_opacity = analysis_settings["outside_opacity"]
                if hasattr(self, 'opacity_spin'):
                    self.opacity_spin.setValue(self._outside_opacity)

            # Restore visualization settings
            viz_settings = session_data.get("visualization_settings", {})
            if viz_settings and self.mesh_viewer.plotter:
                try:
                    camera = self.mesh_viewer.plotter.camera
                    if "camera_position" in viz_settings:
                        camera.position = viz_settings["camera_position"]
                    if "camera_focal_point" in viz_settings:
                        camera.focal_point = viz_settings["camera_focal_point"]
                    if "camera_view_up" in viz_settings:
                        camera.up = viz_settings["camera_view_up"]
                    self.mesh_viewer.plotter.render()
                except Exception as e:
                    print(f"Warning: Could not restore camera settings: {e}")

            # Restore UI state
            ui_state = session_data.get("ui_state", {})
            if "window_geometry" in ui_state:
                geom = ui_state["window_geometry"]
                self.setGeometry(geom.get("x", 100), geom.get("y", 100),
                               geom.get("width", 1000), geom.get("height", 700))

            if "sidebar_visible" in ui_state and hasattr(self, 'sidebar'):
                self.sidebar.setVisible(ui_state["sidebar_visible"])

            if "current_tab" in ui_state and hasattr(self, 'tabs'):
                self.tabs.setCurrentIndex(ui_state["current_tab"])

            # Run analysis if we have both mesh and plane
            if (self.mesh_viewer.mesh is not None and
                hasattr(self, '_last_plane_params') and
                hasattr(self, '_last_slab_thickness')):

                reply = QMessageBox.question(self, "Restore Analysis",
                    "Do you want to run the deformation analysis with the restored settings?",
                    QMessageBox.Yes | QMessageBox.No)

                if reply == QMessageBox.Yes:
                    slab_side = getattr(self, '_last_slab_side', 'centered')
                    self.run_deformation_analysis(self._last_plane_params, self._last_slab_thickness, slab_side)

            QMessageBox.information(self, "Session Loaded",
                                  f"Session loaded successfully from:\n{file_path}\n\n"
                                  f"Restored settings include plane parameters, analysis settings, "
                                  f"and visualization preferences.")

        except Exception as e:
            QMessageBox.critical(self, "Load Session Error", f"Failed to load session: {e}")

    def show_quick_start_guide(self):
        """Show quick start guide dialog"""
        try:
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle("Quick Start Guide - DeformViz 3D")
            dialog.setModal(True)
            dialog.resize(600, 500)

            layout = QVBoxLayout(dialog)

            text_edit = QTextEdit()
            text_edit.setReadOnly(True)
            text_edit.setHtml("""
            <h2>🚀 Quick Start Guide</h2>

            <h3>🎯 For New Users - Use the Analysis Wizard!</h3>
            <p><b>Click the "Analysis Wizard" button</b> (first button in toolbar) for step-by-step guidance.</p>

            <h3>📋 Manual Workflow (5 Steps)</h3>
            <ol>
                <li><b>Load Mesh:</b> File → Open Mesh (Ctrl+O)</li>
                <li><b>Define Plane:</b> Click "Define Plane" button</li>
                <li><b>Set SLAB Thickness:</b> Use bottom slider</li>
                <li><b>Run Analysis:</b> Click "Show Deformation Heatmap"</li>
                <li><b>Export Results:</b> Screenshot, CSV, or save session</li>
            </ol>

            <h3>🔧 Essential Controls</h3>
            <ul>
                <li><b>Mouse:</b> Left=rotate, Right=pan, Wheel=zoom</li>
                <li><b>SLAB Thickness:</b> Bottom slider (real-time adjustment)</li>
                <li><b>Transparency:</b> Bottom slider (outside region opacity)</li>
                <li><b>Colormap:</b> "Select Colormap" button</li>
            </ul>

            <h3>💾 File Management</h3>
            <ul>
                <li><b>Recent Files:</b> File → Recent Files</li>
                <li><b>Save Session:</b> File → Save Session (Ctrl+S)</li>
                <li><b>Load Session:</b> File → Load Session (Ctrl+L)</li>
            </ul>

            <h3>🆘 Need More Help?</h3>
            <ul>
                <li><b>Detailed Workflow:</b> Help → Detailed Workflow</li>
                <li><b>Analysis Wizard:</b> Help → Analysis Wizard Help</li>
                <li><b>Keyboard Shortcuts:</b> Help → Keyboard Shortcuts</li>
            </ul>
            """)

            layout.addWidget(text_edit)

            # Buttons
            button_layout = QHBoxLayout()

            wizard_button = QPushButton("Start Analysis Wizard")
            wizard_button.clicked.connect(lambda: (dialog.accept(), self.show_analysis_wizard()))

            close_button = QPushButton("Close")
            close_button.clicked.connect(dialog.accept)

            button_layout.addWidget(wizard_button)
            button_layout.addStretch()
            button_layout.addWidget(close_button)

            layout.addLayout(button_layout)

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "Help Error", f"Failed to show quick start guide: {e}")

    def show_detailed_workflow(self):
        """Show detailed workflow dialog"""
        try:
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton, QTabWidget, QWidget

            dialog = QDialog(self)
            dialog.setWindowTitle("Detailed Workflow - DeformViz 3D")
            dialog.setModal(True)
            dialog.resize(800, 600)

            layout = QVBoxLayout(dialog)

            # Create tabbed interface
            tabs = QTabWidget()

            # Tab 1: Complete Workflow
            workflow_tab = QWidget()
            workflow_layout = QVBoxLayout(workflow_tab)

            workflow_text = QTextEdit()
            workflow_text.setReadOnly(True)
            workflow_text.setHtml("""
            <h2>🔄 Complete Analysis Workflow</h2>

            <h3>📁 Step 1: Load and Prepare Mesh</h3>
            <ol>
                <li><b>Load Mesh:</b> File → Open Mesh (Ctrl+O)</li>
                <li><b>Supported Formats:</b> PLY, OBJ, STL, VTK, VTP, GLTF, etc.</li>
                <li><b>Select Units:</b> Choose meters, centimeters, or millimeters</li>
                <li><b>Review Info:</b> Check mesh statistics in left sidebar</li>
                <li><b>Process if Needed:</b> Use "Process Mesh" for cleaning/smoothing</li>
            </ol>

            <h3>📐 Step 2: Define Reference Plane</h3>
            <ol>
                <li><b>Click "Define Plane"</b> button in toolbar</li>
                <li><b>Choose Method:</b>
                    <ul>
                        <li><b>3 Points:</b> Click 3 points on mesh surface</li>
                        <li><b>Canonical:</b> XY, YZ, or ZX plane alignment</li>
                    </ul>
                </li>
                <li><b>Verify Direction:</b> Check arrow color (Green/Red/Blue)</li>
                <li><b>Adjust Position:</b> Use translation controls if needed</li>
            </ol>

            <h3>⚙️ Step 3: Configure SLAB Analysis</h3>
            <ol>
                <li><b>Set Thickness:</b> Use bottom slider (real-time preview)</li>
                <li><b>Choose Side:</b> Centered, Positive, or Negative</li>
                <li><b>Adjust Transparency:</b> Outside region opacity (0-100%)</li>
                <li><b>Preview Region:</b> Yellow plane shows analysis area</li>
            </ol>

            <h3>📊 Step 4: Run Deformation Analysis</h3>
            <ol>
                <li><b>Click "Show Deformation Heatmap"</b></li>
                <li><b>Monitor Progress:</b> Progress dialog shows status</li>
                <li><b>Review Results:</b> Color-coded deformation values</li>
                <li><b>Interpret Colors:</b>
                    <ul>
                        <li><b>Blue:</b> Negative deformation (inward)</li>
                        <li><b>Green:</b> Near-zero deformation</li>
                        <li><b>Red:</b> Positive deformation (outward)</li>
                    </ul>
                </li>
            </ol>

            <h3>🎨 Step 5: Customize Visualization</h3>
            <ol>
                <li><b>Change Colormap:</b> "Select Colormap" button</li>
                <li><b>Display Mode:</b> Surface, wireframe, points</li>
                <li><b>Vector Field:</b> Show deformation directions</li>
                <li><b>Camera Views:</b> Use left toolbar for standard views</li>
            </ol>

            <h3>💾 Step 6: Export and Save</h3>
            <ol>
                <li><b>Screenshot:</b> F12 for quick, or high-quality options</li>
                <li><b>Data Export:</b> CSV format for analysis results</li>
                <li><b>Save Session:</b> Complete project state (Ctrl+S)</li>
                <li><b>Save Mesh:</b> Processed mesh with analysis data</li>
            </ol>
            """)

            workflow_layout.addWidget(workflow_text)
            tabs.addTab(workflow_tab, "Complete Workflow")

            layout.addWidget(tabs)

            # Close button
            close_button = QPushButton("Close")
            close_button.clicked.connect(dialog.accept)
            layout.addWidget(close_button)

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "Help Error", f"Failed to show detailed workflow: {e}")

    def show_wizard_help(self):
        """Show analysis wizard help"""
        try:
            help_text = """
🧙‍♂️ Analysis Wizard Help

The Analysis Wizard provides step-by-step guidance for new users:

📋 Step 1: Welcome
• Introduction to DeformViz 3D
• Overview of analysis process
• Tips for getting started

📁 Step 2: Load Mesh
• Choose your 3D mesh file
• Supported formats and recommendations
• Unit selection guidance

📐 Step 3: Define Plane
• Reference plane definition methods
• SLAB thickness configuration
• Visual feedback and validation

⚙️ Step 4: Configure Analysis
• Analysis parameter settings
• Visualization options
• Quality recommendations

📊 Step 5: View Results
• Result interpretation
• Export options
• Next steps guidance

💡 Tips:
• Use the wizard for your first analysis
• Each step includes detailed instructions
• You can exit and return anytime
• Settings are preserved between steps
            """

            QMessageBox.information(self, "Analysis Wizard Help", help_text)

        except Exception as e:
            QMessageBox.critical(self, "Help Error", f"Failed to show wizard help: {e}")

    def show_keyboard_shortcuts(self):
        """Show keyboard shortcuts dialog"""
        try:
            shortcuts_text = """
⌨️ Keyboard Shortcuts

📁 File Operations:
• Ctrl+O - Open Mesh
• Ctrl+S - Save Session
• Ctrl+L - Load Session
• Ctrl+E - Export CSV
• Ctrl+Q - Exit

🔧 Analysis:
• F1 - Quick Start Guide
• F12 - Quick Screenshot

🎨 View Controls:
• Mouse Left - Rotate view
• Mouse Right - Pan view
• Mouse Wheel - Zoom in/out

📋 Interface:
• Tab - Switch between sidebar tabs
• Esc - Close dialogs
• Enter - Confirm actions

💡 Pro Tips:
• Hold Shift while rotating for constrained rotation
• Use Ctrl+Mouse for fine adjustments
• Right-click for context menus
• Hover over buttons for detailed tooltips
            """

            QMessageBox.information(self, "Keyboard Shortcuts", shortcuts_text)

        except Exception as e:
            QMessageBox.critical(self, "Help Error", f"Failed to show keyboard shortcuts: {e}")

    def show_troubleshooting(self):
        """Show troubleshooting guide"""
        try:
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton

            dialog = QDialog(self)
            dialog.setWindowTitle("Troubleshooting Guide")
            dialog.setModal(True)
            dialog.resize(700, 500)

            layout = QVBoxLayout(dialog)

            text_edit = QTextEdit()
            text_edit.setReadOnly(True)
            text_edit.setHtml("""
            <h2>🔧 Troubleshooting Guide</h2>

            <h3>🚫 Common Issues</h3>

            <h4>Mesh Loading Problems</h4>
            <ul>
                <li><b>Large Files:</b> Use "Process Mesh" → Decimation to reduce size</li>
                <li><b>Format Issues:</b> Try converting to PLY or OBJ format</li>
                <li><b>Memory Errors:</b> Close other applications, restart software</li>
                <li><b>Corrupted Files:</b> Check file integrity, re-export from source</li>
            </ul>

            <h4>Plane Definition Issues</h4>
            <ul>
                <li><b>Points Too Close:</b> Select well-separated points</li>
                <li><b>Degenerate Plane:</b> Ensure points aren't collinear</li>
                <li><b>Wrong Direction:</b> Use flip direction or canonical alignment</li>
                <li><b>Alignment Problems:</b> Use canonical planes for standard orientations</li>
            </ul>

            <h4>Analysis Problems</h4>
            <ul>
                <li><b>No Results:</b> Verify plane is properly defined (yellow plane visible)</li>
                <li><b>Empty SLAB:</b> Increase SLAB thickness or adjust position</li>
                <li><b>Wrong Colors:</b> Check colormap selection and range</li>
                <li><b>Performance Issues:</b> Reduce mesh complexity before analysis</li>
            </ul>

            <h4>Visualization Issues</h4>
            <ul>
                <li><b>Missing Colors:</b> Ensure analysis has been run</li>
                <li><b>Colorbar Problems:</b> Try different colormap selection</li>
                <li><b>Rendering Errors:</b> Update graphics drivers</li>
                <li><b>Slow Performance:</b> Use wireframe mode for large meshes</li>
            </ul>

            <h3>⚡ Performance Optimization</h3>
            <ul>
                <li><b>Mesh Decimation:</b> Reduce triangle count for large models</li>
                <li><b>SLAB Thickness:</b> Use appropriate thickness for analysis</li>
                <li><b>Display Mode:</b> Wireframe for very large meshes</li>
                <li><b>Session Management:</b> Save work frequently</li>
            </ul>

            <h3>✅ Best Practices</h3>
            <ul>
                <li><b>Mesh Quality:</b> Clean and repair meshes before analysis</li>
                <li><b>Plane Positioning:</b> Choose representative reference surfaces</li>
                <li><b>SLAB Selection:</b> Match analysis region to research questions</li>
                <li><b>Documentation:</b> Use session files to document parameters</li>
            </ul>

            <h3>🆘 Getting Help</h3>
            <ul>
                <li><b>Analysis Wizard:</b> Use for guided workflow</li>
                <li><b>Tooltips:</b> Hover over buttons for detailed help</li>
                <li><b>Session Files:</b> Save problematic states for debugging</li>
                <li><b>Error Messages:</b> Note exact error text for support</li>
            </ul>
            """)

            layout.addWidget(text_edit)

            close_button = QPushButton("Close")
            close_button.clicked.connect(dialog.accept)
            layout.addWidget(close_button)

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "Help Error", f"Failed to show troubleshooting guide: {e}")

    def show_about_dialog(self):
        """Show about dialog"""
        try:
            about_text = """
🎯 DeformViz 3D v2.0.0

Professional 3D Deformation Analysis Software

🔬 Scientific Features:
• SLAB-aware deformation analysis
• Professional visualization with scientific colormaps
• Advanced mesh processing and analysis tools
• Comprehensive export and reporting capabilities

🎨 User Experience:
• Analysis Wizard for guided workflows
• Session management for project continuity
• Recent files and professional file handling
• Comprehensive help and documentation

🏗️ Technical Excellence:
• Built with PyVista/VTK for high-performance 3D rendering
• Qt-based professional interface
• Robust error handling and validation
• Cross-platform compatibility

🎯 Applications:
• Structural engineering analysis
• Manufacturing quality control
• Geological monitoring
• Academic research
• Professional consulting

💡 Perfect for engineers, researchers, and analysts who need
precise 3D deformation measurement and visualization.

🚀 Production-ready professional software with
commercial-grade quality and reliability.

© 2025 DeformViz 3D Development Team
            """

            QMessageBox.about(self, "About DeformViz 3D", about_text)

        except Exception as e:
            QMessageBox.critical(self, "About Error", f"Failed to show about dialog: {e}")

    # Drag and Drop Event Handlers
    def dragEnterEvent(self, event):
        """Handle drag enter events for file dropping"""
        if event.mimeData().hasUrls():
            # Check if any of the dragged files are supported mesh formats
            urls = event.mimeData().urls()
            supported_extensions = {
                '.stl', '.obj', '.ply', '.vtk', '.vtp', '.off', '.gltf', '.glb',
                '.3ds', '.dae', '.fbx', '.x', '.wrl', '.mesh', '.msh', '.ctm',
                '.gts', '.usd', '.usda', '.usdc', '.usdz'
            }

            for url in urls:
                if url.isLocalFile():
                    file_path = url.toLocalFile()
                    file_ext = os.path.splitext(file_path)[1].lower()
                    if file_ext in supported_extensions:
                        event.acceptProposedAction()
                        return

            # If no supported files found, reject
            event.ignore()
        else:
            event.ignore()

    def dragMoveEvent(self, event):
        """Handle drag move events"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
        else:
            event.ignore()

    def dropEvent(self, event):
        """Handle drop events for file loading"""
        try:
            if event.mimeData().hasUrls():
                urls = event.mimeData().urls()
                supported_extensions = {
                    '.stl', '.obj', '.ply', '.vtk', '.vtp', '.off', '.gltf', '.glb',
                    '.3ds', '.dae', '.fbx', '.x', '.wrl', '.mesh', '.msh', '.ctm',
                    '.gts', '.usd', '.usda', '.usdc', '.usdz'
                }

                # Find the first supported file
                for url in urls:
                    if url.isLocalFile():
                        file_path = url.toLocalFile()
                        file_ext = os.path.splitext(file_path)[1].lower()

                        if file_ext in supported_extensions:
                            # Load the mesh file
                            self._load_dropped_file(file_path)
                            event.acceptProposedAction()
                            return

                # If no supported files found
                QMessageBox.warning(self, "Unsupported File",
                                  "No supported mesh files found in the dropped items.\n\n"
                                  "Supported formats: STL, OBJ, PLY, VTK, VTP, OFF, GLTF, GLB, "
                                  "3DS, DAE, FBX, X, WRL, MESH, MSH, CTM, GTS, USD, USDA, USDC, USDZ")
                event.ignore()
            else:
                event.ignore()

        except Exception as e:
            QMessageBox.critical(self, "Drop Error", f"Failed to handle dropped file: {e}")
            event.ignore()

    def _load_dropped_file(self, file_path):
        """Load a mesh file that was dropped onto the application"""
        try:
            # Show confirmation dialog with file info
            import os
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)

            reply = QMessageBox.question(self, "Load Dropped File",
                f"Load mesh file?\n\n"
                f"File: {file_name}\n"
                f"Size: {file_size_mb:.1f} MB\n"
                f"Path: {file_path}\n\n"
                f"This will replace the current mesh if one is loaded.",
                QMessageBox.Yes | QMessageBox.No)

            if reply == QMessageBox.Yes:
                # Use the existing load mesh functionality
                self.show_loading_status(f"Loading dropped file: {file_name}")
                QApplication.processEvents()

                self.mesh_viewer.load_mesh(file_path)

                # Ask user for unit (same as regular load)
                units = ["meters", "centimeters", "millimeters"]
                unit, ok = QInputDialog.getItem(self, "Select Unit", "Choose the mesh unit:", units, 0, False)
                if ok:
                    self.mesh_unit = unit
                else:
                    self.mesh_unit = "meters"  # fallback

                self.update_mesh_info()
                self.hide_loading_status()

                # Update enhanced status
                self._update_enhanced_status()

                # Add to recent files
                self._add_to_recent_files(file_path)

                # Show success message
                self.statusBar().showMessage(f"Successfully loaded: {file_name}", 5000)

                # Suggest using Analysis Wizard for new users
                if not hasattr(self, '_wizard_suggestion_shown'):
                    reply = QMessageBox.question(self, "Analysis Wizard",
                        f"Mesh loaded successfully!\n\n"
                        f"Would you like to use the Analysis Wizard for guided "
                        f"step-by-step analysis? Perfect for new users!",
                        QMessageBox.Yes | QMessageBox.No)

                    if reply == QMessageBox.Yes:
                        self.show_analysis_wizard()

                    self._wizard_suggestion_shown = True

        except Exception as e:
            self.hide_loading_status()
            QMessageBox.critical(self, "Load Error", f"Failed to load dropped file: {e}")

    def _setup_auto_save(self):
        """Setup automatic session saving"""
        try:
            # Create auto-save timer
            self.auto_save_timer = QTimer()
            self.auto_save_timer.timeout.connect(self._auto_save_session)

            # Auto-save every 5 minutes (300,000 milliseconds)
            auto_save_interval = self.settings.value("auto_save_interval", 300000, type=int)
            self.auto_save_timer.start(auto_save_interval)

            # Track if auto-save is enabled
            self.auto_save_enabled = self.settings.value("auto_save_enabled", True, type=bool)

            # Create auto-save directory
            self.auto_save_dir = os.path.expanduser("~/.deformviz3d/autosave")
            os.makedirs(self.auto_save_dir, exist_ok=True)

            # Track changes for smart auto-save
            self._last_auto_save_state = None
            self._has_unsaved_changes = False

            print(f"Auto-save initialized: {auto_save_interval/1000/60:.1f} minutes interval")

        except Exception as e:
            print(f"Warning: Failed to setup auto-save: {e}")

    def _auto_save_session(self):
        """Automatically save the current session"""
        try:
            if not self.auto_save_enabled:
                return

            # Only auto-save if we have meaningful content
            if (self.mesh_viewer.mesh is None and
                not hasattr(self, '_last_plane_params')):
                return

            # Check if there are actual changes since last auto-save
            current_state = self._get_session_state_hash()
            if current_state == self._last_auto_save_state:
                return  # No changes, skip auto-save

            # Generate auto-save filename with timestamp
            timestamp = QDateTime.currentDateTime().toString("yyyy-MM-dd_hh-mm-ss")
            auto_save_file = os.path.join(self.auto_save_dir, f"autosave_{timestamp}.json")

            # Save session silently
            self._save_session_to_file(auto_save_file, silent=True)
            self._last_auto_save_state = current_state

            # Clean up old auto-save files (keep last 10)
            self._cleanup_auto_save_files()

            # Update status bar briefly
            self.statusBar().showMessage("Auto-saved", 2000)

        except Exception as e:
            print(f"Auto-save failed: {e}")

    def _get_session_state_hash(self):
        """Get a hash representing the current session state"""
        try:
            import hashlib

            state_data = []

            # Include mesh info
            if self.mesh_viewer.mesh is not None:
                mesh = self.mesh_viewer.mesh
                state_data.append(f"mesh:{mesh.n_points}:{mesh.n_cells}")

            # Include plane parameters
            if hasattr(self, '_last_plane_params'):
                plane_params = self._last_plane_params
                state_data.append(f"plane:{plane_params['origin'].tolist()}:{plane_params['normal'].tolist()}")

            # Include SLAB settings
            if hasattr(self, '_last_slab_thickness'):
                state_data.append(f"slab:{self._last_slab_thickness}")

            # Create hash
            state_string = "|".join(state_data)
            return hashlib.md5(state_string.encode()).hexdigest()

        except Exception:
            return None

    def _cleanup_auto_save_files(self):
        """Clean up old auto-save files, keeping only the most recent ones"""
        try:
            if not os.path.exists(self.auto_save_dir):
                return

            # Get all auto-save files
            auto_save_files = []
            for filename in os.listdir(self.auto_save_dir):
                if filename.startswith("autosave_") and filename.endswith(".json"):
                    file_path = os.path.join(self.auto_save_dir, filename)
                    auto_save_files.append((file_path, os.path.getmtime(file_path)))

            # Sort by modification time (newest first)
            auto_save_files.sort(key=lambda x: x[1], reverse=True)

            # Keep only the 10 most recent files
            max_auto_saves = 10
            for file_path, _ in auto_save_files[max_auto_saves:]:
                try:
                    os.remove(file_path)
                except Exception:
                    pass  # Ignore errors when cleaning up

        except Exception:
            pass  # Ignore cleanup errors

    def _setup_enhanced_status(self):
        """Setup enhanced status feedback system"""
        try:
            # Create status widgets
            self.status_mesh_label = QLabel("No mesh loaded")
            self.status_mesh_label.setStyleSheet("color: #666; font-size: 11px;")

            self.status_plane_label = QLabel("No plane defined")
            self.status_plane_label.setStyleSheet("color: #666; font-size: 11px;")

            self.status_analysis_label = QLabel("No analysis")
            self.status_analysis_label.setStyleSheet("color: #666; font-size: 11px;")

            self.status_unit_label = QLabel("Units: meters")
            self.status_unit_label.setStyleSheet("color: #666; font-size: 11px;")

            # Add to status bar
            self.statusBar().addPermanentWidget(self.status_mesh_label)
            self.statusBar().addPermanentWidget(QLabel("|"))
            self.statusBar().addPermanentWidget(self.status_plane_label)
            self.statusBar().addPermanentWidget(QLabel("|"))
            self.statusBar().addPermanentWidget(self.status_analysis_label)
            self.statusBar().addPermanentWidget(QLabel("|"))
            self.statusBar().addPermanentWidget(self.status_unit_label)

            # Initialize status
            self._update_enhanced_status()

        except Exception as e:
            print(f"Warning: Failed to setup enhanced status: {e}")

    def _update_enhanced_status(self):
        """Update the enhanced status indicators"""
        try:
            # Update mesh status
            if self.mesh_viewer.mesh is not None:
                mesh = self.mesh_viewer.mesh
                self.status_mesh_label.setText(f"Mesh: {mesh.n_points:,} pts, {mesh.n_cells:,} cells")
                self.status_mesh_label.setStyleSheet("color: #0a7c0a; font-size: 11px; font-weight: bold;")
            else:
                self.status_mesh_label.setText("No mesh loaded")
                self.status_mesh_label.setStyleSheet("color: #666; font-size: 11px;")

            # Update plane status
            if hasattr(self, '_last_plane_params') and self._last_plane_params is not None:
                slab_thickness = getattr(self, '_last_slab_thickness', 0.2)
                self.status_plane_label.setText(f"Plane: SLAB {slab_thickness:.3f}m")
                self.status_plane_label.setStyleSheet("color: #0a7c0a; font-size: 11px; font-weight: bold;")
            else:
                self.status_plane_label.setText("No plane defined")
                self.status_plane_label.setStyleSheet("color: #666; font-size: 11px;")

            # Update analysis status
            if (self.mesh_viewer.mesh is not None and
                hasattr(self, '_last_deformation_mask') and
                self._last_deformation_mask is not None):
                mask_count = self._last_deformation_mask.sum()
                total_count = len(self._last_deformation_mask)
                percentage = (mask_count / total_count) * 100 if total_count > 0 else 0
                self.status_analysis_label.setText(f"Analysis: {mask_count:,}/{total_count:,} pts ({percentage:.1f}%)")
                self.status_analysis_label.setStyleSheet("color: #0a7c0a; font-size: 11px; font-weight: bold;")
            else:
                self.status_analysis_label.setText("No analysis")
                self.status_analysis_label.setStyleSheet("color: #666; font-size: 11px;")

            # Update unit status
            unit = getattr(self, 'mesh_unit', 'meters')
            self.status_unit_label.setText(f"Units: {unit}")
            self.status_unit_label.setStyleSheet("color: #0066cc; font-size: 11px; font-weight: bold;")

        except Exception as e:
            print(f"Warning: Failed to update enhanced status: {e}")

    def show_enhanced_status_message(self, message, duration=3000, message_type="info"):
        """Show enhanced status message with type-based styling"""
        try:
            # Color coding based on message type
            if message_type == "success":
                styled_message = f"✅ {message}"
            elif message_type == "warning":
                styled_message = f"⚠️ {message}"
            elif message_type == "error":
                styled_message = f"❌ {message}"
            elif message_type == "progress":
                styled_message = f"⏳ {message}"
            else:  # info
                styled_message = f"ℹ️ {message}"

            self.statusBar().showMessage(styled_message, duration)

        except Exception as e:
            # Fallback to regular status message
            self.statusBar().showMessage(message, duration)

    def _setup_split_screen(self):
        """Setup split-screen comparison functionality"""
        try:
            # Initialize split-screen state
            self.split_screen_enabled = False
            self.split_screen_widget = None
            self.left_viewer = None
            self.right_viewer = None

            # Store original central widget
            self.original_central_widget = None

            print("Split-screen functionality initialized")

        except Exception as e:
            print(f"Warning: Failed to setup split-screen: {e}")

    def toggle_split_screen(self):
        """Toggle between single and split-screen view"""
        try:
            if not self.split_screen_enabled:
                self._enable_split_screen()
            else:
                self._disable_split_screen()

        except Exception as e:
            QMessageBox.critical(self, "Split-Screen Error", f"Failed to toggle split-screen: {e}")

    def _enable_split_screen(self):
        """Enable split-screen mode"""
        try:
            from PySide6.QtWidgets import QSplitter, QVBoxLayout, QWidget, QLabel, QPushButton, QHBoxLayout

            # Store original central widget
            self.original_central_widget = self.centralWidget()

            # Create split-screen widget
            self.split_screen_widget = QSplitter(Qt.Horizontal)

            # Create left panel
            left_panel = QWidget()
            left_layout = QVBoxLayout(left_panel)

            # Left panel header
            left_header = QWidget()
            left_header_layout = QHBoxLayout(left_header)
            left_header_layout.setContentsMargins(5, 5, 5, 5)

            left_title = QLabel("Original View")
            left_title.setStyleSheet("font-weight: bold; font-size: 12px; color: #0066cc;")

            copy_to_right_btn = QPushButton("Copy to Right →")
            copy_to_right_btn.setMaximumWidth(120)
            copy_to_right_btn.clicked.connect(self._copy_left_to_right)

            left_header_layout.addWidget(left_title)
            left_header_layout.addStretch()
            left_header_layout.addWidget(copy_to_right_btn)

            # Move original viewer to left panel
            left_layout.addWidget(left_header)
            left_layout.addWidget(self.original_central_widget)

            # Create right panel
            right_panel = QWidget()
            right_layout = QVBoxLayout(right_panel)

            # Right panel header
            right_header = QWidget()
            right_header_layout = QHBoxLayout(right_header)
            right_header_layout.setContentsMargins(5, 5, 5, 5)

            right_title = QLabel("Comparison View")
            right_title.setStyleSheet("font-weight: bold; font-size: 12px; color: #cc6600;")

            copy_to_left_btn = QPushButton("← Copy to Left")
            copy_to_left_btn.setMaximumWidth(120)
            copy_to_left_btn.clicked.connect(self._copy_right_to_left)

            right_header_layout.addWidget(copy_to_left_btn)
            right_header_layout.addStretch()
            right_header_layout.addWidget(right_title)

            # Create right viewer (clone of original)
            from src.gui.meshviewer import MeshViewerWidget
            self.right_viewer = MeshViewerWidget()

            right_layout.addWidget(right_header)
            right_layout.addWidget(self.right_viewer)

            # Add panels to splitter
            self.split_screen_widget.addWidget(left_panel)
            self.split_screen_widget.addWidget(right_panel)

            # Set equal sizes
            self.split_screen_widget.setSizes([500, 500])

            # Set as central widget
            self.setCentralWidget(self.split_screen_widget)

            # Update state
            self.split_screen_enabled = True
            self.left_viewer = self.mesh_viewer  # Original viewer is now left viewer

            # Copy current state to right viewer
            self._copy_left_to_right()

            self.show_enhanced_status_message("Split-screen mode enabled", 3000, "success")

        except Exception as e:
            QMessageBox.critical(self, "Split-Screen Error", f"Failed to enable split-screen: {e}")

    def _disable_split_screen(self):
        """Disable split-screen mode"""
        try:
            if self.original_central_widget:
                # Restore original central widget
                self.setCentralWidget(self.original_central_widget)

                # Clean up split-screen widgets
                if self.split_screen_widget:
                    self.split_screen_widget.deleteLater()
                    self.split_screen_widget = None

                if self.right_viewer:
                    self.right_viewer.deleteLater()
                    self.right_viewer = None

                # Update state
                self.split_screen_enabled = False
                self.left_viewer = None

                self.show_enhanced_status_message("Split-screen mode disabled", 3000, "info")

        except Exception as e:
            QMessageBox.critical(self, "Split-Screen Error", f"Failed to disable split-screen: {e}")

    def _copy_left_to_right(self):
        """Copy the current state from left viewer to right viewer"""
        try:
            if not self.split_screen_enabled or not self.right_viewer:
                return

            # Copy mesh
            if self.mesh_viewer.mesh is not None:
                self.right_viewer.mesh = self.mesh_viewer.mesh.copy()
                self.right_viewer.set_display_mode(getattr(self.mesh_viewer, 'display_mode', 'surface'))

            # Copy camera position
            if self.mesh_viewer.plotter and self.right_viewer.plotter:
                try:
                    left_camera = self.mesh_viewer.plotter.camera
                    right_camera = self.right_viewer.plotter.camera

                    right_camera.position = left_camera.position
                    right_camera.focal_point = left_camera.focal_point
                    right_camera.up = left_camera.up

                    self.right_viewer.plotter.render()
                except Exception:
                    pass

            self.show_enhanced_status_message("Copied to right panel", 2000, "info")

        except Exception as e:
            print(f"Failed to copy left to right: {e}")

    def _copy_right_to_left(self):
        """Copy the current state from right viewer to left viewer"""
        try:
            if not self.split_screen_enabled or not self.right_viewer:
                return

            # Copy mesh
            if self.right_viewer.mesh is not None:
                self.mesh_viewer.mesh = self.right_viewer.mesh.copy()
                self.mesh_viewer.set_display_mode(getattr(self.right_viewer, 'display_mode', 'surface'))
                self.update_mesh_info()
                self._update_enhanced_status()

            # Copy camera position
            if self.mesh_viewer.plotter and self.right_viewer.plotter:
                try:
                    left_camera = self.mesh_viewer.plotter.camera
                    right_camera = self.right_viewer.plotter.camera

                    left_camera.position = right_camera.position
                    left_camera.focal_point = right_camera.focal_point
                    left_camera.up = right_camera.up

                    self.mesh_viewer.plotter.render()
                except Exception:
                    pass

            self.show_enhanced_status_message("Copied to left panel", 2000, "info")

        except Exception as e:
            print(f"Failed to copy right to left: {e}")

    def show_batch_processing_dialog(self):
        """Show batch processing dialog for multiple file analysis"""
        try:
            from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QListWidget,
                                         QPushButton, QLabel, QProgressBar, QTextEdit,
                                         QGroupBox, QCheckBox, QSpinBox, QComboBox,
                                         QFileDialog, QListWidgetItem)

            dialog = QDialog(self)
            dialog.setWindowTitle("Batch Processing - DeformViz 3D")
            dialog.setModal(True)
            dialog.resize(800, 600)

            layout = QVBoxLayout(dialog)

            # File selection section
            file_group = QGroupBox("Files to Process")
            file_layout = QVBoxLayout(file_group)

            # File list
            self.batch_file_list = QListWidget()
            self.batch_file_list.setMinimumHeight(150)
            file_layout.addWidget(self.batch_file_list)

            # File buttons
            file_buttons = QHBoxLayout()

            add_files_btn = QPushButton("Add Files...")
            add_files_btn.clicked.connect(self._add_batch_files)

            add_folder_btn = QPushButton("Add Folder...")
            add_folder_btn.clicked.connect(self._add_batch_folder)

            remove_files_btn = QPushButton("Remove Selected")
            remove_files_btn.clicked.connect(self._remove_batch_files)

            clear_files_btn = QPushButton("Clear All")
            clear_files_btn.clicked.connect(self._clear_batch_files)

            file_buttons.addWidget(add_files_btn)
            file_buttons.addWidget(add_folder_btn)
            file_buttons.addWidget(remove_files_btn)
            file_buttons.addWidget(clear_files_btn)
            file_buttons.addStretch()

            file_layout.addLayout(file_buttons)
            layout.addWidget(file_group)

            # Processing options section
            options_group = QGroupBox("Processing Options")
            options_layout = QVBoxLayout(options_group)

            # Analysis options
            self.batch_run_analysis = QCheckBox("Run deformation analysis")
            self.batch_run_analysis.setChecked(True)
            options_layout.addWidget(self.batch_run_analysis)

            self.batch_generate_screenshots = QCheckBox("Generate screenshots")
            self.batch_generate_screenshots.setChecked(True)
            options_layout.addWidget(self.batch_generate_screenshots)

            self.batch_export_csv = QCheckBox("Export CSV data")
            self.batch_export_csv.setChecked(True)
            options_layout.addWidget(self.batch_export_csv)

            self.batch_save_sessions = QCheckBox("Save session files")
            self.batch_save_sessions.setChecked(False)
            options_layout.addWidget(self.batch_save_sessions)

            # SLAB thickness setting
            slab_layout = QHBoxLayout()
            slab_layout.addWidget(QLabel("SLAB Thickness:"))
            self.batch_slab_thickness = QSpinBox()
            self.batch_slab_thickness.setRange(1, 1000)
            self.batch_slab_thickness.setValue(200)  # 0.2m in mm
            self.batch_slab_thickness.setSuffix(" mm")
            slab_layout.addWidget(self.batch_slab_thickness)
            slab_layout.addStretch()
            options_layout.addLayout(slab_layout)

            # Output directory
            output_layout = QHBoxLayout()
            output_layout.addWidget(QLabel("Output Directory:"))
            self.batch_output_dir = QLabel("(Select output directory)")
            self.batch_output_dir.setStyleSheet("color: #666; font-style: italic;")
            output_layout.addWidget(self.batch_output_dir)

            select_output_btn = QPushButton("Browse...")
            select_output_btn.clicked.connect(self._select_batch_output_dir)
            output_layout.addWidget(select_output_btn)

            options_layout.addLayout(output_layout)
            layout.addWidget(options_group)

            # Progress section
            progress_group = QGroupBox("Progress")
            progress_layout = QVBoxLayout(progress_group)

            self.batch_progress_bar = QProgressBar()
            progress_layout.addWidget(self.batch_progress_bar)

            self.batch_status_text = QTextEdit()
            self.batch_status_text.setMaximumHeight(100)
            self.batch_status_text.setReadOnly(True)
            progress_layout.addWidget(self.batch_status_text)

            layout.addWidget(progress_group)

            # Buttons
            button_layout = QHBoxLayout()

            self.batch_start_btn = QPushButton("Start Batch Processing")
            self.batch_start_btn.clicked.connect(self._start_batch_processing)

            self.batch_stop_btn = QPushButton("Stop")
            self.batch_stop_btn.setEnabled(False)
            self.batch_stop_btn.clicked.connect(self._stop_batch_processing)

            close_btn = QPushButton("Close")
            close_btn.clicked.connect(dialog.accept)

            button_layout.addWidget(self.batch_start_btn)
            button_layout.addWidget(self.batch_stop_btn)
            button_layout.addStretch()
            button_layout.addWidget(close_btn)

            layout.addLayout(button_layout)

            # Initialize state
            self.batch_processing_active = False
            self.batch_output_directory = None

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "Batch Processing Error", f"Failed to show batch processing dialog: {e}")

    def _add_batch_files(self):
        """Add files to batch processing list"""
        try:
            filters = (
                "3D Mesh Files (*.stl *.obj *.ply *.vtk *.vtp *.off *.gltf *.glb *.3ds *.dae *.fbx *.x *.wrl *.mesh *.msh *.ctm *.gts *.usd *.usda *.usdc *.usdz);;"
                "All Files (*)"
            )

            files, _ = QFileDialog.getOpenFileNames(self, "Select Mesh Files for Batch Processing", "", filters)

            for file_path in files:
                # Check if file already in list
                existing_items = [self.batch_file_list.item(i).text() for i in range(self.batch_file_list.count())]
                if file_path not in existing_items:
                    self.batch_file_list.addItem(file_path)

            self._update_batch_status()

        except Exception as e:
            QMessageBox.warning(self, "Add Files Error", f"Failed to add files: {e}")

    def _add_batch_folder(self):
        """Add all mesh files from a folder to batch processing list"""
        try:
            folder_path = QFileDialog.getExistingDirectory(self, "Select Folder with Mesh Files")

            if folder_path:
                supported_extensions = {
                    '.stl', '.obj', '.ply', '.vtk', '.vtp', '.off', '.gltf', '.glb',
                    '.3ds', '.dae', '.fbx', '.x', '.wrl', '.mesh', '.msh', '.ctm',
                    '.gts', '.usd', '.usda', '.usdc', '.usdz'
                }

                added_count = 0
                for root, dirs, files in os.walk(folder_path):
                    for file in files:
                        file_ext = os.path.splitext(file)[1].lower()
                        if file_ext in supported_extensions:
                            file_path = os.path.join(root, file)

                            # Check if file already in list
                            existing_items = [self.batch_file_list.item(i).text() for i in range(self.batch_file_list.count())]
                            if file_path not in existing_items:
                                self.batch_file_list.addItem(file_path)
                                added_count += 1

                QMessageBox.information(self, "Folder Added", f"Added {added_count} mesh files from folder.")
                self._update_batch_status()

        except Exception as e:
            QMessageBox.warning(self, "Add Folder Error", f"Failed to add folder: {e}")

    def _remove_batch_files(self):
        """Remove selected files from batch processing list"""
        try:
            selected_items = self.batch_file_list.selectedItems()
            for item in selected_items:
                row = self.batch_file_list.row(item)
                self.batch_file_list.takeItem(row)

            self._update_batch_status()

        except Exception as e:
            QMessageBox.warning(self, "Remove Files Error", f"Failed to remove files: {e}")

    def _clear_batch_files(self):
        """Clear all files from batch processing list"""
        try:
            self.batch_file_list.clear()
            self._update_batch_status()

        except Exception as e:
            QMessageBox.warning(self, "Clear Files Error", f"Failed to clear files: {e}")

    def _select_batch_output_dir(self):
        """Select output directory for batch processing"""
        try:
            directory = QFileDialog.getExistingDirectory(self, "Select Output Directory")

            if directory:
                self.batch_output_directory = directory
                self.batch_output_dir.setText(directory)
                self.batch_output_dir.setStyleSheet("color: #0066cc; font-weight: bold;")
                self._update_batch_status()

        except Exception as e:
            QMessageBox.warning(self, "Output Directory Error", f"Failed to select output directory: {e}")

    def _update_batch_status(self):
        """Update batch processing status"""
        try:
            file_count = self.batch_file_list.count()
            has_output_dir = self.batch_output_directory is not None

            self.batch_start_btn.setEnabled(file_count > 0 and has_output_dir and not self.batch_processing_active)

            if file_count == 0:
                status = "No files selected for processing"
            elif not has_output_dir:
                status = f"{file_count} files selected - Please select output directory"
            else:
                status = f"{file_count} files ready for processing"

            self.batch_status_text.append(status)

        except Exception as e:
            print(f"Failed to update batch status: {e}")

    def _start_batch_processing(self):
        """Start batch processing of selected files"""
        try:
            if self.batch_file_list.count() == 0:
                QMessageBox.warning(self, "No Files", "Please add files to process.")
                return

            if not self.batch_output_directory:
                QMessageBox.warning(self, "No Output Directory", "Please select an output directory.")
                return

            # Confirm start
            file_count = self.batch_file_list.count()
            reply = QMessageBox.question(self, "Start Batch Processing",
                f"Start processing {file_count} files?\n\n"
                f"This may take a while depending on file sizes and selected options.",
                QMessageBox.Yes | QMessageBox.No)

            if reply != QMessageBox.Yes:
                return

            # Initialize processing
            self.batch_processing_active = True
            self.batch_start_btn.setEnabled(False)
            self.batch_stop_btn.setEnabled(True)
            self.batch_progress_bar.setMaximum(file_count)
            self.batch_progress_bar.setValue(0)

            # Clear status
            self.batch_status_text.clear()
            self.batch_status_text.append(f"Starting batch processing of {file_count} files...")

            # Process files
            self._process_batch_files()

        except Exception as e:
            QMessageBox.critical(self, "Batch Processing Error", f"Failed to start batch processing: {e}")
            self._stop_batch_processing()

    def _stop_batch_processing(self):
        """Stop batch processing"""
        try:
            self.batch_processing_active = False
            self.batch_start_btn.setEnabled(True)
            self.batch_stop_btn.setEnabled(False)

            self.batch_status_text.append("Batch processing stopped.")

        except Exception as e:
            print(f"Failed to stop batch processing: {e}")

    def _process_batch_files(self):
        """Process all files in the batch list"""
        try:
            file_count = self.batch_file_list.count()
            slab_thickness = self.batch_slab_thickness.value() / 1000.0  # Convert mm to meters

            successful_count = 0
            failed_count = 0

            for i in range(file_count):
                if not self.batch_processing_active:
                    break

                file_path = self.batch_file_list.item(i).text()
                file_name = os.path.basename(file_path)

                self.batch_status_text.append(f"\nProcessing {i+1}/{file_count}: {file_name}")
                self.batch_progress_bar.setValue(i)
                QApplication.processEvents()

                try:
                    # Load mesh
                    self.batch_status_text.append(f"  Loading mesh...")
                    QApplication.processEvents()

                    self.mesh_viewer.load_mesh(file_path)

                    if self.mesh_viewer.mesh is None:
                        raise Exception("Failed to load mesh")

                    # Create output subdirectory for this file
                    file_base = os.path.splitext(file_name)[0]
                    output_subdir = os.path.join(self.batch_output_directory, file_base)
                    os.makedirs(output_subdir, exist_ok=True)

                    # Run analysis if requested
                    if self.batch_run_analysis.isChecked():
                        self.batch_status_text.append(f"  Running deformation analysis...")
                        QApplication.processEvents()

                        # Use automatic plane definition (XY plane for batch processing)
                        mesh = self.mesh_viewer.mesh
                        bounds = mesh.bounds
                        center = [(bounds[0] + bounds[1])/2, (bounds[2] + bounds[3])/2, (bounds[4] + bounds[5])/2]

                        plane_params = {
                            'origin': np.array(center),
                            'normal': np.array([0, 0, 1])  # XY plane
                        }

                        self.run_deformation_analysis(plane_params, slab_thickness, 'centered')

                    # Generate screenshot if requested
                    if self.batch_generate_screenshots.isChecked():
                        self.batch_status_text.append(f"  Generating screenshot...")
                        QApplication.processEvents()

                        screenshot_path = os.path.join(output_subdir, f"{file_base}_screenshot.png")
                        img = self.mesh_viewer.plotter.screenshot(return_img=True, scale=2)

                        try:
                            import imageio
                            imageio.imwrite(screenshot_path, img)
                        except ImportError:
                            # Fallback to PIL if imageio not available
                            from PIL import Image
                            Image.fromarray(img).save(screenshot_path)

                    # Export CSV if requested
                    if self.batch_export_csv.isChecked() and self.mesh_viewer.mesh is not None:
                        self.batch_status_text.append(f"  Exporting CSV data...")
                        QApplication.processEvents()

                        csv_path = os.path.join(output_subdir, f"{file_base}_deformation.csv")
                        self._export_batch_csv(csv_path)

                    # Save session if requested
                    if self.batch_save_sessions.isChecked():
                        self.batch_status_text.append(f"  Saving session...")
                        QApplication.processEvents()

                        session_path = os.path.join(output_subdir, f"{file_base}_session.json")
                        self._save_session_to_file(session_path, silent=True)

                    successful_count += 1
                    self.batch_status_text.append(f"  ✅ Completed successfully")

                except Exception as e:
                    failed_count += 1
                    self.batch_status_text.append(f"  ❌ Failed: {e}")

                QApplication.processEvents()

            # Final progress update
            self.batch_progress_bar.setValue(file_count)

            # Summary
            self.batch_status_text.append(f"\n🎉 Batch processing completed!")
            self.batch_status_text.append(f"✅ Successful: {successful_count}")
            self.batch_status_text.append(f"❌ Failed: {failed_count}")
            self.batch_status_text.append(f"📁 Output directory: {self.batch_output_directory}")

            # Stop processing
            self._stop_batch_processing()

        except Exception as e:
            self.batch_status_text.append(f"❌ Batch processing error: {e}")
            self._stop_batch_processing()

    def _export_batch_csv(self, file_path):
        """Export CSV data for batch processing"""
        try:
            mesh = self.mesh_viewer.mesh
            if mesh is None:
                return

            # Get deformation data
            if 'DeformationMasked' in mesh.point_data:
                deformation_data = mesh.point_data['DeformationMasked']
            elif 'Deformation' in mesh.point_data:
                deformation_data = mesh.point_data['Deformation']
            else:
                return

            # Export to CSV
            import csv
            with open(file_path, 'w', newline='') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['X', 'Y', 'Z', 'Deformation'])

                points = mesh.points
                for i, point in enumerate(points):
                    deformation = deformation_data[i] if i < len(deformation_data) else 0
                    writer.writerow([point[0], point[1], point[2], deformation])

        except Exception as e:
            print(f"Failed to export batch CSV: {e}")

    def show_measurement_tools(self):
        """Show measurement tools dialog"""
        try:
            from src.gui.measurement_tools import MeasurementToolsDialog

            dialog = MeasurementToolsDialog(self.mesh_viewer, self)
            dialog.measurement_added.connect(self._on_measurement_added)
            dialog.show()  # Non-modal to allow interaction with 3D view

        except Exception as e:
            QMessageBox.critical(self, "Measurement Tools Error", f"Failed to show measurement tools: {e}")

    def show_quality_control(self):
        """Show quality control dialog"""
        try:
            from src.gui.quality_control import QualityControlDialog

            dialog = QualityControlDialog(self.mesh_viewer, self)
            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "Quality Control Error", f"Failed to show quality control: {e}")

    def _on_measurement_added(self, measurement):
        """Handle new measurement added"""
        try:
            # Update status
            self.show_enhanced_status_message(
                f"Measurement added: {measurement['description']}", 3000, "success")

            # Could add measurement annotations to 3D view here
            # For now, just log the measurement
            print(f"New measurement: {measurement}")

        except Exception as e:
            print(f"Failed to handle measurement: {e}")

    def _on_annotation_added(self, annotation_id):
        """Handle new annotation added"""
        try:
            # Update status
            self.show_enhanced_status_message(
                f"Annotation added: {annotation_id}", 3000, "success")

            # Log the annotation
            print(f"New annotation: {annotation_id}")

        except Exception as e:
            print(f"Failed to handle annotation: {e}")

    def show_advanced_reporting(self):
        """Show advanced reporting dialog"""
        try:
            from src.gui.advanced_reporting import AdvancedReportingDialog

            dialog = AdvancedReportingDialog(self.mesh_viewer, self)
            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "Advanced Reporting Error", f"Failed to show advanced reporting: {e}")

    def export_analysis_results(self):
        """Export comprehensive analysis results"""
        try:
            if not self.mesh_viewer.mesh:
                QMessageBox.warning(self, "No Mesh", "Please load a mesh first.")
                return

            # Show export options dialog
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QPushButton, QLabel, QGroupBox

            dialog = QDialog(self)
            dialog.setWindowTitle("Export Analysis Results")
            dialog.setModal(True)
            dialog.resize(400, 300)

            layout = QVBoxLayout(dialog)

            # Export options
            options_group = QGroupBox("Export Options")
            options_layout = QVBoxLayout(options_group)

            csv_check = QCheckBox("CSV Data (coordinates + analysis)")
            csv_check.setChecked(True)
            options_layout.addWidget(csv_check)

            json_check = QCheckBox("JSON Metadata (mesh info + statistics)")
            json_check.setChecked(True)
            options_layout.addWidget(json_check)

            screenshot_check = QCheckBox("High-resolution Screenshot")
            screenshot_check.setChecked(True)
            options_layout.addWidget(screenshot_check)

            mesh_check = QCheckBox("Processed Mesh (PLY format)")
            mesh_check.setChecked(False)
            options_layout.addWidget(mesh_check)

            layout.addWidget(options_group)

            # Buttons
            button_layout = QHBoxLayout()
            export_btn = QPushButton("Export")
            cancel_btn = QPushButton("Cancel")

            button_layout.addWidget(export_btn)
            button_layout.addWidget(cancel_btn)
            layout.addLayout(button_layout)

            # Connect buttons
            cancel_btn.clicked.connect(dialog.reject)

            def do_export():
                try:
                    # Get base filename
                    file_path, _ = QFileDialog.getSaveFileName(
                        self, "Export Analysis Results", "analysis_results",
                        "All Files (*)")

                    if not file_path:
                        dialog.reject()
                        return

                    base_path = file_path.rsplit('.', 1)[0]  # Remove extension
                    exported_files = []

                    mesh = self.mesh_viewer.mesh

                    # Export CSV data
                    if csv_check.isChecked():
                        csv_path = f"{base_path}.csv"
                        self._export_csv_data(mesh, csv_path)
                        exported_files.append(csv_path)

                    # Export JSON metadata
                    if json_check.isChecked():
                        json_path = f"{base_path}_metadata.json"
                        self._export_json_metadata(mesh, json_path)
                        exported_files.append(json_path)

                    # Export screenshot
                    if screenshot_check.isChecked():
                        img_path = f"{base_path}_screenshot.png"
                        self._export_screenshot(img_path)
                        exported_files.append(img_path)

                    # Export mesh
                    if mesh_check.isChecked():
                        mesh_path = f"{base_path}_mesh.ply"
                        mesh.save(mesh_path)
                        exported_files.append(mesh_path)

                    # Show success message
                    files_list = '\n'.join([f"• {os.path.basename(f)}" for f in exported_files])
                    QMessageBox.information(self, "Export Complete",
                        f"Analysis results exported successfully:\n\n{files_list}")

                    dialog.accept()

                except Exception as e:
                    QMessageBox.critical(self, "Export Error", f"Failed to export results: {e}")

            export_btn.clicked.connect(do_export)

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Failed to show export dialog: {e}")

    def _export_csv_data(self, mesh, file_path):
        """Export mesh data to CSV"""
        import csv

        with open(file_path, 'w', newline='') as f:
            writer = csv.writer(f)

            # Header
            headers = ['Point_Index', 'X', 'Y', 'Z']
            for array_name in mesh.point_data.keys():
                headers.append(array_name)
            writer.writerow(headers)

            # Data
            for i in range(mesh.n_points):
                row = [i] + list(mesh.points[i])
                for array_name in mesh.point_data.keys():
                    row.append(mesh.point_data[array_name][i])
                writer.writerow(row)

    def _export_json_metadata(self, mesh, file_path):
        """Export mesh metadata to JSON"""
        import json
        from datetime import datetime

        metadata = {
            'export_info': {
                'software': 'DeformViz 3D v2.3',
                'timestamp': datetime.now().isoformat(),
                'user': os.getenv('USER', 'Unknown')
            },
            'mesh_info': {
                'n_points': int(mesh.n_points),
                'n_cells': int(mesh.n_cells),
                'bounds': list(mesh.bounds),
                'area': float(mesh.area) if hasattr(mesh, 'area') else None,
                'volume': float(mesh.volume) if hasattr(mesh, 'volume') else None
            },
            'point_data_arrays': list(mesh.point_data.keys()),
            'cell_data_arrays': list(mesh.cell_data.keys()) if mesh.cell_data else []
        }

        # Add statistics for numerical arrays
        for array_name, array_data in mesh.point_data.items():
            if array_data.dtype.kind in 'biufc':  # numeric types
                metadata[f'{array_name}_stats'] = {
                    'mean': float(np.mean(array_data)),
                    'std': float(np.std(array_data)),
                    'min': float(np.min(array_data)),
                    'max': float(np.max(array_data)),
                    'count': int(len(array_data))
                }

        with open(file_path, 'w') as f:
            json.dump(metadata, f, indent=2)

    def _export_screenshot(self, file_path):
        """Export high-resolution screenshot"""
        try:
            img = self.mesh_viewer.plotter.screenshot(return_img=True, scale=3)

            try:
                import imageio
                imageio.imwrite(file_path, img)
            except ImportError:
                # Fallback to PIL
                from PIL import Image
                Image.fromarray(img).save(file_path)

        except Exception as e:
            raise Exception(f"Screenshot export failed: {e}")

def run_app():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
