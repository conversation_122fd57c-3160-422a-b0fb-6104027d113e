"""
Advanced Reporting System for DeformViz 3D
Generates comprehensive reports in multiple formats with images, analysis, and data
"""

import os
import sys
import json
import csv
from datetime import datetime
from pathlib import Path
import numpy as np
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QGroupBox, QFormLayout, QLineEdit,
                              QTextEdit, QTabWidget, QWidget, QCheckBox,
                              QComboBox, QProgressBar, QFileDialog, QMessageBox,
                              QSpinBox, QDoubleSpinBox, QListWidget, QListWidgetItem,
                              QTableWidget, QTableWidgetItem, QHeaderView)
from PySide6.QtCore import Qt, Signal, QThread, QTimer
from PySide6.QtGui import QPixmap, QFont


class ReportGenerationThread(QThread):
    """Background thread for report generation"""
    
    progress_updated = Signal(int, str)
    report_completed = Signal(str, bool)
    
    def __init__(self, report_config, mesh_viewer, parent=None):
        super().__init__(parent)
        self.report_config = report_config
        self.mesh_viewer = mesh_viewer
        self.cancelled = False
    
    def run(self):
        """Generate the report in background"""
        try:
            self.progress_updated.emit(10, "Initializing report generation...")
            
            # Create report generator
            generator = ReportGenerator(self.mesh_viewer)
            
            self.progress_updated.emit(20, "Collecting analysis data...")
            
            # Generate report based on format
            format_type = self.report_config['format'].lower()
            output_path = self.report_config['output_path']
            
            if format_type == 'pdf':
                success = generator.generate_pdf_report(self.report_config, self.progress_callback)
            elif format_type == 'html':
                success = generator.generate_html_report(self.report_config, self.progress_callback)
            elif format_type == 'excel':
                success = generator.generate_excel_report(self.report_config, self.progress_callback)
            elif format_type == 'word':
                success = generator.generate_word_report(self.report_config, self.progress_callback)
            elif format_type == 'powerpoint':
                success = generator.generate_powerpoint_report(self.report_config, self.progress_callback)
            else:
                success = False
            
            if not self.cancelled:
                self.report_completed.emit(output_path, success)
                
        except Exception as e:
            if not self.cancelled:
                self.report_completed.emit(str(e), False)
    
    def progress_callback(self, progress, message):
        """Callback for progress updates"""
        if not self.cancelled:
            self.progress_updated.emit(progress, message)
    
    def cancel(self):
        """Cancel report generation"""
        self.cancelled = True


class AdvancedReportingDialog(QDialog):
    """Advanced reporting dialog with comprehensive options"""
    
    def __init__(self, mesh_viewer, parent=None):
        super().__init__(parent)
        self.mesh_viewer = mesh_viewer
        self.report_thread = None
        
        self.setWindowTitle("Advanced Reporting System - DeformViz 3D")
        self.setModal(True)
        self.resize(800, 700)
        
        self._setup_ui()
        self._load_templates()
    
    def _setup_ui(self):
        """Setup the reporting interface"""
        layout = QVBoxLayout(self)
        
        # Title
        title = QLabel("📊 Advanced Reporting System")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Create tabbed interface
        tabs = QTabWidget()
        
        # Tab 1: Report Configuration
        config_tab = self._create_config_tab()
        tabs.addTab(config_tab, "📋 Configuration")
        
        # Tab 2: Content Selection
        content_tab = self._create_content_tab()
        tabs.addTab(content_tab, "📄 Content")
        
        # Tab 3: Formatting Options
        format_tab = self._create_format_tab()
        tabs.addTab(format_tab, "🎨 Formatting")
        
        # Tab 4: Templates
        template_tab = self._create_template_tab()
        tabs.addTab(template_tab, "📝 Templates")
        
        layout.addWidget(tabs)
        
        # Progress section
        progress_group = QGroupBox("Generation Progress")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_label = QLabel("Ready to generate report")
        
        progress_layout.addWidget(self.progress_label)
        progress_layout.addWidget(self.progress_bar)
        
        layout.addWidget(progress_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.preview_btn = QPushButton("👁️ Preview")
        self.preview_btn.clicked.connect(self._preview_report)
        
        self.generate_btn = QPushButton("🚀 Generate Report")
        self.generate_btn.clicked.connect(self._generate_report)
        
        self.cancel_btn = QPushButton("❌ Cancel")
        self.cancel_btn.clicked.connect(self._cancel_generation)
        self.cancel_btn.setEnabled(False)
        
        close_btn = QPushButton("Close")
        close_btn.clicked.connect(self.accept)
        
        button_layout.addWidget(self.preview_btn)
        button_layout.addWidget(self.generate_btn)
        button_layout.addWidget(self.cancel_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
    
    def _create_config_tab(self):
        """Create report configuration tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Basic settings
        basic_group = QGroupBox("Basic Report Settings")
        basic_layout = QFormLayout(basic_group)
        
        self.report_title = QLineEdit("DeformViz 3D Analysis Report")
        basic_layout.addRow("Report Title:", self.report_title)
        
        self.author_name = QLineEdit("DeformViz 3D User")
        basic_layout.addRow("Author:", self.author_name)
        
        self.company_name = QLineEdit("")
        basic_layout.addRow("Company/Organization:", self.company_name)
        
        self.report_format = QComboBox()
        self.report_format.addItems(["PDF", "HTML", "Excel", "Word", "PowerPoint"])
        basic_layout.addRow("Output Format:", self.report_format)
        
        layout.addWidget(basic_group)
        
        # Output settings
        output_group = QGroupBox("Output Settings")
        output_layout = QVBoxLayout(output_group)
        
        file_layout = QHBoxLayout()
        self.output_path = QLineEdit()
        browse_btn = QPushButton("Browse...")
        browse_btn.clicked.connect(self._browse_output_path)
        
        file_layout.addWidget(QLabel("Output File:"))
        file_layout.addWidget(self.output_path)
        file_layout.addWidget(browse_btn)
        
        output_layout.addLayout(file_layout)
        
        # Quality settings
        quality_layout = QFormLayout()
        
        self.image_quality = QComboBox()
        self.image_quality.addItems(["High (300 DPI)", "Medium (150 DPI)", "Low (72 DPI)"])
        quality_layout.addRow("Image Quality:", self.image_quality)
        
        self.include_metadata = QCheckBox("Include technical metadata")
        self.include_metadata.setChecked(True)
        quality_layout.addRow("Metadata:", self.include_metadata)
        
        output_layout.addLayout(quality_layout)
        layout.addWidget(output_group)
        
        layout.addStretch()
        return tab
    
    def _create_content_tab(self):
        """Create content selection tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Analysis content
        analysis_group = QGroupBox("Analysis Content")
        analysis_layout = QVBoxLayout(analysis_group)
        
        self.include_mesh_info = QCheckBox("Mesh Information (points, cells, bounds)")
        self.include_mesh_info.setChecked(True)
        analysis_layout.addWidget(self.include_mesh_info)
        
        self.include_deformation = QCheckBox("Deformation Analysis Results")
        self.include_deformation.setChecked(True)
        analysis_layout.addWidget(self.include_deformation)
        
        self.include_measurements = QCheckBox("Measurement Results")
        self.include_measurements.setChecked(True)
        analysis_layout.addWidget(self.include_measurements)
        
        self.include_quality_control = QCheckBox("Quality Control Analysis")
        self.include_quality_control.setChecked(True)
        analysis_layout.addWidget(self.include_quality_control)
        
        self.include_statistics = QCheckBox("Statistical Analysis")
        self.include_statistics.setChecked(True)
        analysis_layout.addWidget(self.include_statistics)
        
        layout.addWidget(analysis_group)
        
        # Visual content
        visual_group = QGroupBox("Visual Content")
        visual_layout = QVBoxLayout(visual_group)
        
        self.include_screenshots = QCheckBox("3D Visualization Screenshots")
        self.include_screenshots.setChecked(True)
        visual_layout.addWidget(self.include_screenshots)
        
        self.include_heatmaps = QCheckBox("Deformation Heatmaps")
        self.include_heatmaps.setChecked(True)
        visual_layout.addWidget(self.include_heatmaps)
        
        self.include_charts = QCheckBox("Statistical Charts and Graphs")
        self.include_charts.setChecked(True)
        visual_layout.addWidget(self.include_charts)
        
        self.include_histograms = QCheckBox("Data Distribution Histograms")
        self.include_histograms.setChecked(True)
        visual_layout.addWidget(self.include_histograms)
        
        layout.addWidget(visual_group)
        
        # Data tables
        data_group = QGroupBox("Data Tables")
        data_layout = QVBoxLayout(data_group)
        
        self.include_raw_data = QCheckBox("Raw Measurement Data")
        self.include_raw_data.setChecked(False)
        data_layout.addWidget(self.include_raw_data)
        
        self.include_summary_tables = QCheckBox("Summary Statistics Tables")
        self.include_summary_tables.setChecked(True)
        data_layout.addWidget(self.include_summary_tables)
        
        self.include_tolerance_tables = QCheckBox("Tolerance Check Results")
        self.include_tolerance_tables.setChecked(True)
        data_layout.addWidget(self.include_tolerance_tables)
        
        layout.addWidget(data_group)
        
        layout.addStretch()
        return tab
    
    def _create_format_tab(self):
        """Create formatting options tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Page layout
        page_group = QGroupBox("Page Layout")
        page_layout = QFormLayout(page_group)
        
        self.page_size = QComboBox()
        self.page_size.addItems(["A4", "Letter", "A3", "Legal"])
        page_layout.addRow("Page Size:", self.page_size)
        
        self.page_orientation = QComboBox()
        self.page_orientation.addItems(["Portrait", "Landscape"])
        page_layout.addRow("Orientation:", self.page_orientation)
        
        layout.addWidget(page_group)
        
        # Styling
        style_group = QGroupBox("Styling Options")
        style_layout = QFormLayout(style_group)
        
        self.color_scheme = QComboBox()
        self.color_scheme.addItems(["Professional Blue", "Scientific Green", "Engineering Orange", "Monochrome"])
        style_layout.addRow("Color Scheme:", self.color_scheme)
        
        self.font_size = QSpinBox()
        self.font_size.setRange(8, 16)
        self.font_size.setValue(11)
        style_layout.addRow("Font Size:", self.font_size)
        
        self.include_logo = QCheckBox("Include company logo")
        style_layout.addRow("Branding:", self.include_logo)
        
        layout.addWidget(style_group)
        
        # Charts and graphs
        chart_group = QGroupBox("Charts and Graphs")
        chart_layout = QVBoxLayout(chart_group)
        
        self.chart_style = QComboBox()
        self.chart_style.addItems(["Modern", "Classic", "Scientific", "Minimal"])
        chart_layout.addWidget(QLabel("Chart Style:"))
        chart_layout.addWidget(self.chart_style)
        
        self.include_3d_charts = QCheckBox("Generate 3D charts where applicable")
        chart_layout.addWidget(self.include_3d_charts)
        
        layout.addWidget(chart_group)
        
        layout.addStretch()
        return tab
    
    def _create_template_tab(self):
        """Create template selection tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Template selection
        template_group = QGroupBox("Report Templates")
        template_layout = QVBoxLayout(template_group)
        
        self.template_list = QListWidget()
        template_layout.addWidget(self.template_list)
        
        template_buttons = QHBoxLayout()
        
        load_template_btn = QPushButton("Load Template")
        load_template_btn.clicked.connect(self._load_template)
        
        save_template_btn = QPushButton("Save as Template")
        save_template_btn.clicked.connect(self._save_template)
        
        template_buttons.addWidget(load_template_btn)
        template_buttons.addWidget(save_template_btn)
        template_buttons.addStretch()
        
        template_layout.addLayout(template_buttons)
        layout.addWidget(template_group)
        
        # Template preview
        preview_group = QGroupBox("Template Preview")
        preview_layout = QVBoxLayout(preview_group)
        
        self.template_preview = QTextEdit()
        self.template_preview.setReadOnly(True)
        self.template_preview.setMaximumHeight(200)
        preview_layout.addWidget(self.template_preview)
        
        layout.addWidget(preview_group)
        
        layout.addStretch()
        return tab

    def _load_templates(self):
        """Load available report templates"""
        templates = [
            "📊 Standard Analysis Report",
            "🔬 Scientific Research Report",
            "🏭 Quality Control Report",
            "📈 Executive Summary",
            "🎓 Educational Report",
            "💼 Client Presentation"
        ]

        for template in templates:
            item = QListWidgetItem(template)
            self.template_list.addItem(item)

    def _browse_output_path(self):
        """Browse for output file path"""
        format_type = self.report_format.currentText().lower()

        filters = {
            'pdf': "PDF Files (*.pdf)",
            'html': "HTML Files (*.html)",
            'excel': "Excel Files (*.xlsx)",
            'word': "Word Documents (*.docx)",
            'powerpoint': "PowerPoint Files (*.pptx)"
        }

        file_filter = filters.get(format_type, "All Files (*)")

        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Report As", "", file_filter)

        if file_path:
            self.output_path.setText(file_path)

    def _preview_report(self):
        """Preview the report before generation"""
        try:
            config = self._get_report_config()

            # Create preview dialog
            preview_dialog = ReportPreviewDialog(config, self.mesh_viewer, self)
            preview_dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "Preview Error", f"Failed to generate preview: {e}")

    def _generate_report(self):
        """Generate the report"""
        try:
            config = self._get_report_config()

            if not config['output_path']:
                QMessageBox.warning(self, "No Output Path", "Please specify an output file path.")
                return

            # Disable generate button and enable cancel
            self.generate_btn.setEnabled(False)
            self.cancel_btn.setEnabled(True)

            # Start report generation thread
            self.report_thread = ReportGenerationThread(config, self.mesh_viewer, self)
            self.report_thread.progress_updated.connect(self._update_progress)
            self.report_thread.report_completed.connect(self._report_finished)
            self.report_thread.start()

        except Exception as e:
            QMessageBox.critical(self, "Generation Error", f"Failed to start report generation: {e}")
            self._reset_buttons()

    def _cancel_generation(self):
        """Cancel report generation"""
        if self.report_thread:
            self.report_thread.cancel()
            self.progress_label.setText("Cancelling...")

    def _update_progress(self, progress, message):
        """Update progress bar and message"""
        self.progress_bar.setValue(progress)
        self.progress_label.setText(message)

    def _report_finished(self, result, success):
        """Handle report generation completion"""
        self._reset_buttons()

        if success:
            QMessageBox.information(self, "Report Generated",
                f"Report successfully generated:\n{result}")
            self.progress_label.setText("Report generation completed")
            self.progress_bar.setValue(100)
        else:
            QMessageBox.critical(self, "Generation Failed",
                f"Report generation failed:\n{result}")
            self.progress_label.setText("Report generation failed")
            self.progress_bar.setValue(0)

    def _reset_buttons(self):
        """Reset button states"""
        self.generate_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)

    def _get_report_config(self):
        """Get current report configuration"""
        return {
            'title': self.report_title.text(),
            'author': self.author_name.text(),
            'company': self.company_name.text(),
            'format': self.report_format.currentText(),
            'output_path': self.output_path.text(),
            'image_quality': self.image_quality.currentText(),
            'include_metadata': self.include_metadata.isChecked(),
            'include_mesh_info': self.include_mesh_info.isChecked(),
            'include_deformation': self.include_deformation.isChecked(),
            'include_measurements': self.include_measurements.isChecked(),
            'include_quality_control': self.include_quality_control.isChecked(),
            'include_statistics': self.include_statistics.isChecked(),
            'include_screenshots': self.include_screenshots.isChecked(),
            'include_heatmaps': self.include_heatmaps.isChecked(),
            'include_charts': self.include_charts.isChecked(),
            'include_histograms': self.include_histograms.isChecked(),
            'include_raw_data': self.include_raw_data.isChecked(),
            'include_summary_tables': self.include_summary_tables.isChecked(),
            'include_tolerance_tables': self.include_tolerance_tables.isChecked(),
            'page_size': self.page_size.currentText(),
            'page_orientation': self.page_orientation.currentText(),
            'color_scheme': self.color_scheme.currentText(),
            'font_size': self.font_size.value(),
            'include_logo': self.include_logo.isChecked(),
            'chart_style': self.chart_style.currentText(),
            'include_3d_charts': self.include_3d_charts.isChecked()
        }

    def _load_template(self):
        """Load selected template"""
        current_item = self.template_list.currentItem()
        if current_item:
            template_name = current_item.text()
            self.template_preview.setText(f"Loading template: {template_name}")
            # TODO: Implement template loading logic

    def _save_template(self):
        """Save current settings as template"""
        # TODO: Implement template saving logic
        QMessageBox.information(self, "Template Saved", "Current settings saved as template.")


class ReportPreviewDialog(QDialog):
    """Dialog for previewing report before generation"""

    def __init__(self, config, mesh_viewer, parent=None):
        super().__init__(parent)
        self.config = config
        self.mesh_viewer = mesh_viewer

        self.setWindowTitle("Report Preview")
        self.setModal(True)
        self.resize(600, 500)

        layout = QVBoxLayout(self)

        # Preview text
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        layout.addWidget(self.preview_text)

        # Generate preview content
        self._generate_preview()

        # Close button
        close_btn = QPushButton("Close")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)

    def _generate_preview(self):
        """Generate preview content"""
        preview_html = f"""
        <h1>{self.config['title']}</h1>
        <p><strong>Author:</strong> {self.config['author']}</p>
        <p><strong>Company:</strong> {self.config['company']}</p>
        <p><strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>

        <h2>Report Contents</h2>
        <ul>
        """

        if self.config['include_mesh_info']:
            preview_html += "<li>✅ Mesh Information</li>"
        if self.config['include_deformation']:
            preview_html += "<li>✅ Deformation Analysis</li>"
        if self.config['include_measurements']:
            preview_html += "<li>✅ Measurement Results</li>"
        if self.config['include_quality_control']:
            preview_html += "<li>✅ Quality Control Analysis</li>"
        if self.config['include_statistics']:
            preview_html += "<li>✅ Statistical Analysis</li>"
        if self.config['include_screenshots']:
            preview_html += "<li>✅ 3D Visualization Screenshots</li>"
        if self.config['include_charts']:
            preview_html += "<li>✅ Statistical Charts</li>"

        preview_html += """
        </ul>

        <h2>Format Settings</h2>
        <ul>
        <li><strong>Output Format:</strong> {format}</li>
        <li><strong>Page Size:</strong> {page_size}</li>
        <li><strong>Orientation:</strong> {page_orientation}</li>
        <li><strong>Color Scheme:</strong> {color_scheme}</li>
        </ul>
        """.format(**self.config)

        self.preview_text.setHtml(preview_html)


class ReportGenerator:
    """Report generation engine - import from core module"""

    def __init__(self, mesh_viewer):
        # Import the actual generator
        try:
            from src.core.report_generator import ReportGenerator as CoreGenerator
            self.generator = CoreGenerator(mesh_viewer)
        except ImportError:
            self.generator = None

    def generate_pdf_report(self, config, progress_callback=None):
        """Generate PDF report"""
        if self.generator:
            return self.generator.generate_pdf_report(config, progress_callback)
        else:
            # Fallback implementation
            return self._generate_simple_report(config, 'pdf', progress_callback)

    def generate_html_report(self, config, progress_callback=None):
        """Generate HTML report"""
        if self.generator:
            return self.generator.generate_html_report(config, progress_callback)
        else:
            return self._generate_simple_report(config, 'html', progress_callback)

    def generate_excel_report(self, config, progress_callback=None):
        """Generate Excel report"""
        if self.generator:
            return self.generator.generate_excel_report(config, progress_callback)
        else:
            return self._generate_simple_report(config, 'excel', progress_callback)

    def generate_word_report(self, config, progress_callback=None):
        """Generate Word report"""
        if self.generator:
            return self.generator.generate_word_report(config, progress_callback)
        else:
            return self._generate_simple_report(config, 'word', progress_callback)

    def generate_powerpoint_report(self, config, progress_callback=None):
        """Generate PowerPoint report"""
        if self.generator:
            return self.generator.generate_powerpoint_report(config, progress_callback)
        else:
            return self._generate_simple_report(config, 'powerpoint', progress_callback)

    def _generate_simple_report(self, config, format_type, progress_callback=None):
        """Simple fallback report generation"""
        try:
            if progress_callback:
                progress_callback(50, f"Generating simple {format_type} report...")

            # Create basic report content
            content = f"""
{config['title']}
Author: {config['author']}
Company: {config['company']}
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

This is a basic report generated by DeformViz 3D.
For full-featured reports, please install the required dependencies.

Report includes:
- Mesh information: {'Yes' if config['include_mesh_info'] else 'No'}
- Deformation analysis: {'Yes' if config['include_deformation'] else 'No'}
- Measurements: {'Yes' if config['include_measurements'] else 'No'}
- Quality control: {'Yes' if config['include_quality_control'] else 'No'}
- Statistics: {'Yes' if config['include_statistics'] else 'No'}
"""

            # Save as text file with appropriate extension
            output_path = config['output_path']
            if not output_path.endswith(f'.{format_type}'):
                output_path = output_path + f'.{format_type}'

            with open(output_path, 'w') as f:
                f.write(content)

            if progress_callback:
                progress_callback(100, f"Simple {format_type} report completed")

            return True

        except Exception as e:
            print(f"Simple report generation error: {e}")
            return False
