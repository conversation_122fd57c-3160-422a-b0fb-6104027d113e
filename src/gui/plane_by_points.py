from PySide6.QtWidgets import (QDialog, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QSlider, QDoubleSpinBox, QGroupBox, QFormLayout, QMessageBox, QRadioButton, QTabWidget, QWidget, QButtonGroup, QComboBox)
from PySide6.QtCore import Qt
import numpy as np
import pyvista as pv
from scipy.spatial.transform import Rotation as R
from ..core import mesh_utils

class PlaneByPointsDialog(QDialog):
    def __init__(self, mesh_viewer, parent=None, outside_opacity=0.18):
        super().__init__(parent)
        self.setWindowTitle("Define Analysis Plane (Wizard)")
        self.setMinimumWidth(400)

        # Get mesh and ensure it's PolyData
        mesh = getattr(mesh_viewer, 'mesh', None)
        if mesh is not None:
            try:
                mesh = mesh_utils.ensure_polydata(mesh)
                mesh_viewer.mesh = mesh
            except Exception as e:
                QMessageBox.warning(self, "Mesh Type Error", f"Mesh must be PolyData for processing. Conversion failed: {e}")

        self.mesh_viewer = mesh_viewer
        self.outside_opacity = outside_opacity
        self._previous_tab_idx = 0  # Initialize tab tracking
        self.reset_all()  # Reset all state and previews on dialog open
        self._setup_wizard_ui()
        self._connect_signals()
        self._update_nav_buttons()
        self._go_to_step(0)

    def reset_all(self):
        # Clear all state and remove any previews/plane definitions
        self.picked_points = []
        self.plane_params = None
        self.slab_thickness = None

        # Disable any active picking
        try:
            self.mesh_viewer.plotter.disable_picking()
        except Exception:
            pass

        # Mesh is already guaranteed PolyData with triangles from __init__
        plotter = getattr(self.mesh_viewer, 'plotter', None)
        for attr in ['_plane_preview_actor', '_point_preview_actor', '_slab_preview_actor', '_arrow_preview_actor']:
            if hasattr(self, attr):
                actor = getattr(self, attr)
                if actor and plotter:
                    try:
                        plotter.remove_actor(actor)
                    except Exception:
                        pass
                setattr(self, attr, None)
        if plotter:
            plotter.render()
        # Optionally, clear any analysis plane in mesh_viewer
        if hasattr(self.mesh_viewer, 'clear_analysis_plane'):
            self.mesh_viewer.clear_analysis_plane()

    def _setup_wizard_ui(self):
        layout = QVBoxLayout(self)
        self.tabs = QTabWidget()
        self.tabs.setTabBarAutoHide(True)
        self.tabs.setTabPosition(QTabWidget.North)
        self.tabs.setMovable(False)
        self.tabs.setDocumentMode(True)
        self.tabs.setUsesScrollButtons(False)
        # --- Step 1: Plane type selection ---
        self.tab_type = QWidget()
        type_layout = QVBoxLayout(self.tab_type)
        type_layout.addWidget(QLabel("Step 1: Select plane definition method."))
        self.rb_by3 = QRadioButton("By 3 points (pick on mesh)")
        self.rb_canonical = QRadioButton("Canonical plane (XY, YZ, ZX)")
        self.rb_by3.setChecked(True)
        self.rb_type_group = QButtonGroup(self)
        self.rb_type_group.addButton(self.rb_by3)
        self.rb_type_group.addButton(self.rb_canonical)
        type_layout.addWidget(self.rb_by3)
        type_layout.addWidget(self.rb_canonical)
        type_layout.addStretch(1)
        self.tab_type.setLayout(type_layout)
        self.tabs.addTab(self.tab_type, "Plane Type")
        # --- Step 2: Point picking ---
        self.tab_pick = QWidget()
        pick_layout = QVBoxLayout(self.tab_pick)
        pick_layout.addWidget(QLabel("Step 2: Pick 3 points on the mesh to define the plane."))
        self.info_label = QLabel("Points picked: 0/3")
        pick_layout.addWidget(self.info_label)
        self.btn_undo = QPushButton("Undo Last Point")
        self.btn_reset = QPushButton("Reset Points")
        btns = QHBoxLayout()
        btns.addWidget(self.btn_undo)
        btns.addWidget(self.btn_reset)
        pick_layout.addLayout(btns)
        pick_layout.addStretch(1)
        self.tab_pick.setLayout(pick_layout)
        self.tabs.addTab(self.tab_pick, "Pick Points")
        # --- Step 3: Slab/alignment/transform options ---
        self.tab_opts = QWidget()
        opts_layout = QVBoxLayout(self.tab_opts)
        # Slab thickness
        self.slab_group = QGroupBox("Slab Thickness")
        slab_layout = QFormLayout(self.slab_group)
        self.slab_slider = QSlider(Qt.Horizontal)
        self.slab_slider.setRange(1, 200)
        self.slab_slider.setValue(20)
        self.slab_spin = QDoubleSpinBox()
        self.slab_spin.setRange(0.01, 10.0)
        self.slab_spin.setDecimals(3)
        self.slab_spin.setValue(0.20)
        self.slab_spin.setSuffix(" m")
        slab_layout.addRow("Thickness:", self.slab_spin)
        slab_layout.addRow("(Quick adjust):", self.slab_slider)
        opts_layout.addWidget(self.slab_group)
        # Plane alignment - make plane cut through canonical planes
        self.align_group = QGroupBox("Make Plane Cut Through")
        align_layout = QHBoxLayout()
        self.align_keep = QRadioButton("Keep as is")
        self.align_x = QRadioButton("YZ plane (normal in YZ)")
        self.align_y = QRadioButton("ZX plane (normal in ZX)")
        self.align_z = QRadioButton("XY plane (normal in XY)")
        self.align_keep.setChecked(True)
        align_layout.addWidget(self.align_keep)
        align_layout.addWidget(self.align_x)
        align_layout.addWidget(self.align_y)
        align_layout.addWidget(self.align_z)
        self.align_group.setLayout(align_layout)
        opts_layout.addWidget(self.align_group)
        # Slab position
        self.slabpos_group = QGroupBox("Slab Position")
        slabpos_layout = QHBoxLayout()
        self.slab_centered = QRadioButton("Centered on plane")
        self.slab_positive = QRadioButton("Positive side")
        self.slab_negative = QRadioButton("Negative side")
        self.slab_centered.setChecked(True)
        slabpos_layout.addWidget(self.slab_centered)
        slabpos_layout.addWidget(self.slab_positive)
        slabpos_layout.addWidget(self.slab_negative)
        self.slabpos_group.setLayout(slabpos_layout)
        opts_layout.addWidget(self.slabpos_group)
        # Transform controls
        self.transform_group = QGroupBox("Plane Transform")
        transform_layout = QFormLayout(self.transform_group)
        self.trans_x = QDoubleSpinBox(); self.trans_x.setRange(-1000, 1000); self.trans_x.setDecimals(3)
        self.trans_y = QDoubleSpinBox(); self.trans_y.setRange(-1000, 1000); self.trans_y.setDecimals(3)
        self.trans_z = QDoubleSpinBox(); self.trans_z.setRange(-1000, 1000); self.trans_z.setDecimals(3)
        transform_layout.addRow("Translate X:", self.trans_x)
        transform_layout.addRow("Translate Y:", self.trans_y)
        transform_layout.addRow("Translate Z:", self.trans_z)
        self.rot_x = QDoubleSpinBox(); self.rot_x.setRange(-180, 180); self.rot_x.setDecimals(2)
        self.rot_y = QDoubleSpinBox(); self.rot_y.setRange(-180, 180); self.rot_y.setDecimals(2)
        self.rot_z = QDoubleSpinBox(); self.rot_z.setRange(-180, 180); self.rot_z.setDecimals(2)
        transform_layout.addRow("Rotate X (deg):", self.rot_x)
        transform_layout.addRow("Rotate Y (deg):", self.rot_y)
        transform_layout.addRow("Rotate Z (deg):", self.rot_z)
        opts_layout.addWidget(self.transform_group)
        opts_layout.addStretch(1)
        self.tab_opts.setLayout(opts_layout)
        self.tabs.addTab(self.tab_opts, "Options")
        # --- Step 4: Summary and apply ---
        self.tab_summary = QWidget()
        summary_layout = QVBoxLayout(self.tab_summary)
        self.summary_label = QLabel()
        summary_layout.addWidget(QLabel("Step 4: Review and apply plane definition."))
        summary_layout.addWidget(self.summary_label)
        summary_layout.addStretch(1)
        self.btn_apply = QPushButton("Apply and Close")
        summary_layout.addWidget(self.btn_apply)
        self.tab_summary.setLayout(summary_layout)
        self.tabs.addTab(self.tab_summary, "Summary")
        layout.addWidget(self.tabs)
        # --- Navigation buttons ---
        nav = QHBoxLayout()
        self.btn_prev = QPushButton("< Previous")
        self.btn_next = QPushButton("Next >")
        self.btn_back_to_start = QPushButton("Back to Start")
        nav.addWidget(self.btn_prev)
        nav.addWidget(self.btn_next)
        nav.addWidget(self.btn_back_to_start)
        layout.addLayout(nav)

    def _connect_signals(self):
        self.btn_prev.clicked.connect(self._on_prev)
        self.btn_next.clicked.connect(self._on_next)
        self.btn_back_to_start.clicked.connect(lambda: self._go_to_step(0))
        self.btn_undo.clicked.connect(self.undo_point)
        self.btn_reset.clicked.connect(self.reset_points)
        self.btn_apply.clicked.connect(self.apply_and_accept)
        self.slab_spin.valueChanged.connect(self._on_slab_spin_changed)
        self.slab_slider.valueChanged.connect(self._on_slab_slider_changed)
        self.trans_x.valueChanged.connect(self.update_plane_preview)
        self.trans_y.valueChanged.connect(self.update_plane_preview)
        self.trans_z.valueChanged.connect(self.update_plane_preview)
        self.rot_x.valueChanged.connect(self.update_plane_preview)
        self.rot_y.valueChanged.connect(self.update_plane_preview)
        self.rot_z.valueChanged.connect(self.update_plane_preview)
        self.tabs.currentChanged.connect(self._on_tab_changed)

        # Connect slab side radio buttons to update preview
        self.slab_centered.toggled.connect(self.update_plane_preview)
        self.slab_positive.toggled.connect(self.update_plane_preview)
        self.slab_negative.toggled.connect(self.update_plane_preview)

        # Connect alignment radio buttons to update preview
        self.align_keep.toggled.connect(self.update_plane_preview)
        self.align_x.toggled.connect(self.update_plane_preview)
        self.align_y.toggled.connect(self.update_plane_preview)
        self.align_z.toggled.connect(self.update_plane_preview)

    def _on_prev(self):
        idx = self.tabs.currentIndex()
        if idx > 0:
            self._go_to_step(idx - 1)

    def _on_next(self):
        idx = self.tabs.currentIndex()
        if idx < self.tabs.count() - 1:
            self._go_to_step(idx + 1)

    def _go_to_step(self, idx):
        self.tabs.setCurrentIndex(idx)
        self._update_nav_buttons()
        if idx == 0:
            # Reset state if going to start
            self.reset_all()
        if idx == 1:
            if self.rb_by3.isChecked():
                self.start_point_picking()
            # else: handle canonical plane setup if needed
        if idx == 2:
            self.update_plane_preview()
        if idx == 3:
            self._update_summary()

    def _update_nav_buttons(self):
        idx = self.tabs.currentIndex()
        self.btn_prev.setEnabled(idx > 0)
        self.btn_back_to_start.setEnabled(idx > 0)
        # Only allow next if requirements for step are met
        if idx == 0:
            self.btn_next.setEnabled(True)
        elif idx == 1:
            if self.rb_by3.isChecked():
                self.btn_next.setEnabled(len(self.picked_points) == 3)
            else:
                self.btn_next.setEnabled(True)  # Canonical plane: always allow next
        elif idx == 2:
            self.btn_next.setEnabled(True)
        else:
            self.btn_next.setEnabled(False)

    def _on_tab_changed(self, idx):
        # Stop any active picking when switching tabs
        if hasattr(self, '_previous_tab_idx') and self._previous_tab_idx == 1:
            self.stop_point_picking()

        self._update_nav_buttons()
        if idx == 1:
            if self.rb_by3.isChecked():
                self.start_point_picking()
            # else: handle canonical plane setup if needed
        if idx == 2:
            self.update_plane_preview()
        if idx == 3:
            self._update_summary()

        # Remember current tab for next change
        self._previous_tab_idx = idx

    def start_point_picking(self):
        """Start point picking, ensuring any previous picking is disabled first"""
        try:
            # Disable any existing picking first
            self.mesh_viewer.plotter.disable_picking()
        except Exception:
            # If no picking was active, this will fail silently
            pass

        # Only start picking if we don't have 3 points yet
        if len(self.picked_points) < 3:
            try:
                # Now enable point picking
                self.mesh_viewer.plotter.enable_point_picking(
                    callback=self.on_point_picked,
                    use_picker=True,
                    show_message=False,
                    show_point=True,
                    left_clicking=True
                )
            except Exception as e:
                # If enabling picking fails, show a warning but don't crash
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "Picking Error",
                                  f"Could not enable point picking: {e}")

        self.update_info()

    def stop_point_picking(self):
        """Stop point picking and clean up picker state"""
        try:
            self.mesh_viewer.plotter.disable_picking()
        except Exception:
            pass

    def closeEvent(self, event):
        """Override closeEvent to clean up picking when dialog is closed"""
        self.stop_point_picking()
        super().closeEvent(event)

    def reject(self):
        """Override reject to clean up picking when dialog is cancelled"""
        self.stop_point_picking()
        super().reject()

    def on_point_picked(self, *args, **kwargs):
        point = None
        for arg in args:
            if hasattr(arg, '__len__') and len(arg) == 3 and all(isinstance(x, (float, int)) for x in arg):
                point = arg
                break
        if point is None:
            point = kwargs.get('point', None)
        if point is None:
            return
        if len(self.picked_points) < 3:
            self.picked_points.append(point)
            self.update_info()
            self.update_plane_preview()
            if len(self.picked_points) == 3:
                self.define_plane()
                # Stop picking when we have all 3 points
                self.stop_point_picking()
            self._update_nav_buttons()

    def update_info(self):
        self.info_label.setText(f"Points picked: {len(self.picked_points)}/3")
        self._update_nav_buttons()

    def define_plane(self):
        import numpy as np
        p1, p2, p3 = [np.array(p) for p in self.picked_points]
        normal = np.cross(p2 - p1, p3 - p1)
        if np.linalg.norm(normal) == 0:
            self.info_label.setText("Points are colinear. Pick again.")
            self.reset_points()
            return
        normal = normal / np.linalg.norm(normal)
        self.plane_params = {'origin': p1, 'normal': normal}
        self.slab_group.setVisible(True)
        self.align_group.setVisible(True)
        self.slabpos_group.setVisible(True)
        self.transform_group.setVisible(True)
        self.info_label.setText("Plane defined. Set options and proceed.")
        self.trans_x.setValue(0)
        self.trans_y.setValue(0)
        self.trans_z.setValue(0)
        self.rot_x.setValue(0)
        self.rot_y.setValue(0)
        self.rot_z.setValue(0)
        self.update_plane_preview()
        self._update_nav_buttons()

    def update_plane_preview(self):
        plotter = self.mesh_viewer.plotter
        # Remove previous previews
        for attr in ['_plane_preview_actor', '_point_preview_actor', '_slab_preview_actor', '_arrow_preview_actor']:
            actor = getattr(self, attr, None)
            if actor:
                try:
                    plotter.remove_actor(actor)
                except Exception:
                    pass
                setattr(self, attr, None)
        # Add new point previews
        if self.picked_points:
            points = np.array(self.picked_points)
            self._point_preview_actor = plotter.add_points(
                points,
                color='yellow',
                point_size=18,
                render_points_as_spheres=True,
                name='point_preview',
                opacity=1.0,
                reset_camera=False
            )
        # If 3 points, show the plane with transform and slab region
        if len(self.picked_points) == 3 and self.plane_params is not None:
            origin = np.array(self.plane_params['origin'])
            normal = np.array(self.plane_params['normal'])
            # Alignment - project normal onto canonical planes (plane cuts through canonical plane)
            if self.align_x.isChecked():
                # Project normal onto YZ plane (plane cuts through YZ plane)
                # Remove X component, keep Y and Z
                normal = np.array([0, normal[1], normal[2]])
                if np.linalg.norm(normal) > 0:
                    normal = normal / np.linalg.norm(normal)
                else:
                    normal = np.array([0, 1, 0])  # fallback to Y-axis
            elif self.align_y.isChecked():
                # Project normal onto ZX plane (plane cuts through ZX plane)
                # Remove Y component, keep Z and X
                normal = np.array([normal[0], 0, normal[2]])
                if np.linalg.norm(normal) > 0:
                    normal = normal / np.linalg.norm(normal)
                else:
                    normal = np.array([1, 0, 0])  # fallback to X-axis
            elif self.align_z.isChecked():
                # Project normal onto XY plane (plane cuts through XY plane)
                # Remove Z component, keep X and Y
                normal = np.array([normal[0], normal[1], 0])
                if np.linalg.norm(normal) > 0:
                    normal = normal / np.linalg.norm(normal)
                else:
                    normal = np.array([1, 0, 0])  # fallback to X-axis
            # Translation
            t = np.array([
                self.trans_x.value(),
                self.trans_y.value(),
                self.trans_z.value()
            ])
            center = origin + t
            # Rotation
            rx, ry, rz = np.deg2rad(self.rot_x.value()), np.deg2rad(self.rot_y.value()), np.deg2rad(self.rot_z.value())
            def rotmat(rx, ry, rz):
                return R.from_euler('zyx', [rz, ry, rx]).as_matrix()
            Rmat = rotmat(rx, ry, rz)
            normal_rot = Rmat @ normal
            # Estimate plane size
            mesh = getattr(self.mesh_viewer, 'mesh', None)
            if mesh is not None:
                bounds = np.array(mesh.bounds).reshape(3, 2)
                size = np.linalg.norm(bounds[:, 1] - bounds[:, 0]) * 0.5
            else:
                size = 1.0
            plane = pv.Plane(center=center, direction=normal_rot, i_size=size, j_size=size)
            self._plane_preview_actor = plotter.add_mesh(
                plane, color='yellow', opacity=0.4, name='plane_preview', pickable=False, reset_camera=False
            )

            # Add direction arrow to show plane normal and slab side
            arrow_length = size * 0.3  # Arrow length proportional to plane size
            arrow_start = center

            # Arrow direction depends on slab side selection
            arrow_direction = normal_rot.copy()
            arrow_color = 'red'

            if self.slab_positive.isChecked():
                # Arrow points in positive normal direction
                arrow_direction = normal_rot
                arrow_color = 'green'  # Green for positive side
            elif self.slab_negative.isChecked():
                # Arrow points in negative normal direction
                arrow_direction = -normal_rot
                arrow_color = 'red'    # Red for negative side
            else:
                # Centered: show both directions with smaller arrows
                arrow_direction = normal_rot
                arrow_color = 'blue'   # Blue for centered

            # Create arrow using PyVista
            arrow = pv.Arrow(start=arrow_start, direction=arrow_direction, scale=arrow_length)
            self._arrow_preview_actor = plotter.add_mesh(
                arrow, color=arrow_color, opacity=0.8, name='arrow_preview', pickable=False, reset_camera=False
            )
            # Slab region preview - show the actual slab region
            thickness = self.slab_spin.value() if self.slab_group.isVisible() else 0.2
            slab_center = center  # Keep plane center as reference

            # Adjust slab center based on side selection for preview
            if self.slab_positive.isChecked():
                slab_center = center + normal_rot * (thickness/2)
            elif self.slab_negative.isChecked():
                slab_center = center - normal_rot * (thickness/2)
            # For centered, slab_center remains at plane center
            if mesh is not None and thickness > 0:
                z = normal_rot / np.linalg.norm(normal_rot)
                up = np.array([0, 0, 1]) if abs(z[2]) < 0.9 else np.array([1, 0, 0])
                x = np.cross(up, z); x /= np.linalg.norm(x)
                y = np.cross(z, x)
                T = np.eye(4)
                T[:3, 0] = x
                T[:3, 1] = y
                T[:3, 2] = z
                T[:3, 3] = slab_center  # Use slab center instead of plane center
                slab_size = size
                box = pv.Box(bounds=(-slab_size, slab_size, -slab_size, slab_size, -thickness/2, thickness/2))
                box = box.transform(T, inplace=False)
                self._slab_preview_actor = plotter.add_mesh(
                    box, color="#00e6ff", opacity=self.outside_opacity, style='surface', name="slab_preview", reset_camera=False,
                    show_edges=True, edge_color="#0080a0", line_width=2
                )
            plotter.add_points(
                np.array([center]), color='red', point_size=20, render_points_as_spheres=True, name='center_indicator', opacity=0.7, reset_camera=False
            )
        plotter.render()

    def undo_point(self):
        if self.picked_points:
            self.picked_points.pop()
            self.update_info()
            self.update_plane_preview()

    def reset_points(self):
        self.picked_points = []
        self.update_info()
        self.plane_params = None
        self.slab_thickness = None
        self.update_plane_preview()
        plotter = self.mesh_viewer.plotter
        if hasattr(self, '_arrow_preview_actor') and self._arrow_preview_actor:
            try:
                plotter.remove_actor(self._arrow_preview_actor)
            except Exception:
                pass
            self._arrow_preview_actor = None
        self._update_nav_buttons()

    def _on_slab_spin_changed(self, val):
        self.slab_slider.blockSignals(True)
        self.slab_slider.setValue(int(val * 100))
        self.slab_slider.blockSignals(False)
        self.update_plane_preview()

    def _on_slab_slider_changed(self, val):
        self.slab_spin.blockSignals(True)
        self.slab_spin.setValue(val / 100.0)
        self.slab_spin.blockSignals(False)
        self.update_plane_preview()

    def _update_summary(self):
        if self.plane_params is None:
            self.summary_label.setText("No plane defined.")
            return
        origin = np.array(self.plane_params['origin'])
        normal = np.array(self.plane_params['normal'])
        if self.align_x.isChecked():
            # Project normal onto YZ plane (plane cuts through YZ plane)
            normal = np.array([0, normal[1], normal[2]])
            if np.linalg.norm(normal) > 0:
                normal = normal / np.linalg.norm(normal)
            else:
                normal = np.array([0, 1, 0])  # fallback
        elif self.align_y.isChecked():
            # Project normal onto ZX plane (plane cuts through ZX plane)
            normal = np.array([normal[0], 0, normal[2]])
            if np.linalg.norm(normal) > 0:
                normal = normal / np.linalg.norm(normal)
            else:
                normal = np.array([1, 0, 0])  # fallback
        elif self.align_z.isChecked():
            # Project normal onto XY plane (plane cuts through XY plane)
            normal = np.array([normal[0], normal[1], 0])
            if np.linalg.norm(normal) > 0:
                normal = normal / np.linalg.norm(normal)
            else:
                normal = np.array([1, 0, 0])  # fallback
        slabpos = "centered" if self.slab_centered.isChecked() else ("positive" if self.slab_positive.isChecked() else "negative")
        summary = f"<b>Plane origin:</b> {origin.round(3).tolist()}<br>"
        summary += f"<b>Normal:</b> {normal.round(3).tolist()}<br>"
        summary += f"<b>Slab thickness:</b> {self.slab_spin.value():.3f} m<br>"
        summary += f"<b>Slab position:</b> {slabpos}<br>"
        summary += f"<b>Transform:</b> T=({self.trans_x.value()}, {self.trans_y.value()}, {self.trans_z.value()}), R=({self.rot_x.value()}, {self.rot_y.value()}, {self.rot_z.value()})"
        self.summary_label.setText(summary)

    def apply_and_accept(self):
        self.slab_thickness = self.slab_spin.value()
        plotter = self.mesh_viewer.plotter
        for attr in ['_slab_preview_actor', '_point_preview_actor']:
            actor = getattr(self, attr, None)
            if actor:
                try:
                    plotter.remove_actor(actor)
                except Exception:
                    pass
                setattr(self, attr, None)
        plotter.render()
        if self.plane_params is not None:
            origin = np.array(self.plane_params['origin'])
            normal = np.array(self.plane_params['normal'])
            if self.align_x.isChecked():
                # Project normal onto YZ plane (plane cuts through YZ plane)
                normal = np.array([0, normal[1], normal[2]])
                if np.linalg.norm(normal) > 0:
                    normal = normal / np.linalg.norm(normal)
                else:
                    normal = np.array([0, 1, 0])  # fallback
            elif self.align_y.isChecked():
                # Project normal onto ZX plane (plane cuts through ZX plane)
                normal = np.array([normal[0], 0, normal[2]])
                if np.linalg.norm(normal) > 0:
                    normal = normal / np.linalg.norm(normal)
                else:
                    normal = np.array([1, 0, 0])  # fallback
            elif self.align_z.isChecked():
                # Project normal onto XY plane (plane cuts through XY plane)
                normal = np.array([normal[0], normal[1], 0])
                if np.linalg.norm(normal) > 0:
                    normal = normal / np.linalg.norm(normal)
                else:
                    normal = np.array([1, 0, 0])  # fallback
            # Store slab side selection for main window
            if self.slab_positive.isChecked():
                self._slab_side = 'positive'
            elif self.slab_negative.isChecked():
                self._slab_side = 'negative'
            else:
                self._slab_side = 'centered'

            # IMPORTANT: Don't modify origin here! Let main window handle slab positioning
            # The origin should remain the actual plane center
            # self.mesh_viewer.set_analysis_plane_params(origin, normal, self.slab_thickness)
            # --- Calculate and store the vector field for visualization ---
            vectors = mesh_utils.calculate_vector_field(self.mesh_viewer.mesh, origin, normal)
            self.mesh_viewer.mesh.point_data["Displacement"] = vectors
        else:
            QMessageBox.warning(self, "Incomplete Plane", "Please pick 3 points to define the plane before applying.")
        self.accept()

    def closeEvent(self, event):
        plotter = self.mesh_viewer.plotter
        for attr in ['_plane_preview_actor', '_point_preview_actor', '_slab_preview_actor', '_arrow_preview_actor']:
            actor = getattr(self, attr, None)
            if actor:
                try:
                    plotter.remove_actor(actor)
                except Exception:
                    pass
                setattr(self, attr, None)
        if hasattr(self, '_custom_light') and self._custom_light:
            try:
                plotter.remove_light(self._custom_light)
            except Exception:
                pass
            self._custom_light = None
        plotter.render()
        super().closeEvent(event)

    def set_outside_opacity(self, value):
        self.outside_opacity = float(value)
        self.update_plane_preview()
