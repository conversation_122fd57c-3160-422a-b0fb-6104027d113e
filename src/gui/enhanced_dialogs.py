"""
Enhanced dialog components for improved user experience

This module provides advanced dialog boxes and interactive components
for the 3D Heatmap Deformation Viewer.
"""
from typing import Dict, List, Optional, Tuple, Any
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QSpinBox, QDoubleSpinBox,
    QComboBox, QCheckBox, QGroupBox, QTabWidget, QWidget,
    QTextEdit, QProgressBar, QSlider, QDialogButtonBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QFileDialog, QColorDialog, QFrame, QSplitter
)
from PySide6.QtCore import Qt, Signal, QTimer, QThread, pyqtSignal
from PySide6.QtGui import QFont, QColor, QPalette, QPixmap, QIcon
import numpy as np
from ..utils.logging_config import get_logger

logger = get_logger(__name__)

class StatisticsDialog(QDialog):
    """Dialog for displaying comprehensive deformation statistics"""
    
    def __init__(self, statistics: Dict[str, float], parent=None):
        super().__init__(parent)
        self.setWindowTitle("Deformation Statistics")
        self.setModal(True)
        self.resize(400, 500)
        
        self.statistics = statistics
        self._setup_ui()
    
    def _setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Title
        title = QLabel("Statistical Analysis Results")
        title.setAlignment(Qt.AlignCenter)
        font = title.font()
        font.setPointSize(14)
        font.setBold(True)
        title.setFont(font)
        layout.addWidget(title)
        
        # Statistics table
        self.table = QTableWidget()
        self.table.setColumnCount(2)
        self.table.setHorizontalHeaderLabels(["Measure", "Value"])
        self.table.horizontalHeader().setStretchLastSection(True)
        
        # Populate table
        self._populate_table()
        
        layout.addWidget(self.table)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        # Export button
        export_btn = QPushButton("Export to CSV")
        export_btn.clicked.connect(self._export_csv)
        button_box.addButton(export_btn, QDialogButtonBox.ActionRole)
        
        layout.addWidget(button_box)
    
    def _populate_table(self):
        """Populate the statistics table"""
        # Define display names and formatting
        display_info = {
            'count': ('Count', '{:.0f}'),
            'mean': ('Mean', '{:.6f}'),
            'median': ('Median', '{:.6f}'),
            'std': ('Standard Deviation', '{:.6f}'),
            'var': ('Variance', '{:.6f}'),
            'min': ('Minimum', '{:.6f}'),
            'max': ('Maximum', '{:.6f}'),
            'range': ('Range', '{:.6f}'),
            'q25': ('25th Percentile', '{:.6f}'),
            'q75': ('75th Percentile', '{:.6f}'),
            'iqr': ('Interquartile Range', '{:.6f}'),
            'skewness': ('Skewness', '{:.6f}'),
            'kurtosis': ('Kurtosis', '{:.6f}'),
            'rms': ('RMS', '{:.6f}')
        }
        
        self.table.setRowCount(len(self.statistics))
        
        row = 0
        for key, value in self.statistics.items():
            if key in display_info:
                name, fmt = display_info[key]
                
                # Measure name
                name_item = QTableWidgetItem(name)
                name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
                self.table.setItem(row, 0, name_item)
                
                # Value
                value_item = QTableWidgetItem(fmt.format(value))
                value_item.setFlags(value_item.flags() & ~Qt.ItemIsEditable)
                self.table.setItem(row, 1, value_item)
                
                row += 1
        
        self.table.resizeColumnsToContents()
    
    def _export_csv(self):
        """Export statistics to CSV file"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export Statistics", "deformation_statistics.csv",
                "CSV Files (*.csv);;All Files (*)"
            )
            
            if file_path:
                import csv
                with open(file_path, 'w', newline='') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(['Measure', 'Value'])
                    
                    for row in range(self.table.rowCount()):
                        measure = self.table.item(row, 0).text()
                        value = self.table.item(row, 1).text()
                        writer.writerow([measure, value])
                
                QMessageBox.information(self, "Export Complete", 
                                      f"Statistics exported to {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Failed to export: {e}")

class AnomalyDetectionDialog(QDialog):
    """Dialog for configuring and running anomaly detection"""
    
    detection_requested = Signal(str, float)  # method, threshold
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Anomaly Detection")
        self.setModal(True)
        self.resize(350, 250)
        
        self._setup_ui()
    
    def _setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Method selection
        method_group = QGroupBox("Detection Method")
        method_layout = QVBoxLayout(method_group)
        
        self.method_combo = QComboBox()
        self.method_combo.addItems([
            "IQR (Interquartile Range)",
            "Z-Score",
            "Modified Z-Score"
        ])
        self.method_combo.currentTextChanged.connect(self._on_method_changed)
        
        method_layout.addWidget(QLabel("Method:"))
        method_layout.addWidget(self.method_combo)
        
        layout.addWidget(method_group)
        
        # Threshold configuration
        threshold_group = QGroupBox("Threshold Configuration")
        threshold_layout = QFormLayout(threshold_group)
        
        self.threshold_spin = QDoubleSpinBox()
        self.threshold_spin.setRange(0.1, 10.0)
        self.threshold_spin.setSingleStep(0.1)
        self.threshold_spin.setValue(1.5)
        self.threshold_spin.setDecimals(1)
        
        self.threshold_label = QLabel("Multiplier for IQR method")
        
        threshold_layout.addRow("Threshold:", self.threshold_spin)
        threshold_layout.addRow("Description:", self.threshold_label)
        
        layout.addWidget(threshold_group)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self._run_detection)
        button_box.rejected.connect(self.reject)
        
        layout.addWidget(button_box)
        
        # Initialize
        self._on_method_changed()
    
    def _on_method_changed(self):
        """Update threshold description based on selected method"""
        method = self.method_combo.currentText()
        
        if "IQR" in method:
            self.threshold_label.setText("Multiplier for IQR method (typical: 1.5)")
            self.threshold_spin.setValue(1.5)
        elif "Z-Score" in method and "Modified" not in method:
            self.threshold_label.setText("Z-score threshold (typical: 2.0-3.0)")
            self.threshold_spin.setValue(2.0)
        elif "Modified Z-Score" in method:
            self.threshold_label.setText("Modified Z-score threshold (typical: 3.5)")
            self.threshold_spin.setValue(3.5)
    
    def _run_detection(self):
        """Emit detection request and close dialog"""
        method_map = {
            "IQR (Interquartile Range)": "iqr",
            "Z-Score": "zscore",
            "Modified Z-Score": "modified_zscore"
        }
        
        method = method_map[self.method_combo.currentText()]
        threshold = self.threshold_spin.value()
        
        self.detection_requested.emit(method, threshold)
        self.accept()

class ProgressDialog(QDialog):
    """Enhanced progress dialog with detailed status"""
    
    def __init__(self, title: str = "Processing", parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(400, 150)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowCloseButtonHint)
        
        self._setup_ui()
    
    def _setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Status label
        self.status_label = QLabel("Initializing...")
        layout.addWidget(self.status_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        layout.addWidget(self.progress_bar)
        
        # Detail text
        self.detail_text = QTextEdit()
        self.detail_text.setMaximumHeight(80)
        self.detail_text.setReadOnly(True)
        layout.addWidget(self.detail_text)
        
        # Cancel button
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        layout.addWidget(self.cancel_button)
    
    def update_progress(self, value: int, status: str = "", detail: str = ""):
        """Update progress dialog"""
        self.progress_bar.setValue(value)
        
        if status:
            self.status_label.setText(status)
        
        if detail:
            self.detail_text.append(detail)
            # Auto-scroll to bottom
            cursor = self.detail_text.textCursor()
            cursor.movePosition(cursor.End)
            self.detail_text.setTextCursor(cursor)
    
    def set_indeterminate(self, indeterminate: bool = True):
        """Set progress bar to indeterminate mode"""
        if indeterminate:
            self.progress_bar.setRange(0, 0)
        else:
            self.progress_bar.setRange(0, 100)
