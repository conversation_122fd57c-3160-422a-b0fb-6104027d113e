"""
Advanced Annotation System for DeformViz 3D
Provides comprehensive annotation tools for 3D mesh visualization
"""

import numpy as np
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QGroupBox, QFormLayout, QLineEdit,
                              QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                              QColorDialog, QListWidget, QListWidgetItem,
                              QCheckBox, QSlider, QTabWidget, QMessageBox,
                              QFileDialog, QTableWidget, QTableWidgetItem)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QColor, QFont, QPixmap, QPainter
import pyvista as pv


class AnnotationManager:
    """Manages all annotations in the 3D scene"""
    
    def __init__(self, plotter):
        self.plotter = plotter
        self.annotations = []
        self.annotation_counter = 0
        
    def add_point_annotation(self, position, label="", color="red", size=10):
        """Add a point annotation"""
        annotation_id = f"point_{self.annotation_counter}"
        self.annotation_counter += 1
        
        # Create sphere for point
        sphere = pv.Sphere(radius=size/1000, center=position)
        actor = self.plotter.add_mesh(sphere, color=color, name=annotation_id)
        
        # Add text label if provided
        if label:
            self.plotter.add_point_labels([position], [label], 
                                        point_size=size, font_size=12,
                                        name=f"{annotation_id}_label")
        
        annotation = {
            'id': annotation_id,
            'type': 'point',
            'position': position,
            'label': label,
            'color': color,
            'size': size,
            'actor': actor,
            'visible': True
        }
        
        self.annotations.append(annotation)
        return annotation_id
    
    def add_line_annotation(self, start_pos, end_pos, label="", color="blue", width=3):
        """Add a line annotation"""
        annotation_id = f"line_{self.annotation_counter}"
        self.annotation_counter += 1
        
        # Create line
        line = pv.Line(start_pos, end_pos)
        actor = self.plotter.add_mesh(line, color=color, line_width=width, name=annotation_id)
        
        # Add label at midpoint if provided
        if label:
            midpoint = (np.array(start_pos) + np.array(end_pos)) / 2
            self.plotter.add_point_labels([midpoint], [label], 
                                        font_size=12, name=f"{annotation_id}_label")
        
        annotation = {
            'id': annotation_id,
            'type': 'line',
            'start_pos': start_pos,
            'end_pos': end_pos,
            'label': label,
            'color': color,
            'width': width,
            'actor': actor,
            'visible': True
        }
        
        self.annotations.append(annotation)
        return annotation_id
    
    def add_text_annotation(self, position, text, color="black", font_size=12):
        """Add a text annotation"""
        annotation_id = f"text_{self.annotation_counter}"
        self.annotation_counter += 1
        
        # Add text label
        self.plotter.add_point_labels([position], [text], 
                                    point_size=0, font_size=font_size,
                                    text_color=color, name=annotation_id)
        
        annotation = {
            'id': annotation_id,
            'type': 'text',
            'position': position,
            'text': text,
            'color': color,
            'font_size': font_size,
            'visible': True
        }
        
        self.annotations.append(annotation)
        return annotation_id
    
    def add_measurement_annotation(self, start_pos, end_pos, unit="m"):
        """Add a measurement annotation with distance"""
        distance = np.linalg.norm(np.array(end_pos) - np.array(start_pos))
        label = f"{distance:.3f} {unit}"
        
        # Add line
        line_id = self.add_line_annotation(start_pos, end_pos, label, "green", 2)
        
        # Add measurement points
        point1_id = self.add_point_annotation(start_pos, "P1", "green", 8)
        point2_id = self.add_point_annotation(end_pos, "P2", "green", 8)
        
        annotation_id = f"measurement_{self.annotation_counter}"
        self.annotation_counter += 1
        
        annotation = {
            'id': annotation_id,
            'type': 'measurement',
            'start_pos': start_pos,
            'end_pos': end_pos,
            'distance': distance,
            'unit': unit,
            'line_id': line_id,
            'point1_id': point1_id,
            'point2_id': point2_id,
            'visible': True
        }
        
        self.annotations.append(annotation)
        return annotation_id
    
    def remove_annotation(self, annotation_id):
        """Remove an annotation"""
        annotation = self.get_annotation(annotation_id)
        if annotation:
            # Remove from plotter
            try:
                self.plotter.remove_actor(annotation_id)
                self.plotter.remove_actor(f"{annotation_id}_label")
                
                # For measurements, remove associated elements
                if annotation['type'] == 'measurement':
                    self.remove_annotation(annotation['line_id'])
                    self.remove_annotation(annotation['point1_id'])
                    self.remove_annotation(annotation['point2_id'])
                
            except:
                pass
            
            # Remove from list
            self.annotations = [a for a in self.annotations if a['id'] != annotation_id]
    
    def get_annotation(self, annotation_id):
        """Get annotation by ID"""
        for annotation in self.annotations:
            if annotation['id'] == annotation_id:
                return annotation
        return None
    
    def toggle_annotation_visibility(self, annotation_id):
        """Toggle annotation visibility"""
        annotation = self.get_annotation(annotation_id)
        if annotation:
            annotation['visible'] = not annotation['visible']
            # Update visibility in plotter
            try:
                actor = self.plotter.renderer.actors.get(annotation_id)
                if actor:
                    actor.SetVisibility(annotation['visible'])
            except:
                pass
    
    def clear_all_annotations(self):
        """Clear all annotations"""
        for annotation in self.annotations.copy():
            self.remove_annotation(annotation['id'])
    
    def export_annotations(self, file_path):
        """Export annotations to JSON file"""
        import json
        
        export_data = []
        for annotation in self.annotations:
            # Create exportable data (remove non-serializable items)
            export_annotation = {k: v for k, v in annotation.items() 
                               if k not in ['actor']}
            # Convert numpy arrays to lists
            for key, value in export_annotation.items():
                if isinstance(value, np.ndarray):
                    export_annotation[key] = value.tolist()
            export_data.append(export_annotation)
        
        with open(file_path, 'w') as f:
            json.dump(export_data, f, indent=2)
    
    def import_annotations(self, file_path):
        """Import annotations from JSON file"""
        import json
        
        with open(file_path, 'r') as f:
            import_data = json.load(f)
        
        for annotation_data in import_data:
            if annotation_data['type'] == 'point':
                self.add_point_annotation(
                    annotation_data['position'],
                    annotation_data.get('label', ''),
                    annotation_data.get('color', 'red'),
                    annotation_data.get('size', 10)
                )
            elif annotation_data['type'] == 'line':
                self.add_line_annotation(
                    annotation_data['start_pos'],
                    annotation_data['end_pos'],
                    annotation_data.get('label', ''),
                    annotation_data.get('color', 'blue'),
                    annotation_data.get('width', 3)
                )
            elif annotation_data['type'] == 'text':
                self.add_text_annotation(
                    annotation_data['position'],
                    annotation_data['text'],
                    annotation_data.get('color', 'black'),
                    annotation_data.get('font_size', 12)
                )
            elif annotation_data['type'] == 'measurement':
                self.add_measurement_annotation(
                    annotation_data['start_pos'],
                    annotation_data['end_pos'],
                    annotation_data.get('unit', 'm')
                )


class AnnotationWidget(QWidget):
    """Widget for annotation controls in the sidebar"""
    
    annotation_added = Signal(str)  # Signal when annotation is added
    
    def __init__(self, mesh_viewer, parent=None):
        super().__init__(parent)
        self.mesh_viewer = mesh_viewer
        self.annotation_manager = AnnotationManager(mesh_viewer.plotter)
        
        # Current annotation mode
        self.current_mode = None
        self.pending_points = []
        self.point_picking_enabled = False
        
        self._setup_ui()
        self._setup_point_picking()
    
    def _setup_ui(self):
        """Setup the annotation interface"""
        layout = QVBoxLayout(self)
        
        # Annotation tools
        tools_group = QGroupBox("Annotation Tools")
        tools_layout = QVBoxLayout(tools_group)
        
        # Tool buttons
        self.point_btn = QPushButton("📍 Add Point")
        self.point_btn.clicked.connect(lambda: self._set_mode('point'))
        tools_layout.addWidget(self.point_btn)
        
        self.line_btn = QPushButton("📏 Add Line")
        self.line_btn.clicked.connect(lambda: self._set_mode('line'))
        tools_layout.addWidget(self.line_btn)
        
        self.text_btn = QPushButton("📝 Add Text")
        self.text_btn.clicked.connect(lambda: self._set_mode('text'))
        tools_layout.addWidget(self.text_btn)
        
        self.measure_btn = QPushButton("📐 Add Measurement")
        self.measure_btn.clicked.connect(lambda: self._set_mode('measurement'))
        tools_layout.addWidget(self.measure_btn)
        
        layout.addWidget(tools_group)
        
        # Annotation properties
        props_group = QGroupBox("Properties")
        props_layout = QFormLayout(props_group)
        
        self.label_input = QLineEdit()
        self.label_input.setPlaceholderText("Enter label/text...")
        props_layout.addRow("Label/Text:", self.label_input)
        
        # Color selection
        color_layout = QHBoxLayout()
        self.color_btn = QPushButton("🎨 Color")
        self.color_btn.clicked.connect(self._choose_color)
        self.current_color = QColor("red")
        color_layout.addWidget(self.color_btn)
        
        self.color_preview = QLabel()
        self.color_preview.setFixedSize(30, 20)
        self._update_color_preview()
        color_layout.addWidget(self.color_preview)
        color_layout.addStretch()
        
        props_layout.addRow("Color:", color_layout)
        
        # Size/Width
        self.size_spin = QSpinBox()
        self.size_spin.setRange(1, 50)
        self.size_spin.setValue(10)
        props_layout.addRow("Size/Width:", self.size_spin)
        
        layout.addWidget(props_group)
        
        # Annotation list
        list_group = QGroupBox("Annotations")
        list_layout = QVBoxLayout(list_group)
        
        self.annotation_list = QListWidget()
        self.annotation_list.itemClicked.connect(self._on_annotation_selected)
        list_layout.addWidget(self.annotation_list)
        
        # List controls
        list_controls = QHBoxLayout()
        
        delete_btn = QPushButton("🗑️ Delete")
        delete_btn.clicked.connect(self._delete_selected_annotation)
        list_controls.addWidget(delete_btn)
        
        clear_btn = QPushButton("🧹 Clear All")
        clear_btn.clicked.connect(self._clear_all_annotations)
        list_controls.addWidget(clear_btn)
        
        list_layout.addLayout(list_controls)
        layout.addWidget(list_group)
        
        # Export/Import
        io_group = QGroupBox("Import/Export")
        io_layout = QHBoxLayout(io_group)
        
        export_btn = QPushButton("💾 Export")
        export_btn.clicked.connect(self._export_annotations)
        io_layout.addWidget(export_btn)
        
        import_btn = QPushButton("📂 Import")
        import_btn.clicked.connect(self._import_annotations)
        io_layout.addWidget(import_btn)
        
        layout.addWidget(io_group)
        
        # Status
        self.status_label = QLabel("Click a tool, then click on the mesh to add annotations")
        self.status_label.setWordWrap(True)
        self.status_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
    
    def _setup_point_picking(self):
        """Setup point picking for annotations"""
        try:
            # Enable point picking on the plotter
            self.mesh_viewer.plotter.enable_point_picking(
                callback=self._on_point_picked,
                show_message=False,
                use_mesh=True
            )
            self.point_picking_enabled = True
        except Exception as e:
            print(f"Could not enable point picking: {e}")
    
    def _set_mode(self, mode):
        """Set annotation mode"""
        self.current_mode = mode
        self.pending_points = []
        
        # Update button styles
        for btn in [self.point_btn, self.line_btn, self.text_btn, self.measure_btn]:
            btn.setStyleSheet("")
        
        if mode == 'point':
            self.point_btn.setStyleSheet("background-color: #e3f2fd;")
            self.status_label.setText("Click on the mesh to add a point annotation")
        elif mode == 'line':
            self.line_btn.setStyleSheet("background-color: #e3f2fd;")
            self.status_label.setText("Click two points on the mesh to create a line")
        elif mode == 'text':
            self.text_btn.setStyleSheet("background-color: #e3f2fd;")
            self.status_label.setText("Click on the mesh to add text annotation")
        elif mode == 'measurement':
            self.measure_btn.setStyleSheet("background-color: #e3f2fd;")
            self.status_label.setText("Click two points to measure distance")
    
    def _on_point_picked(self, point):
        """Handle point picking"""
        if not self.current_mode:
            return
        
        self.pending_points.append(point)
        
        if self.current_mode == 'point':
            self._add_point_annotation(point)
        elif self.current_mode == 'text':
            self._add_text_annotation(point)
        elif self.current_mode in ['line', 'measurement']:
            if len(self.pending_points) == 2:
                if self.current_mode == 'line':
                    self._add_line_annotation()
                else:
                    self._add_measurement_annotation()
            else:
                self.status_label.setText(f"Click second point for {self.current_mode}")
    
    def _add_point_annotation(self, position):
        """Add point annotation"""
        label = self.label_input.text()
        color = self.current_color.name()
        size = self.size_spin.value()
        
        annotation_id = self.annotation_manager.add_point_annotation(position, label, color, size)
        self._update_annotation_list()
        self._reset_mode()
        
        self.annotation_added.emit(annotation_id)
    
    def _add_line_annotation(self):
        """Add line annotation"""
        if len(self.pending_points) >= 2:
            label = self.label_input.text()
            color = self.current_color.name()
            width = self.size_spin.value()
            
            annotation_id = self.annotation_manager.add_line_annotation(
                self.pending_points[0], self.pending_points[1], label, color, width)
            self._update_annotation_list()
            self._reset_mode()
            
            self.annotation_added.emit(annotation_id)
    
    def _add_text_annotation(self, position):
        """Add text annotation"""
        text = self.label_input.text()
        if not text:
            text = "Text"
        
        color = self.current_color.name()
        font_size = self.size_spin.value()
        
        annotation_id = self.annotation_manager.add_text_annotation(position, text, color, font_size)
        self._update_annotation_list()
        self._reset_mode()
        
        self.annotation_added.emit(annotation_id)
    
    def _add_measurement_annotation(self):
        """Add measurement annotation"""
        if len(self.pending_points) >= 2:
            annotation_id = self.annotation_manager.add_measurement_annotation(
                self.pending_points[0], self.pending_points[1])
            self._update_annotation_list()
            self._reset_mode()
            
            self.annotation_added.emit(annotation_id)
    
    def _reset_mode(self):
        """Reset annotation mode"""
        self.current_mode = None
        self.pending_points = []
        
        # Reset button styles
        for btn in [self.point_btn, self.line_btn, self.text_btn, self.measure_btn]:
            btn.setStyleSheet("")
        
        self.status_label.setText("Click a tool, then click on the mesh to add annotations")
    
    def _choose_color(self):
        """Choose annotation color"""
        color = QColorDialog.getColor(self.current_color, self)
        if color.isValid():
            self.current_color = color
            self._update_color_preview()
    
    def _update_color_preview(self):
        """Update color preview"""
        pixmap = QPixmap(30, 20)
        pixmap.fill(self.current_color)
        self.color_preview.setPixmap(pixmap)
    
    def _update_annotation_list(self):
        """Update the annotation list"""
        self.annotation_list.clear()
        
        for annotation in self.annotation_manager.annotations:
            item_text = f"{annotation['type'].title()}: {annotation['id']}"
            if 'label' in annotation and annotation['label']:
                item_text += f" - {annotation['label']}"
            elif 'text' in annotation:
                item_text += f" - {annotation['text']}"
            
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, annotation['id'])
            self.annotation_list.addItem(item)
    
    def _on_annotation_selected(self, item):
        """Handle annotation selection"""
        annotation_id = item.data(Qt.UserRole)
        # Could highlight the annotation in the 3D view
    
    def _delete_selected_annotation(self):
        """Delete selected annotation"""
        current_item = self.annotation_list.currentItem()
        if current_item:
            annotation_id = current_item.data(Qt.UserRole)
            self.annotation_manager.remove_annotation(annotation_id)
            self._update_annotation_list()
    
    def _clear_all_annotations(self):
        """Clear all annotations"""
        reply = QMessageBox.question(self, "Clear All", 
            "Are you sure you want to clear all annotations?",
            QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.annotation_manager.clear_all_annotations()
            self._update_annotation_list()
    
    def _export_annotations(self):
        """Export annotations"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Annotations", "annotations.json", 
            "JSON Files (*.json);;All Files (*)")
        
        if file_path:
            try:
                self.annotation_manager.export_annotations(file_path)
                QMessageBox.information(self, "Export Complete", 
                    f"Annotations exported to: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Export Error", f"Failed to export: {e}")
    
    def _import_annotations(self):
        """Import annotations"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Import Annotations", "", 
            "JSON Files (*.json);;All Files (*)")
        
        if file_path:
            try:
                self.annotation_manager.import_annotations(file_path)
                self._update_annotation_list()
                QMessageBox.information(self, "Import Complete", 
                    f"Annotations imported from: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Import Error", f"Failed to import: {e}")
