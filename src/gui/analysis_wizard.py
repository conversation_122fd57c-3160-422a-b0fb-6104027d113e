"""
Analysis Wizard - Guided workflow for new users
Provides step-by-step guidance through the complete deformation analysis process
"""

from PySide6.QtWidgets import (QWizard, QWizardPage, QVBoxLayout, QHBoxLayout, 
                               QLabel, QPushButton, QFileDialog, QMessageBox, 
                               QProgressBar, QTextEdit, QCheckBox, QSpinBox,
                               QDoubleSpinBox, QComboBox, QGroupBox, QFormLayout)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QPixmap, QFont
import os

class AnalysisWizard(QWizard):
    """Main analysis wizard dialog"""
    
    # Signals for communicating with main window
    mesh_load_requested = Signal(str)  # file_path
    plane_definition_requested = Signal()
    analysis_requested = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("DeformViz 3D - Analysis Wizard")
        self.setWizardStyle(QWizard.ModernStyle)
        self.setMinimumSize(600, 500)
        
        # Add wizard pages
        self.addPage(WelcomePage())
        self.addPage(MeshLoadPage())
        self.addPage(PlaneDefinitionPage())
        self.addPage(AnalysisConfigPage())
        self.addPage(ResultsPage())
        
        # Connect signals
        self.currentIdChanged.connect(self._on_page_changed)
        
    def _on_page_changed(self, page_id):
        """Handle page changes and update content"""
        current_page = self.currentPage()
        if hasattr(current_page, 'on_page_entered'):
            current_page.on_page_entered()

class WelcomePage(QWizardPage):
    """Welcome page with overview of the analysis process"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("Welcome to DeformViz 3D")
        self.setSubTitle("This wizard will guide you through the complete deformation analysis process")
        
        layout = QVBoxLayout()
        
        # Welcome message
        welcome_text = QLabel("""
        <h3>🎯 What you'll accomplish:</h3>
        <ul>
            <li><b>Load your 3D mesh</b> - Import your structural model</li>
            <li><b>Define analysis plane</b> - Set reference plane for measurements</li>
            <li><b>Configure analysis</b> - Choose analysis parameters</li>
            <li><b>View results</b> - Visualize deformation heatmaps</li>
        </ul>
        
        <h3>📋 Before you start:</h3>
        <ul>
            <li>Have your 3D mesh file ready (PLY, OBJ, STL, etc.)</li>
            <li>Know the coordinate system and units</li>
            <li>Understand your analysis requirements</li>
        </ul>
        
        <p><b>💡 Tip:</b> This wizard provides guidance for first-time users. 
        Experienced users can access all tools directly from the main interface.</p>
        """)
        welcome_text.setWordWrap(True)
        layout.addWidget(welcome_text)
        
        self.setLayout(layout)

class MeshLoadPage(QWizardPage):
    """Page for loading and validating mesh"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("Load 3D Mesh")
        self.setSubTitle("Select and load your 3D mesh file for analysis")
        
        self.mesh_loaded = False
        self.file_path = ""
        
        layout = QVBoxLayout()
        
        # File selection
        file_group = QGroupBox("Select Mesh File")
        file_layout = QVBoxLayout()
        
        self.file_label = QLabel("No file selected")
        self.file_label.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 10px; border: 1px solid #ccc; }")
        file_layout.addWidget(self.file_label)
        
        self.browse_btn = QPushButton("Browse for Mesh File...")
        self.browse_btn.clicked.connect(self._browse_file)
        file_layout.addWidget(self.browse_btn)
        
        file_group.setLayout(file_layout)
        layout.addWidget(file_group)
        
        # Supported formats info
        formats_group = QGroupBox("Supported Formats")
        formats_layout = QVBoxLayout()
        
        formats_text = QLabel("""
        <b>Common formats:</b> PLY, OBJ, STL, VTK, VTP<br>
        <b>CAD formats:</b> GLTF, GLB, 3DS, DAE, FBX<br>
        <b>Scientific formats:</b> MESH, MSH, CTM, GTS<br>
        <b>Other formats:</b> OFF, X, WRL, USD, USDA, USDC, USDZ
        """)
        formats_layout.addWidget(formats_text)
        formats_group.setLayout(formats_layout)
        layout.addWidget(formats_group)
        
        # Load status
        self.status_label = QLabel("Please select a mesh file to continue")
        self.status_label.setStyleSheet("QLabel { color: #666; font-style: italic; }")
        layout.addWidget(self.status_label)
        
        self.setLayout(layout)
        
    def _browse_file(self):
        """Open file dialog to select mesh"""
        filters = (
            "3D Mesh Files (*.stl *.obj *.ply *.vtk *.vtp *.off *.gltf *.glb *.3ds *.dae *.fbx *.x *.wrl *.mesh *.msh *.ctm *.gts *.usd *.usda *.usdc *.usdz);;"
            "STL Files (*.stl);;"
            "OBJ Files (*.obj);;"
            "PLY Files (*.ply);;"
            "VTK Files (*.vtk *.vtp);;"
            "All Files (*)"
        )
        
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select 3D Mesh File", "", filters
        )
        
        if file_path:
            self.file_path = file_path
            self.file_label.setText(f"Selected: {os.path.basename(file_path)}")
            self.file_label.setStyleSheet("QLabel { background-color: #e8f5e8; padding: 10px; border: 1px solid #4CAF50; }")
            self.status_label.setText("✅ File selected. Click 'Next' to load the mesh.")
            self.status_label.setStyleSheet("QLabel { color: #4CAF50; font-weight: bold; }")
            self.mesh_loaded = True
            self.completeChanged.emit()
            
            # Emit signal to main window to load mesh
            wizard = self.wizard()
            if hasattr(wizard, 'mesh_load_requested'):
                wizard.mesh_load_requested.emit(file_path)
    
    def isComplete(self):
        """Page is complete when mesh is loaded"""
        return self.mesh_loaded
    
    def on_page_entered(self):
        """Called when page is entered"""
        if not self.mesh_loaded:
            self.status_label.setText("Please select a mesh file to continue")

class PlaneDefinitionPage(QWizardPage):
    """Page for defining analysis plane"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("Define Analysis Plane")
        self.setSubTitle("Set the reference plane for deformation measurements")
        
        self.plane_defined = False
        
        layout = QVBoxLayout()
        
        # Instructions
        instructions = QLabel("""
        <h3>📐 Define your analysis plane:</h3>
        <p>The analysis plane serves as the reference for measuring deformations. 
        Points will be measured as distances from this plane.</p>
        
        <p><b>Choose your method:</b></p>
        <ul>
            <li><b>3 Points:</b> Click three points on your mesh to define the plane</li>
            <li><b>Canonical Plane:</b> Align with standard XY, YZ, or ZX planes</li>
        </ul>
        """)
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Plane definition button
        self.define_btn = QPushButton("Open Plane Definition Tool")
        self.define_btn.setMinimumHeight(40)
        self.define_btn.clicked.connect(self._open_plane_definition)
        layout.addWidget(self.define_btn)
        
        # Status
        self.plane_status = QLabel("No plane defined yet")
        self.plane_status.setStyleSheet("QLabel { color: #666; font-style: italic; }")
        layout.addWidget(self.plane_status)
        
        # Tips
        tips = QLabel("""
        <h3>💡 Tips:</h3>
        <ul>
            <li>Choose points that represent the intended reference surface</li>
            <li>For walls: pick points along the intended flat surface</li>
            <li>For canonical alignment: use when you want standard orientations</li>
            <li>The SLAB thickness determines the analysis region around the plane</li>
        </ul>
        """)
        tips.setWordWrap(True)
        tips.setStyleSheet("QLabel { background-color: #f9f9f9; padding: 10px; border-left: 3px solid #2196F3; }")
        layout.addWidget(tips)
        
        self.setLayout(layout)
    
    def _open_plane_definition(self):
        """Open plane definition dialog"""
        wizard = self.wizard()
        if hasattr(wizard, 'plane_definition_requested'):
            wizard.plane_definition_requested.emit()
            # Assume plane will be defined (main window should update this)
            self.plane_defined = True
            self.plane_status.setText("✅ Plane defined successfully")
            self.plane_status.setStyleSheet("QLabel { color: #4CAF50; font-weight: bold; }")
            self.completeChanged.emit()
    
    def isComplete(self):
        """Page is complete when plane is defined"""
        return self.plane_defined
    
    def mark_plane_defined(self):
        """Called by main window when plane is successfully defined"""
        self.plane_defined = True
        self.plane_status.setText("✅ Plane defined successfully")
        self.plane_status.setStyleSheet("QLabel { color: #4CAF50; font-weight: bold; }")
        self.completeChanged.emit()

class AnalysisConfigPage(QWizardPage):
    """Page for configuring analysis parameters"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("Configure Analysis")
        self.setSubTitle("Set parameters for deformation analysis")
        
        layout = QVBoxLayout()
        
        # Analysis options
        config_group = QGroupBox("Analysis Configuration")
        config_layout = QFormLayout()
        
        # SLAB thickness
        self.slab_thickness = QDoubleSpinBox()
        self.slab_thickness.setRange(0.01, 10.0)
        self.slab_thickness.setValue(0.2)
        self.slab_thickness.setDecimals(3)
        self.slab_thickness.setSuffix(" m")
        config_layout.addRow("SLAB Thickness:", self.slab_thickness)
        
        # Analysis region
        self.analysis_region = QComboBox()
        self.analysis_region.addItems(["Centered on plane", "Positive side only", "Negative side only"])
        config_layout.addRow("Analysis Region:", self.analysis_region)
        
        # Colormap
        self.colormap = QComboBox()
        self.colormap.addItems(["coolwarm", "viridis", "plasma", "jet", "rainbow"])
        config_layout.addRow("Colormap:", self.colormap)
        
        config_group.setLayout(config_layout)
        layout.addWidget(config_group)
        
        # Run analysis button
        self.run_btn = QPushButton("Run Deformation Analysis")
        self.run_btn.setMinimumHeight(40)
        self.run_btn.clicked.connect(self._run_analysis)
        layout.addWidget(self.run_btn)
        
        # Progress
        self.progress = QProgressBar()
        self.progress.setVisible(False)
        layout.addWidget(self.progress)
        
        # Status
        self.analysis_status = QLabel("Ready to run analysis")
        layout.addWidget(self.analysis_status)
        
        self.setLayout(layout)
        self.analysis_complete = False
    
    def _run_analysis(self):
        """Run the deformation analysis"""
        self.progress.setVisible(True)
        self.progress.setRange(0, 0)  # Indeterminate progress
        self.analysis_status.setText("Running analysis...")
        
        # Emit signal to main window
        wizard = self.wizard()
        if hasattr(wizard, 'analysis_requested'):
            wizard.analysis_requested.emit()
        
        # Simulate completion (main window should call mark_analysis_complete)
        self.mark_analysis_complete()
    
    def mark_analysis_complete(self):
        """Called when analysis is complete"""
        self.progress.setVisible(False)
        self.analysis_status.setText("✅ Analysis complete!")
        self.analysis_status.setStyleSheet("QLabel { color: #4CAF50; font-weight: bold; }")
        self.analysis_complete = True
        self.completeChanged.emit()
    
    def isComplete(self):
        """Page is complete when analysis is done"""
        return self.analysis_complete

class ResultsPage(QWizardPage):
    """Final page showing results and next steps"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("Analysis Complete!")
        self.setSubTitle("Your deformation analysis is ready")
        
        layout = QVBoxLayout()
        
        # Success message
        success_msg = QLabel("""
        <h2>🎉 Congratulations!</h2>
        <p>Your deformation analysis has been completed successfully. 
        The results are now displayed in the main viewer.</p>
        """)
        success_msg.setAlignment(Qt.AlignCenter)
        layout.addWidget(success_msg)
        
        # Next steps
        next_steps = QLabel("""
        <h3>🚀 What you can do next:</h3>
        <ul>
            <li><b>Adjust visualization:</b> Change colormaps, transparency, and view angles</li>
            <li><b>Export results:</b> Save screenshots, CSV data, or analysis reports</li>
            <li><b>Refine analysis:</b> Adjust SLAB thickness or plane position</li>
            <li><b>Advanced analysis:</b> Use curvature analysis, statistics, or anomaly detection</li>
        </ul>
        
        <h3>📚 Learn more:</h3>
        <ul>
            <li>Use the comprehensive tooltips throughout the interface</li>
            <li>Explore the sidebar tabs for detailed mesh information</li>
            <li>Try different visualization modes and analysis tools</li>
        </ul>
        """)
        next_steps.setWordWrap(True)
        layout.addWidget(next_steps)
        
        # Tips
        tips = QLabel("""
        <p><b>💡 Pro tip:</b> Save your session to preserve all settings and continue your work later!</p>
        """)
        tips.setStyleSheet("QLabel { background-color: #e3f2fd; padding: 15px; border-radius: 5px; }")
        tips.setAlignment(Qt.AlignCenter)
        layout.addWidget(tips)
        
        self.setLayout(layout)
    
    def isComplete(self):
        """Results page is always complete"""
        return True
