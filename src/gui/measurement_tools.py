"""
Measurement Tools for DeformViz 3D
Provides essential measurement capabilities for 3D meshes
"""

import numpy as np
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QListWidget, QGroupBox, QFormLayout,
                              QDoubleSpinBox, QTextEdit, QTabWidget, QWidget,
                              QTableWidget, QTableWidgetItem, QHeaderView,
                              QMessageBox, QFileDialog, QComboBox)
from PySide6.QtCore import Qt, Signal
import pyvista as pv


class MeasurementToolsDialog(QDialog):
    """Dialog for comprehensive measurement tools"""
    
    measurement_added = Signal(dict)  # Signal when new measurement is added
    
    def __init__(self, mesh_viewer, parent=None):
        super().__init__(parent)
        self.mesh_viewer = mesh_viewer
        self.measurements = []
        self.current_measurement_type = None
        self.selected_points = []
        
        self.setWindowTitle("Measurement Tools - DeformViz 3D")
        self.setModal(False)  # Allow interaction with main window
        self.resize(600, 500)
        
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        """Setup the measurement tools interface"""
        layout = QVBoxLayout(self)
        
        # Create tabbed interface
        tabs = QTabWidget()
        
        # Tab 1: Basic Measurements
        basic_tab = self._create_basic_measurements_tab()
        tabs.addTab(basic_tab, "Basic Measurements")
        
        # Tab 2: Surface Analysis
        surface_tab = self._create_surface_analysis_tab()
        tabs.addTab(surface_tab, "Surface Analysis")
        
        # Tab 3: Geometric Properties
        geometric_tab = self._create_geometric_properties_tab()
        tabs.addTab(geometric_tab, "Geometric Properties")
        
        layout.addWidget(tabs)
        
        # Measurement results table
        results_group = QGroupBox("Measurement Results")
        results_layout = QVBoxLayout(results_group)
        
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(4)
        self.results_table.setHorizontalHeaderLabels(["Type", "Description", "Value", "Unit"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        results_layout.addWidget(self.results_table)
        
        # Results buttons
        results_buttons = QHBoxLayout()
        
        export_btn = QPushButton("Export Results")
        export_btn.clicked.connect(self._export_results)
        
        clear_btn = QPushButton("Clear All")
        clear_btn.clicked.connect(self._clear_measurements)
        
        results_buttons.addWidget(export_btn)
        results_buttons.addWidget(clear_btn)
        results_buttons.addStretch()
        
        results_layout.addLayout(results_buttons)
        layout.addWidget(results_group)
        
        # Close button
        close_btn = QPushButton("Close")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)
    
    def _create_basic_measurements_tab(self):
        """Create basic measurements tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Distance measurements
        distance_group = QGroupBox("Distance Measurements")
        distance_layout = QVBoxLayout(distance_group)
        
        point_to_point_btn = QPushButton("Point-to-Point Distance")
        point_to_point_btn.clicked.connect(lambda: self._start_measurement("point_to_point"))
        distance_layout.addWidget(point_to_point_btn)
        
        point_to_surface_btn = QPushButton("Point-to-Surface Distance")
        point_to_surface_btn.clicked.connect(lambda: self._start_measurement("point_to_surface"))
        distance_layout.addWidget(point_to_surface_btn)
        
        layout.addWidget(distance_group)
        
        # Angle measurements
        angle_group = QGroupBox("Angle Measurements")
        angle_layout = QVBoxLayout(angle_group)
        
        three_point_angle_btn = QPushButton("Three-Point Angle")
        three_point_angle_btn.clicked.connect(lambda: self._start_measurement("three_point_angle"))
        angle_layout.addWidget(three_point_angle_btn)
        
        surface_angle_btn = QPushButton("Surface Normal Angle")
        surface_angle_btn.clicked.connect(lambda: self._start_measurement("surface_angle"))
        angle_layout.addWidget(surface_angle_btn)
        
        layout.addWidget(angle_group)
        
        # Instructions
        instructions = QLabel("""
        <b>Instructions:</b><br>
        1. Select measurement type<br>
        2. Click points on the 3D mesh<br>
        3. Results appear in table below<br>
        4. Use Ctrl+Click for precise selection
        """)
        instructions.setStyleSheet("background: #f0f0f0; padding: 10px; border-radius: 5px;")
        layout.addWidget(instructions)
        
        layout.addStretch()
        return tab
    
    def _create_surface_analysis_tab(self):
        """Create surface analysis tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Surface roughness
        roughness_group = QGroupBox("Surface Roughness Analysis")
        roughness_layout = QVBoxLayout(roughness_group)
        
        # Region selection
        region_layout = QHBoxLayout()
        region_layout.addWidget(QLabel("Analysis Region:"))
        
        self.region_combo = QComboBox()
        self.region_combo.addItems(["Entire Mesh", "Selected Region", "SLAB Region"])
        region_layout.addWidget(self.region_combo)
        region_layout.addStretch()
        roughness_layout.addLayout(region_layout)
        
        # Roughness parameters
        params_layout = QFormLayout()
        
        self.reference_plane_combo = QComboBox()
        self.reference_plane_combo.addItems(["Best Fit Plane", "Current Analysis Plane", "XY Plane", "XZ Plane", "YZ Plane"])
        params_layout.addRow("Reference Plane:", self.reference_plane_combo)
        
        roughness_layout.addLayout(params_layout)
        
        # Calculate button
        calc_roughness_btn = QPushButton("Calculate Surface Roughness")
        calc_roughness_btn.clicked.connect(self._calculate_surface_roughness)
        roughness_layout.addWidget(calc_roughness_btn)
        
        layout.addWidget(roughness_group)
        
        # Flatness analysis
        flatness_group = QGroupBox("Flatness Analysis")
        flatness_layout = QVBoxLayout(flatness_group)
        
        calc_flatness_btn = QPushButton("Calculate Flatness Deviation")
        calc_flatness_btn.clicked.connect(self._calculate_flatness)
        flatness_layout.addWidget(calc_flatness_btn)
        
        layout.addWidget(flatness_group)
        
        layout.addStretch()
        return tab
    
    def _create_geometric_properties_tab(self):
        """Create geometric properties tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Basic properties
        basic_group = QGroupBox("Basic Geometric Properties")
        basic_layout = QVBoxLayout(basic_group)
        
        calc_area_btn = QPushButton("Calculate Surface Area")
        calc_area_btn.clicked.connect(self._calculate_surface_area)
        basic_layout.addWidget(calc_area_btn)
        
        calc_volume_btn = QPushButton("Calculate Volume")
        calc_volume_btn.clicked.connect(self._calculate_volume)
        basic_layout.addWidget(calc_volume_btn)
        
        calc_bbox_btn = QPushButton("Calculate Bounding Box")
        calc_bbox_btn.clicked.connect(self._calculate_bounding_box)
        basic_layout.addWidget(calc_bbox_btn)
        
        layout.addWidget(basic_group)
        
        # Advanced properties
        advanced_group = QGroupBox("Advanced Properties")
        advanced_layout = QVBoxLayout(advanced_group)
        
        calc_centroid_btn = QPushButton("Calculate Centroid")
        calc_centroid_btn.clicked.connect(self._calculate_centroid)
        advanced_layout.addWidget(calc_centroid_btn)
        
        calc_inertia_btn = QPushButton("Calculate Moments of Inertia")
        calc_inertia_btn.clicked.connect(self._calculate_moments_of_inertia)
        advanced_layout.addWidget(calc_inertia_btn)
        
        layout.addWidget(advanced_group)
        
        layout.addStretch()
        return tab
    
    def _connect_signals(self):
        """Connect measurement signals"""
        # Connect to mesh viewer for point selection
        if hasattr(self.mesh_viewer, 'plotter'):
            try:
                self.mesh_viewer.plotter.enable_point_picking(callback=self._on_point_picked)
            except Exception as e:
                print(f"Could not enable point picking: {e}")
    
    def _start_measurement(self, measurement_type):
        """Start a new measurement"""
        self.current_measurement_type = measurement_type
        self.selected_points = []
        
        # Update status
        if measurement_type == "point_to_point":
            self.parent().show_enhanced_status_message("Select 2 points for distance measurement", 5000, "info")
        elif measurement_type == "three_point_angle":
            self.parent().show_enhanced_status_message("Select 3 points for angle measurement", 5000, "info")
        elif measurement_type == "point_to_surface":
            self.parent().show_enhanced_status_message("Select 1 point for surface distance", 5000, "info")
        elif measurement_type == "surface_angle":
            self.parent().show_enhanced_status_message("Select 2 points for surface angle", 5000, "info")
    
    def _on_point_picked(self, point):
        """Handle point selection"""
        if self.current_measurement_type is None:
            return
        
        self.selected_points.append(point)
        
        # Check if we have enough points for the measurement
        if self.current_measurement_type == "point_to_point" and len(self.selected_points) == 2:
            self._calculate_point_to_point_distance()
        elif self.current_measurement_type == "three_point_angle" and len(self.selected_points) == 3:
            self._calculate_three_point_angle()
        elif self.current_measurement_type == "point_to_surface" and len(self.selected_points) == 1:
            self._calculate_point_to_surface_distance()
        elif self.current_measurement_type == "surface_angle" and len(self.selected_points) == 2:
            self._calculate_surface_angle()
    
    def _calculate_point_to_point_distance(self):
        """Calculate distance between two points"""
        try:
            p1, p2 = self.selected_points[0], self.selected_points[1]
            distance = np.linalg.norm(np.array(p2) - np.array(p1))
            
            self._add_measurement("Distance", f"Point-to-Point", f"{distance:.6f}", "m")
            self._reset_measurement()
            
        except Exception as e:
            QMessageBox.warning(self, "Measurement Error", f"Failed to calculate distance: {e}")
    
    def _calculate_three_point_angle(self):
        """Calculate angle between three points"""
        try:
            p1, p2, p3 = [np.array(p) for p in self.selected_points]
            
            # Vectors from middle point
            v1 = p1 - p2
            v2 = p3 - p2
            
            # Calculate angle
            cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
            angle_rad = np.arccos(np.clip(cos_angle, -1.0, 1.0))
            angle_deg = np.degrees(angle_rad)
            
            self._add_measurement("Angle", f"Three-Point Angle", f"{angle_deg:.3f}", "degrees")
            self._reset_measurement()
            
        except Exception as e:
            QMessageBox.warning(self, "Measurement Error", f"Failed to calculate angle: {e}")
    
    def _reset_measurement(self):
        """Reset current measurement"""
        self.current_measurement_type = None
        self.selected_points = []
        self.parent().show_enhanced_status_message("Measurement completed", 2000, "success")
    
    def _add_measurement(self, type_name, description, value, unit):
        """Add measurement to results table"""
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)
        
        self.results_table.setItem(row, 0, QTableWidgetItem(type_name))
        self.results_table.setItem(row, 1, QTableWidgetItem(description))
        self.results_table.setItem(row, 2, QTableWidgetItem(value))
        self.results_table.setItem(row, 3, QTableWidgetItem(unit))
        
        # Store measurement data
        measurement = {
            'type': type_name,
            'description': description,
            'value': value,
            'unit': unit,
            'points': self.selected_points.copy() if self.selected_points else []
        }
        self.measurements.append(measurement)
        
        # Emit signal
        self.measurement_added.emit(measurement)
    
    def _export_results(self):
        """Export measurement results to CSV"""
        try:
            if not self.measurements:
                QMessageBox.information(self, "No Data", "No measurements to export.")
                return
            
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Export Measurements", "", "CSV Files (*.csv);;All Files (*)")
            
            if file_path:
                import csv
                with open(file_path, 'w', newline='') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(['Type', 'Description', 'Value', 'Unit'])
                    
                    for measurement in self.measurements:
                        writer.writerow([
                            measurement['type'],
                            measurement['description'],
                            measurement['value'],
                            measurement['unit']
                        ])
                
                QMessageBox.information(self, "Export Complete", f"Measurements exported to: {file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Failed to export measurements: {e}")
    
    def _clear_measurements(self):
        """Clear all measurements"""
        self.measurements.clear()
        self.results_table.setRowCount(0)
        self._reset_measurement()

    def _calculate_point_to_surface_distance(self):
        """Calculate minimum distance from point to surface"""
        try:
            if not self.mesh_viewer.mesh:
                QMessageBox.warning(self, "No Mesh", "Please load a mesh first.")
                return

            point = np.array(self.selected_points[0])
            mesh = self.mesh_viewer.mesh

            # Find closest point on surface
            closest_point, cell_id = mesh.find_closest_point(point, return_closest_point=True)
            distance = np.linalg.norm(point - closest_point)

            self._add_measurement("Distance", f"Point-to-Surface", f"{distance:.6f}", "m")
            self._reset_measurement()

        except Exception as e:
            QMessageBox.warning(self, "Measurement Error", f"Failed to calculate surface distance: {e}")

    def _calculate_surface_angle(self):
        """Calculate angle between surface normals at two points"""
        try:
            if not self.mesh_viewer.mesh:
                QMessageBox.warning(self, "No Mesh", "Please load a mesh first.")
                return

            mesh = self.mesh_viewer.mesh
            p1, p2 = [np.array(p) for p in self.selected_points]

            # Find closest points on surface and get normals
            _, cell_id1 = mesh.find_closest_point(p1, return_closest_point=True)
            _, cell_id2 = mesh.find_closest_point(p2, return_closest_point=True)

            # Calculate surface normals
            mesh_with_normals = mesh.compute_normals(cell_normals=True, point_normals=False)
            normals = mesh_with_normals.cell_data['Normals']

            normal1 = normals[cell_id1]
            normal2 = normals[cell_id2]

            # Calculate angle between normals
            cos_angle = np.dot(normal1, normal2) / (np.linalg.norm(normal1) * np.linalg.norm(normal2))
            angle_rad = np.arccos(np.clip(cos_angle, -1.0, 1.0))
            angle_deg = np.degrees(angle_rad)

            self._add_measurement("Angle", f"Surface Normal Angle", f"{angle_deg:.3f}", "degrees")
            self._reset_measurement()

        except Exception as e:
            QMessageBox.warning(self, "Measurement Error", f"Failed to calculate surface angle: {e}")

    def _calculate_surface_roughness(self):
        """Calculate surface roughness parameters"""
        try:
            if not self.mesh_viewer.mesh:
                QMessageBox.warning(self, "No Mesh", "Please load a mesh first.")
                return

            mesh = self.mesh_viewer.mesh
            points = mesh.points

            # Get reference plane
            reference_plane = self.reference_plane_combo.currentText()

            if reference_plane == "Current Analysis Plane" and hasattr(self.parent(), '_last_plane_params'):
                plane_params = self.parent()._last_plane_params
                origin = plane_params['origin']
                normal = plane_params['normal']
            else:
                # Use best fit plane
                centroid = np.mean(points, axis=0)
                # Simple best fit plane using PCA
                centered_points = points - centroid
                _, _, vh = np.linalg.svd(centered_points)
                normal = vh[-1]  # Last component is normal to best fit plane
                origin = centroid

            # Calculate distances from points to reference plane
            distances = np.dot(points - origin, normal)

            # Apply region filter
            region = self.region_combo.currentText()
            if region == "SLAB Region" and hasattr(self.parent(), '_last_deformation_mask'):
                mask = self.parent()._last_deformation_mask
                distances = distances[mask]
            elif region == "Selected Region":
                # For now, use all points (could be enhanced with region selection)
                pass

            # Calculate roughness parameters
            ra = np.mean(np.abs(distances))  # Average roughness
            rq = np.sqrt(np.mean(distances**2))  # RMS roughness
            rz = np.max(distances) - np.min(distances)  # Peak-to-valley
            rsk = np.mean(distances**3) / (rq**3)  # Skewness
            rku = np.mean(distances**4) / (rq**4)  # Kurtosis

            # Add measurements
            self._add_measurement("Roughness", f"Ra (Average)", f"{ra:.6f}", "m")
            self._add_measurement("Roughness", f"Rq (RMS)", f"{rq:.6f}", "m")
            self._add_measurement("Roughness", f"Rz (Peak-Valley)", f"{rz:.6f}", "m")
            self._add_measurement("Roughness", f"Rsk (Skewness)", f"{rsk:.3f}", "")
            self._add_measurement("Roughness", f"Rku (Kurtosis)", f"{rku:.3f}", "")

            self.parent().show_enhanced_status_message("Surface roughness calculated", 3000, "success")

        except Exception as e:
            QMessageBox.critical(self, "Roughness Error", f"Failed to calculate surface roughness: {e}")

    def _calculate_flatness(self):
        """Calculate flatness deviation"""
        try:
            if not self.mesh_viewer.mesh:
                QMessageBox.warning(self, "No Mesh", "Please load a mesh first.")
                return

            mesh = self.mesh_viewer.mesh
            points = mesh.points

            # Calculate best fit plane
            centroid = np.mean(points, axis=0)
            centered_points = points - centroid
            _, _, vh = np.linalg.svd(centered_points)
            normal = vh[-1]

            # Calculate distances to best fit plane
            distances = np.dot(centered_points, normal)

            # Flatness metrics
            flatness_deviation = np.max(distances) - np.min(distances)
            rms_flatness = np.sqrt(np.mean(distances**2))

            self._add_measurement("Flatness", f"Total Flatness Deviation", f"{flatness_deviation:.6f}", "m")
            self._add_measurement("Flatness", f"RMS Flatness", f"{rms_flatness:.6f}", "m")

            self.parent().show_enhanced_status_message("Flatness calculated", 3000, "success")

        except Exception as e:
            QMessageBox.critical(self, "Flatness Error", f"Failed to calculate flatness: {e}")

    def _calculate_surface_area(self):
        """Calculate total surface area"""
        try:
            if not self.mesh_viewer.mesh:
                QMessageBox.warning(self, "No Mesh", "Please load a mesh first.")
                return

            mesh = self.mesh_viewer.mesh
            area = mesh.area

            self._add_measurement("Geometry", f"Total Surface Area", f"{area:.6f}", "m²")
            self.parent().show_enhanced_status_message("Surface area calculated", 3000, "success")

        except Exception as e:
            QMessageBox.critical(self, "Area Error", f"Failed to calculate surface area: {e}")

    def _calculate_volume(self):
        """Calculate mesh volume"""
        try:
            if not self.mesh_viewer.mesh:
                QMessageBox.warning(self, "No Mesh", "Please load a mesh first.")
                return

            mesh = self.mesh_viewer.mesh

            # Check if mesh is closed
            if not mesh.is_manifold:
                QMessageBox.warning(self, "Volume Warning",
                    "Mesh may not be watertight. Volume calculation may be inaccurate.")

            volume = mesh.volume

            self._add_measurement("Geometry", f"Volume", f"{volume:.6f}", "m³")
            self.parent().show_enhanced_status_message("Volume calculated", 3000, "success")

        except Exception as e:
            QMessageBox.critical(self, "Volume Error", f"Failed to calculate volume: {e}")

    def _calculate_bounding_box(self):
        """Calculate bounding box dimensions"""
        try:
            if not self.mesh_viewer.mesh:
                QMessageBox.warning(self, "No Mesh", "Please load a mesh first.")
                return

            mesh = self.mesh_viewer.mesh
            bounds = mesh.bounds

            # Calculate dimensions
            length = bounds[1] - bounds[0]  # X dimension
            width = bounds[3] - bounds[2]   # Y dimension
            height = bounds[5] - bounds[4]  # Z dimension

            # Calculate diagonal
            diagonal = np.sqrt(length**2 + width**2 + height**2)

            self._add_measurement("Bounding Box", f"Length (X)", f"{length:.6f}", "m")
            self._add_measurement("Bounding Box", f"Width (Y)", f"{width:.6f}", "m")
            self._add_measurement("Bounding Box", f"Height (Z)", f"{height:.6f}", "m")
            self._add_measurement("Bounding Box", f"Diagonal", f"{diagonal:.6f}", "m")

            self.parent().show_enhanced_status_message("Bounding box calculated", 3000, "success")

        except Exception as e:
            QMessageBox.critical(self, "Bounding Box Error", f"Failed to calculate bounding box: {e}")

    def _calculate_centroid(self):
        """Calculate mesh centroid"""
        try:
            if not self.mesh_viewer.mesh:
                QMessageBox.warning(self, "No Mesh", "Please load a mesh first.")
                return

            mesh = self.mesh_viewer.mesh
            centroid = mesh.center

            self._add_measurement("Centroid", f"X Coordinate", f"{centroid[0]:.6f}", "m")
            self._add_measurement("Centroid", f"Y Coordinate", f"{centroid[1]:.6f}", "m")
            self._add_measurement("Centroid", f"Z Coordinate", f"{centroid[2]:.6f}", "m")

            self.parent().show_enhanced_status_message("Centroid calculated", 3000, "success")

        except Exception as e:
            QMessageBox.critical(self, "Centroid Error", f"Failed to calculate centroid: {e}")

    def _calculate_moments_of_inertia(self):
        """Calculate moments of inertia (simplified)"""
        try:
            if not self.mesh_viewer.mesh:
                QMessageBox.warning(self, "No Mesh", "Please load a mesh first.")
                return

            mesh = self.mesh_viewer.mesh
            points = mesh.points
            centroid = mesh.center

            # Translate to centroid
            translated_points = points - centroid

            # Calculate second moments (simplified, assuming unit density)
            Ixx = np.sum(translated_points[:, 1]**2 + translated_points[:, 2]**2)
            Iyy = np.sum(translated_points[:, 0]**2 + translated_points[:, 2]**2)
            Izz = np.sum(translated_points[:, 0]**2 + translated_points[:, 1]**2)

            self._add_measurement("Inertia", f"Ixx", f"{Ixx:.3e}", "m⁴")
            self._add_measurement("Inertia", f"Iyy", f"{Iyy:.3e}", "m⁴")
            self._add_measurement("Inertia", f"Izz", f"{Izz:.3e}", "m⁴")

            self.parent().show_enhanced_status_message("Moments of inertia calculated", 3000, "success")

        except Exception as e:
            QMessageBox.critical(self, "Inertia Error", f"Failed to calculate moments of inertia: {e}")
