"""
Quality Control Module for DeformViz 3D
Provides tolerance checking, statistical analysis, and automated reporting
"""

import numpy as np
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QGroupBox, QFormLayout, QDoubleSpinBox,
                              QTextEdit, QTabWidget, QWidget, QTableWidget, 
                              QTableWidgetItem, QHeaderView, QMessageBox, 
                              QFileDialog, QComboBox, QCheckBox, QProgressBar,
                              QSpinBox, QListWidget, QListWidgetItem)
from PySide6.QtCore import Qt, Signal
import pyvista as pv


class QualityControlDialog(QDialog):
    """Dialog for quality control and tolerance checking"""
    
    def __init__(self, mesh_viewer, parent=None):
        super().__init__(parent)
        self.mesh_viewer = mesh_viewer
        self.tolerance_results = []
        self.statistical_results = {}
        
        self.setWindowTitle("Quality Control - DeformViz 3D")
        self.setModal(False)
        self.resize(700, 600)
        
        self._setup_ui()
    
    def _setup_ui(self):
        """Setup the quality control interface"""
        layout = QVBoxLayout(self)
        
        # Create tabbed interface
        tabs = QTabWidget()
        
        # Tab 1: Tolerance Checking
        tolerance_tab = self._create_tolerance_tab()
        tabs.addTab(tolerance_tab, "Tolerance Checking")
        
        # Tab 2: Statistical Analysis
        stats_tab = self._create_statistical_tab()
        tabs.addTab(stats_tab, "Statistical Analysis")
        
        # Tab 3: Automated Reporting
        reporting_tab = self._create_reporting_tab()
        tabs.addTab(reporting_tab, "Automated Reporting")
        
        layout.addWidget(tabs)
        
        # Results area
        results_group = QGroupBox("Quality Control Results")
        results_layout = QVBoxLayout(results_group)
        
        self.results_text = QTextEdit()
        self.results_text.setMaximumHeight(200)
        self.results_text.setReadOnly(True)
        results_layout.addWidget(self.results_text)
        
        layout.addWidget(results_group)
        
        # Close button
        close_btn = QPushButton("Close")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)
    
    def _create_tolerance_tab(self):
        """Create tolerance checking tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Dimensional tolerances
        dim_group = QGroupBox("Dimensional Tolerances")
        dim_layout = QFormLayout(dim_group)
        
        # Flatness tolerance
        self.flatness_tolerance = QDoubleSpinBox()
        self.flatness_tolerance.setRange(0.001, 100.0)
        self.flatness_tolerance.setValue(0.1)
        self.flatness_tolerance.setSuffix(" mm")
        self.flatness_tolerance.setDecimals(3)
        dim_layout.addRow("Flatness Tolerance:", self.flatness_tolerance)
        
        # Surface roughness tolerance
        self.roughness_tolerance = QDoubleSpinBox()
        self.roughness_tolerance.setRange(0.001, 10.0)
        self.roughness_tolerance.setValue(0.05)
        self.roughness_tolerance.setSuffix(" mm")
        self.roughness_tolerance.setDecimals(3)
        dim_layout.addRow("Surface Roughness (Ra):", self.roughness_tolerance)
        
        # Dimensional tolerance
        self.dimensional_tolerance = QDoubleSpinBox()
        self.dimensional_tolerance.setRange(0.001, 100.0)
        self.dimensional_tolerance.setValue(1.0)
        self.dimensional_tolerance.setSuffix(" mm")
        self.dimensional_tolerance.setDecimals(3)
        dim_layout.addRow("Dimensional Tolerance:", self.dimensional_tolerance)
        
        layout.addWidget(dim_group)
        
        # Tolerance checking options
        options_group = QGroupBox("Checking Options")
        options_layout = QVBoxLayout(options_group)
        
        self.check_flatness = QCheckBox("Check Flatness Compliance")
        self.check_flatness.setChecked(True)
        options_layout.addWidget(self.check_flatness)
        
        self.check_roughness = QCheckBox("Check Surface Roughness")
        self.check_roughness.setChecked(True)
        options_layout.addWidget(self.check_roughness)
        
        self.check_dimensions = QCheckBox("Check Dimensional Accuracy")
        self.check_dimensions.setChecked(True)
        options_layout.addWidget(self.check_dimensions)
        
        self.generate_heatmap = QCheckBox("Generate Compliance Heatmap")
        self.generate_heatmap.setChecked(True)
        options_layout.addWidget(self.generate_heatmap)
        
        layout.addWidget(options_group)
        
        # Run tolerance check
        run_tolerance_btn = QPushButton("Run Tolerance Check")
        run_tolerance_btn.clicked.connect(self._run_tolerance_check)
        layout.addWidget(run_tolerance_btn)
        
        layout.addStretch()
        return tab
    
    def _create_statistical_tab(self):
        """Create statistical analysis tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Statistical parameters
        stats_group = QGroupBox("Statistical Analysis Parameters")
        stats_layout = QFormLayout(stats_group)
        
        # Confidence level
        self.confidence_level = QComboBox()
        self.confidence_level.addItems(["90%", "95%", "99%", "99.9%"])
        self.confidence_level.setCurrentText("95%")
        stats_layout.addRow("Confidence Level:", self.confidence_level)
        
        # Sample size for statistical analysis
        self.sample_size = QSpinBox()
        self.sample_size.setRange(100, 1000000)
        self.sample_size.setValue(10000)
        stats_layout.addRow("Sample Size:", self.sample_size)
        
        layout.addWidget(stats_group)
        
        # Analysis options
        analysis_group = QGroupBox("Analysis Options")
        analysis_layout = QVBoxLayout(analysis_group)
        
        self.calc_descriptive = QCheckBox("Descriptive Statistics")
        self.calc_descriptive.setChecked(True)
        analysis_layout.addWidget(self.calc_descriptive)
        
        self.calc_distribution = QCheckBox("Distribution Analysis")
        self.calc_distribution.setChecked(True)
        analysis_layout.addWidget(self.calc_distribution)
        
        self.calc_outliers = QCheckBox("Outlier Detection")
        self.calc_outliers.setChecked(True)
        analysis_layout.addWidget(self.calc_outliers)
        
        self.calc_capability = QCheckBox("Process Capability (Cp, Cpk)")
        self.calc_capability.setChecked(False)
        analysis_layout.addWidget(self.calc_capability)
        
        layout.addWidget(analysis_group)
        
        # Run statistical analysis
        run_stats_btn = QPushButton("Run Statistical Analysis")
        run_stats_btn.clicked.connect(self._run_statistical_analysis)
        layout.addWidget(run_stats_btn)
        
        layout.addStretch()
        return tab
    
    def _create_reporting_tab(self):
        """Create automated reporting tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Report configuration
        config_group = QGroupBox("Report Configuration")
        config_layout = QFormLayout(config_group)
        
        # Report title
        self.report_title = QLabel("Quality Control Report")
        config_layout.addRow("Report Title:", self.report_title)
        
        # Report format
        self.report_format = QComboBox()
        self.report_format.addItems(["HTML", "PDF", "CSV", "JSON"])
        config_layout.addRow("Report Format:", self.report_format)
        
        # Include options
        include_group = QGroupBox("Include in Report")
        include_layout = QVBoxLayout(include_group)
        
        self.include_measurements = QCheckBox("Measurement Results")
        self.include_measurements.setChecked(True)
        include_layout.addWidget(self.include_measurements)
        
        self.include_tolerances = QCheckBox("Tolerance Check Results")
        self.include_tolerances.setChecked(True)
        include_layout.addWidget(self.include_tolerances)
        
        self.include_statistics = QCheckBox("Statistical Analysis")
        self.include_statistics.setChecked(True)
        include_layout.addWidget(self.include_statistics)
        
        self.include_screenshots = QCheckBox("Screenshots and Visualizations")
        self.include_screenshots.setChecked(True)
        include_layout.addWidget(self.include_screenshots)
        
        layout.addWidget(config_group)
        layout.addWidget(include_group)
        
        # Generate report
        generate_report_btn = QPushButton("Generate Quality Report")
        generate_report_btn.clicked.connect(self._generate_quality_report)
        layout.addWidget(generate_report_btn)
        
        layout.addStretch()
        return tab
    
    def _run_tolerance_check(self):
        """Run comprehensive tolerance checking"""
        try:
            if not self.mesh_viewer.mesh:
                QMessageBox.warning(self, "No Mesh", "Please load a mesh first.")
                return
            
            self.results_text.clear()
            self.results_text.append("🔍 Running Tolerance Check...\n")
            
            mesh = self.mesh_viewer.mesh
            results = []
            
            # Check flatness if enabled
            if self.check_flatness.isChecked():
                flatness_result = self._check_flatness_tolerance()
                results.append(flatness_result)
                self.results_text.append(f"📐 Flatness Check: {flatness_result['status']}")
                self.results_text.append(f"   Measured: {flatness_result['measured']:.3f} mm")
                self.results_text.append(f"   Tolerance: {flatness_result['tolerance']:.3f} mm\n")
            
            # Check surface roughness if enabled
            if self.check_roughness.isChecked():
                roughness_result = self._check_roughness_tolerance()
                results.append(roughness_result)
                self.results_text.append(f"🌊 Surface Roughness Check: {roughness_result['status']}")
                self.results_text.append(f"   Measured Ra: {roughness_result['measured']:.3f} mm")
                self.results_text.append(f"   Tolerance: {roughness_result['tolerance']:.3f} mm\n")
            
            # Check dimensions if enabled
            if self.check_dimensions.isChecked():
                dimension_result = self._check_dimensional_tolerance()
                results.append(dimension_result)
                self.results_text.append(f"📏 Dimensional Check: {dimension_result['status']}")
                self.results_text.append(f"   Details: {dimension_result['details']}\n")
            
            # Generate compliance heatmap if enabled
            if self.generate_heatmap.isChecked():
                self._generate_compliance_heatmap()
                self.results_text.append("🎨 Compliance heatmap generated\n")
            
            # Overall assessment
            passed_checks = sum(1 for r in results if r['status'] == 'PASS')
            total_checks = len(results)
            
            if passed_checks == total_checks:
                self.results_text.append("✅ OVERALL RESULT: ALL CHECKS PASSED")
            else:
                self.results_text.append(f"⚠️ OVERALL RESULT: {passed_checks}/{total_checks} CHECKS PASSED")
            
            self.tolerance_results = results
            self.parent().show_enhanced_status_message("Tolerance check completed", 3000, "success")
            
        except Exception as e:
            QMessageBox.critical(self, "Tolerance Check Error", f"Failed to run tolerance check: {e}")
    
    def _check_flatness_tolerance(self):
        """Check flatness against tolerance"""
        try:
            mesh = self.mesh_viewer.mesh
            points = mesh.points
            
            # Calculate best fit plane
            centroid = np.mean(points, axis=0)
            centered_points = points - centroid
            _, _, vh = np.linalg.svd(centered_points)
            normal = vh[-1]
            
            # Calculate distances to best fit plane
            distances = np.dot(centered_points, normal)
            flatness_deviation = np.max(distances) - np.min(distances)
            
            # Convert to mm for comparison
            flatness_mm = flatness_deviation * 1000
            tolerance_mm = self.flatness_tolerance.value()
            
            status = "PASS" if flatness_mm <= tolerance_mm else "FAIL"
            
            return {
                'type': 'flatness',
                'measured': flatness_mm,
                'tolerance': tolerance_mm,
                'status': status,
                'details': f"Flatness deviation: {flatness_mm:.3f} mm"
            }
            
        except Exception as e:
            return {
                'type': 'flatness',
                'measured': 0,
                'tolerance': self.flatness_tolerance.value(),
                'status': 'ERROR',
                'details': f"Error calculating flatness: {e}"
            }
    
    def _check_roughness_tolerance(self):
        """Check surface roughness against tolerance"""
        try:
            mesh = self.mesh_viewer.mesh
            points = mesh.points
            
            # Calculate surface roughness (simplified Ra calculation)
            centroid = np.mean(points, axis=0)
            centered_points = points - centroid
            _, _, vh = np.linalg.svd(centered_points)
            normal = vh[-1]
            
            distances = np.dot(centered_points, normal)
            ra = np.mean(np.abs(distances))
            
            # Convert to mm
            ra_mm = ra * 1000
            tolerance_mm = self.roughness_tolerance.value()
            
            status = "PASS" if ra_mm <= tolerance_mm else "FAIL"
            
            return {
                'type': 'roughness',
                'measured': ra_mm,
                'tolerance': tolerance_mm,
                'status': status,
                'details': f"Surface roughness Ra: {ra_mm:.3f} mm"
            }
            
        except Exception as e:
            return {
                'type': 'roughness',
                'measured': 0,
                'tolerance': self.roughness_tolerance.value(),
                'status': 'ERROR',
                'details': f"Error calculating roughness: {e}"
            }
    
    def _check_dimensional_tolerance(self):
        """Check dimensional accuracy"""
        try:
            mesh = self.mesh_viewer.mesh
            bounds = mesh.bounds
            
            # Calculate dimensions
            length = (bounds[1] - bounds[0]) * 1000  # Convert to mm
            width = (bounds[3] - bounds[2]) * 1000
            height = (bounds[5] - bounds[4]) * 1000
            
            tolerance_mm = self.dimensional_tolerance.value()
            
            # For this example, assume nominal dimensions (could be user-specified)
            # This is a simplified check - in practice, would compare against CAD model
            status = "PASS"  # Simplified for demo
            
            return {
                'type': 'dimensional',
                'measured': max(length, width, height),
                'tolerance': tolerance_mm,
                'status': status,
                'details': f"L:{length:.1f} W:{width:.1f} H:{height:.1f} mm"
            }
            
        except Exception as e:
            return {
                'type': 'dimensional',
                'measured': 0,
                'tolerance': self.dimensional_tolerance.value(),
                'status': 'ERROR',
                'details': f"Error calculating dimensions: {e}"
            }

    def _generate_compliance_heatmap(self):
        """Generate compliance heatmap visualization"""
        try:
            if not self.mesh_viewer.mesh:
                return

            mesh = self.mesh_viewer.mesh
            points = mesh.points

            # Calculate compliance based on flatness deviation
            centroid = np.mean(points, axis=0)
            centered_points = points - centroid
            _, _, vh = np.linalg.svd(centered_points)
            normal = vh[-1]

            distances = np.dot(centered_points, normal)
            tolerance_m = self.flatness_tolerance.value() / 1000.0  # Convert mm to m

            # Create compliance values (1.0 = perfect compliance, 0.0 = out of tolerance)
            compliance = np.clip(1.0 - np.abs(distances) / tolerance_m, 0.0, 1.0)

            # Add to mesh
            mesh.point_data['Compliance'] = compliance

            # Update visualization
            self.mesh_viewer.set_colormap('Compliance', colormap='RdYlGn')

        except Exception as e:
            print(f"Failed to generate compliance heatmap: {e}")

    def _run_statistical_analysis(self):
        """Run comprehensive statistical analysis"""
        try:
            if not self.mesh_viewer.mesh:
                QMessageBox.warning(self, "No Mesh", "Please load a mesh first.")
                return

            self.results_text.clear()
            self.results_text.append("📊 Running Statistical Analysis...\n")

            mesh = self.mesh_viewer.mesh

            # Get deformation data if available
            if 'DeformationMasked' in mesh.point_data:
                data = mesh.point_data['DeformationMasked']
                # Remove NaN values
                data = data[~np.isnan(data)]
            elif 'Deformation' in mesh.point_data:
                data = mesh.point_data['Deformation']
            else:
                # Use Z-coordinates as fallback
                data = mesh.points[:, 2]
                self.results_text.append("ℹ️ Using Z-coordinates for analysis (no deformation data found)\n")

            # Sample data if too large
            sample_size = min(self.sample_size.value(), len(data))
            if len(data) > sample_size:
                indices = np.random.choice(len(data), sample_size, replace=False)
                data = data[indices]

            results = {}

            # Descriptive statistics
            if self.calc_descriptive.isChecked():
                desc_stats = self._calculate_descriptive_statistics(data)
                results['descriptive'] = desc_stats

                self.results_text.append("📈 Descriptive Statistics:")
                self.results_text.append(f"   Mean: {desc_stats['mean']:.6f}")
                self.results_text.append(f"   Std Dev: {desc_stats['std']:.6f}")
                self.results_text.append(f"   Min: {desc_stats['min']:.6f}")
                self.results_text.append(f"   Max: {desc_stats['max']:.6f}")
                self.results_text.append(f"   Range: {desc_stats['range']:.6f}")
                self.results_text.append(f"   Skewness: {desc_stats['skewness']:.3f}")
                self.results_text.append(f"   Kurtosis: {desc_stats['kurtosis']:.3f}\n")

            # Distribution analysis
            if self.calc_distribution.isChecked():
                dist_analysis = self._analyze_distribution(data)
                results['distribution'] = dist_analysis

                self.results_text.append("📊 Distribution Analysis:")
                self.results_text.append(f"   Normality Test: {dist_analysis['normality']}")
                self.results_text.append(f"   25th Percentile: {dist_analysis['q25']:.6f}")
                self.results_text.append(f"   50th Percentile: {dist_analysis['q50']:.6f}")
                self.results_text.append(f"   75th Percentile: {dist_analysis['q75']:.6f}")
                self.results_text.append(f"   IQR: {dist_analysis['iqr']:.6f}\n")

            # Outlier detection
            if self.calc_outliers.isChecked():
                outlier_analysis = self._detect_outliers(data)
                results['outliers'] = outlier_analysis

                self.results_text.append("🎯 Outlier Detection:")
                self.results_text.append(f"   Outliers Found: {outlier_analysis['count']}")
                self.results_text.append(f"   Percentage: {outlier_analysis['percentage']:.2f}%")
                self.results_text.append(f"   Method: {outlier_analysis['method']}\n")

            # Process capability
            if self.calc_capability.isChecked():
                capability = self._calculate_process_capability(data)
                results['capability'] = capability

                self.results_text.append("⚙️ Process Capability:")
                self.results_text.append(f"   Cp: {capability['cp']:.3f}")
                self.results_text.append(f"   Cpk: {capability['cpk']:.3f}")
                self.results_text.append(f"   Assessment: {capability['assessment']}\n")

            self.statistical_results = results
            self.results_text.append("✅ Statistical analysis completed")

            self.parent().show_enhanced_status_message("Statistical analysis completed", 3000, "success")

        except Exception as e:
            QMessageBox.critical(self, "Statistical Analysis Error", f"Failed to run statistical analysis: {e}")

    def _calculate_descriptive_statistics(self, data):
        """Calculate descriptive statistics"""
        from scipy import stats

        return {
            'mean': np.mean(data),
            'std': np.std(data),
            'min': np.min(data),
            'max': np.max(data),
            'range': np.max(data) - np.min(data),
            'skewness': stats.skew(data),
            'kurtosis': stats.kurtosis(data),
            'count': len(data)
        }

    def _analyze_distribution(self, data):
        """Analyze data distribution"""
        from scipy import stats

        # Normality test
        _, p_value = stats.shapiro(data[:5000])  # Shapiro-Wilk test (limited sample)
        normality = "Normal" if p_value > 0.05 else "Non-normal"

        # Percentiles
        q25, q50, q75 = np.percentile(data, [25, 50, 75])

        return {
            'normality': normality,
            'p_value': p_value,
            'q25': q25,
            'q50': q50,
            'q75': q75,
            'iqr': q75 - q25
        }

    def _detect_outliers(self, data):
        """Detect outliers using IQR method"""
        q25, q75 = np.percentile(data, [25, 75])
        iqr = q75 - q25

        # IQR method
        lower_bound = q25 - 1.5 * iqr
        upper_bound = q75 + 1.5 * iqr

        outliers = (data < lower_bound) | (data > upper_bound)
        outlier_count = np.sum(outliers)

        return {
            'count': outlier_count,
            'percentage': (outlier_count / len(data)) * 100,
            'method': 'IQR (1.5 × IQR)',
            'lower_bound': lower_bound,
            'upper_bound': upper_bound
        }

    def _calculate_process_capability(self, data):
        """Calculate process capability indices"""
        # Simplified capability calculation
        # In practice, would need specification limits

        mean = np.mean(data)
        std = np.std(data)

        # Assume tolerance is ±3σ for demonstration
        usl = mean + 3 * std  # Upper specification limit
        lsl = mean - 3 * std  # Lower specification limit

        # Calculate Cp and Cpk
        cp = (usl - lsl) / (6 * std) if std > 0 else 0
        cpk_upper = (usl - mean) / (3 * std) if std > 0 else 0
        cpk_lower = (mean - lsl) / (3 * std) if std > 0 else 0
        cpk = min(cpk_upper, cpk_lower)

        # Assessment
        if cpk >= 1.33:
            assessment = "Excellent"
        elif cpk >= 1.0:
            assessment = "Adequate"
        elif cpk >= 0.67:
            assessment = "Poor"
        else:
            assessment = "Inadequate"

        return {
            'cp': cp,
            'cpk': cpk,
            'assessment': assessment,
            'usl': usl,
            'lsl': lsl
        }

    def _generate_quality_report(self):
        """Generate comprehensive quality report"""
        try:
            if not self.tolerance_results and not self.statistical_results:
                QMessageBox.warning(self, "No Data", "Please run tolerance check and/or statistical analysis first.")
                return

            # Get save location
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Save Quality Report", "",
                "HTML Files (*.html);;PDF Files (*.pdf);;CSV Files (*.csv);;JSON Files (*.json)")

            if not file_path:
                return

            format_type = self.report_format.currentText().lower()

            if format_type == "html":
                self._generate_html_report(file_path)
            elif format_type == "csv":
                self._generate_csv_report(file_path)
            elif format_type == "json":
                self._generate_json_report(file_path)
            else:
                QMessageBox.information(self, "Format Not Implemented",
                    f"{format_type.upper()} format not yet implemented. Using HTML instead.")
                self._generate_html_report(file_path.replace('.pdf', '.html'))

            QMessageBox.information(self, "Report Generated", f"Quality report saved to: {file_path}")
            self.parent().show_enhanced_status_message("Quality report generated", 3000, "success")

        except Exception as e:
            QMessageBox.critical(self, "Report Error", f"Failed to generate report: {e}")

    def _generate_html_report(self, file_path):
        """Generate HTML quality report"""
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Quality Control Report - DeformViz 3D</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; }}
                .pass {{ color: green; font-weight: bold; }}
                .fail {{ color: red; font-weight: bold; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Quality Control Report</h1>
                <p>Generated by DeformViz 3D</p>
                <p>Date: {np.datetime64('now')}</p>
            </div>
        """

        # Add tolerance results
        if self.tolerance_results and self.include_tolerances.isChecked():
            html_content += """
            <div class="section">
                <h2>Tolerance Check Results</h2>
                <table>
                    <tr><th>Check Type</th><th>Measured</th><th>Tolerance</th><th>Status</th></tr>
            """

            for result in self.tolerance_results:
                status_class = "pass" if result['status'] == 'PASS' else "fail"
                html_content += f"""
                    <tr>
                        <td>{result['type'].title()}</td>
                        <td>{result['measured']:.3f}</td>
                        <td>{result['tolerance']:.3f}</td>
                        <td class="{status_class}">{result['status']}</td>
                    </tr>
                """

            html_content += "</table></div>"

        # Add statistical results
        if self.statistical_results and self.include_statistics.isChecked():
            html_content += """
            <div class="section">
                <h2>Statistical Analysis Results</h2>
            """

            if 'descriptive' in self.statistical_results:
                stats = self.statistical_results['descriptive']
                html_content += f"""
                <h3>Descriptive Statistics</h3>
                <table>
                    <tr><td>Mean</td><td>{stats['mean']:.6f}</td></tr>
                    <tr><td>Standard Deviation</td><td>{stats['std']:.6f}</td></tr>
                    <tr><td>Minimum</td><td>{stats['min']:.6f}</td></tr>
                    <tr><td>Maximum</td><td>{stats['max']:.6f}</td></tr>
                    <tr><td>Range</td><td>{stats['range']:.6f}</td></tr>
                </table>
                """

            html_content += "</div>"

        html_content += """
        </body>
        </html>
        """

        with open(file_path, 'w') as f:
            f.write(html_content)

    def _generate_csv_report(self, file_path):
        """Generate CSV quality report"""
        import csv

        with open(file_path, 'w', newline='') as csvfile:
            writer = csv.writer(csvfile)

            # Header
            writer.writerow(['Quality Control Report - DeformViz 3D'])
            writer.writerow(['Generated:', str(np.datetime64('now'))])
            writer.writerow([])

            # Tolerance results
            if self.tolerance_results:
                writer.writerow(['Tolerance Check Results'])
                writer.writerow(['Type', 'Measured', 'Tolerance', 'Status'])

                for result in self.tolerance_results:
                    writer.writerow([
                        result['type'].title(),
                        f"{result['measured']:.3f}",
                        f"{result['tolerance']:.3f}",
                        result['status']
                    ])
                writer.writerow([])

            # Statistical results
            if self.statistical_results and 'descriptive' in self.statistical_results:
                writer.writerow(['Statistical Analysis Results'])
                stats = self.statistical_results['descriptive']

                writer.writerow(['Statistic', 'Value'])
                writer.writerow(['Mean', f"{stats['mean']:.6f}"])
                writer.writerow(['Standard Deviation', f"{stats['std']:.6f}"])
                writer.writerow(['Minimum', f"{stats['min']:.6f}"])
                writer.writerow(['Maximum', f"{stats['max']:.6f}"])
                writer.writerow(['Range', f"{stats['range']:.6f}"])

    def _generate_json_report(self, file_path):
        """Generate JSON quality report"""
        import json

        report_data = {
            'title': 'Quality Control Report - DeformViz 3D',
            'generated': str(np.datetime64('now')),
            'tolerance_results': self.tolerance_results,
            'statistical_results': self.statistical_results
        }

        with open(file_path, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)
