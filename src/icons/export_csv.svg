<?xml version="1.0" encoding="UTF-8"?>
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Export CSV icon with spreadsheet -->
  
  <!-- Document -->
  <rect x="4" y="3" width="14" height="18" rx="1" fill="none" stroke="currentColor" stroke-width="1.5"/>
  
  <!-- CSV grid -->
  <line x1="6" y1="7" x2="16" y2="7" stroke="currentColor" stroke-width="0.5"/>
  <line x1="6" y1="9" x2="16" y2="9" stroke="currentColor" stroke-width="0.5"/>
  <line x1="6" y1="11" x2="16" y2="11" stroke="currentColor" stroke-width="0.5"/>
  <line x1="6" y1="13" x2="16" y2="13" stroke="currentColor" stroke-width="0.5"/>
  <line x1="6" y1="15" x2="16" y2="15" stroke="currentColor" stroke-width="0.5"/>
  
  <line x1="8" y1="6" x2="8" y2="16" stroke="currentColor" stroke-width="0.5"/>
  <line x1="11" y1="6" x2="11" y2="16" stroke="currentColor" stroke-width="0.5"/>
  <line x1="14" y1="6" x2="14" y2="16" stroke="currentColor" stroke-width="0.5"/>
  
  <!-- Header row -->
  <rect x="6" y="6" width="10" height="1" fill="currentColor" opacity="0.3"/>
  
  <!-- CSV label -->
  <text x="11" y="19" text-anchor="middle" font-size="6" fill="currentColor">CSV</text>
  
  <!-- Export arrow -->
  <path d="M18 12 L22 12 M20 10 L22 12 L20 14" stroke="currentColor" stroke-width="2" fill="none"/>
</svg>
