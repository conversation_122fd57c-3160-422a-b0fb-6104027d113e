<?xml version="1.0" encoding="UTF-8"?>
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Define plane icon with plane and normal vector -->
  
  <!-- Plane surface -->
  <path d="M4 16 L20 8 L20 12 L4 20 Z" fill="currentColor" opacity="0.2" stroke="currentColor" stroke-width="1"/>
  
  <!-- Plane grid -->
  <line x1="8" y1="18" x2="16" y2="10" stroke="currentColor" stroke-width="0.5" opacity="0.5"/>
  <line x1="6" y1="17" x2="18" y2="9" stroke="currentColor" stroke-width="0.5" opacity="0.5"/>
  <line x1="10" y1="19" x2="14" y2="11" stroke="currentColor" stroke-width="0.5" opacity="0.5"/>
  
  <!-- Normal vector -->
  <path d="M12 14 L12 4" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
  <path d="M10 6 L12 4 L14 6" stroke="currentColor" stroke-width="1.5" fill="none" stroke-linecap="round"/>
  
  <!-- Three definition points -->
  <circle cx="6" cy="18" r="1.5" fill="currentColor"/>
  <circle cx="18" cy="10" r="1.5" fill="currentColor"/>
  <circle cx="12" cy="14" r="1.5" fill="currentColor"/>
  
  <!-- Point labels -->
  <text x="4" y="20" font-size="6" fill="currentColor">P1</text>
  <text x="19" y="8" font-size="6" fill="currentColor">P2</text>
  <text x="13" y="16" font-size="6" fill="currentColor">P3</text>
</svg>
