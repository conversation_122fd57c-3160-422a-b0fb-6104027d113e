<?xml version="1.0" encoding="UTF-8"?>
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Heatmap icon with color gradient and mesh -->
  
  <!-- 3D mesh surface -->
  <path d="M4 16 L12 8 L20 16 L12 20 Z" fill="url(#heatmapGradient)" stroke="currentColor" stroke-width="1"/>
  
  <!-- Mesh grid lines -->
  <line x1="4" y1="16" x2="20" y2="16" stroke="currentColor" stroke-width="0.5" opacity="0.7"/>
  <line x1="8" y1="12" x2="16" y2="20" stroke="currentColor" stroke-width="0.5" opacity="0.7"/>
  <line x1="12" y1="8" x2="12" y2="20" stroke="currentColor" stroke-width="0.5" opacity="0.7"/>
  <line x1="6" y1="14" x2="18" y2="18" stroke="currentColor" stroke-width="0.5" opacity="0.7"/>
  
  <!-- Color scale bar -->
  <rect x="21" y="6" width="2" height="12" fill="url(#colorScale)" stroke="currentColor" stroke-width="0.5"/>
  
  <!-- Scale labels -->
  <text x="20" y="7" text-anchor="end" font-size="4" fill="currentColor">+</text>
  <text x="20" y="12" text-anchor="end" font-size="4" fill="currentColor">0</text>
  <text x="20" y="17" text-anchor="end" font-size="4" fill="currentColor">-</text>
  
  <!-- Gradient definitions -->
  <defs>
    <linearGradient id="heatmapGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff0000;stop-opacity:0.8"/>
      <stop offset="50%" style="stop-color:#00ff00;stop-opacity:0.8"/>
      <stop offset="100%" style="stop-color:#0000ff;stop-opacity:0.8"/>
    </linearGradient>
    <linearGradient id="colorScale" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ff0000"/>
      <stop offset="50%" style="stop-color:#00ff00"/>
      <stop offset="100%" style="stop-color:#0000ff"/>
    </linearGradient>
  </defs>
</svg>
