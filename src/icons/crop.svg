<?xml version="1.0" encoding="UTF-8"?>
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Crop icon with selection box and mesh -->
  
  <!-- Original mesh (background) -->
  <path d="M2 18 L10 10 L18 18 L10 22 Z" fill="none" stroke="currentColor" stroke-width="1" opacity="0.3"/>
  
  <!-- Crop selection box -->
  <rect x="6" y="6" width="12" height="8" fill="none" stroke="currentColor" stroke-width="2" stroke-dasharray="3,2"/>
  
  <!-- Crop handles -->
  <rect x="5" y="5" width="2" height="2" fill="currentColor"/>
  <rect x="17" y="5" width="2" height="2" fill="currentColor"/>
  <rect x="5" y="13" width="2" height="2" fill="currentColor"/>
  <rect x="17" y="13" width="2" height="2" fill="currentColor"/>
  
  <!-- Cropped mesh (inside box) -->
  <path d="M8 12 L12 8 L16 12 L12 16 Z" fill="currentColor" opacity="0.2" stroke="currentColor" stroke-width="1"/>
  
  <!-- Grid lines -->
  <line x1="8" y1="12" x2="16" y2="12" stroke="currentColor" stroke-width="0.5" opacity="0.6"/>
  <line x1="12" y1="8" x2="12" y2="16" stroke="currentColor" stroke-width="0.5" opacity="0.6"/>
  
  <!-- Crop tool -->
  <path d="M20 2 L22 4 L20 6" stroke="currentColor" stroke-width="1.5" fill="none"/>
  <line x1="21" y1="3" x2="19" y2="5" stroke="currentColor" stroke-width="1"/>
</svg>
