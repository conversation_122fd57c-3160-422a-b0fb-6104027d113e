#!/usr/bin/env python3
"""
Test the icon fix to ensure all icons work
"""

import sys
import os

def test_all_icons_exist():
    """Test that all required icons exist"""
    print("🔍 Testing All Icon Files...")
    
    try:
        # List of all icons used in the toolbar
        required_icons = [
            "wizard.svg",
            "load.svg", 
            "save.svg",
            "export.svg",
            "screenshot.svg",
            "split_screen.svg",
            "plane.svg",
            "crop.svg",
            "process_mesh.svg",
            "analyze.svg",
            "measurement.svg",
            "heatmap.svg",
            "vector_field.svg",
            "color_picker.svg",
            "export_csv.svg",
            "save_session.svg",
            "load_session.svg",
            "undo.svg",
            "batch_process.svg",
            "quality_control.svg",
            "advanced_report.svg",
            "settings.svg",
            "help.svg"
        ]
        
        missing_icons = []
        existing_icons = []
        
        for icon in required_icons:
            icon_path = os.path.join("src", "icons", icon)
            if os.path.exists(icon_path):
                existing_icons.append(icon)
                print(f"✅ {icon}")
            else:
                missing_icons.append(icon)
                print(f"❌ {icon} - MISSING")
        
        print(f"\n📊 Summary: {len(existing_icons)}/{len(required_icons)} icons found")
        
        if missing_icons:
            print(f"⚠️ Missing icons: {missing_icons}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Icon existence test failed: {e}")
        return False

def test_icon_loading_function():
    """Test the updated icon loading function"""
    print("\n🔧 Testing Icon Loading Function...")
    
    try:
        # Read the updated function
        with open("src/gui/mainwindow.py", 'r') as f:
            content = f.read()
        
        # Check that original name is tried first
        if 'os.path.join("src", "icons", name)' in content:
            print("✅ Icon function tries original name first")
        else:
            print("❌ Icon function doesn't try original name first")
            return False
        
        # Check that it doesn't create colored squares by default
        if 'return QIcon()' in content and 'pixmap.fill' not in content:
            print("✅ Icon function returns empty icon instead of colored square")
        else:
            print("❌ Icon function still creates colored squares")
            return False
        
        # Check for error handling
        if 'try:' in content and 'except Exception' in content:
            print("✅ Icon function has proper error handling")
        else:
            print("❌ Icon function missing error handling")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Icon loading function test failed: {e}")
        return False

def simulate_icon_loading():
    """Simulate the icon loading for common icons"""
    print("\n🎨 Simulating Icon Loading...")
    
    try:
        test_icons = ["wizard.svg", "load.svg", "advanced_report.svg"]
        
        for icon_name in test_icons:
            print(f"\nTesting: {icon_name}")
            
            # Simulate the search paths
            base_name = icon_name.replace('.svg', '').replace('.png', '')
            icon_paths = [
                os.path.join("src", "icons", icon_name),  # Original name first
                os.path.join("src", "icons", f"{base_name}.png"),  # PNG variant
                os.path.join("src", "icons", f"{base_name}.svg"),  # SVG variant
                os.path.join("data", "icons", icon_name),
                os.path.join("icons", icon_name)
            ]
            
            found_path = None
            for path in icon_paths:
                if os.path.exists(path):
                    found_path = path
                    print(f"  ✅ Found: {path}")
                    break
                else:
                    print(f"  ❌ Not found: {path}")
            
            if found_path:
                print(f"  ✅ {icon_name} should load correctly")
            else:
                print(f"  ❌ {icon_name} will not load")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Icon loading simulation failed: {e}")
        return False

def create_missing_png_icons():
    """Create PNG versions for any missing icons"""
    print("\n🎨 Creating Missing PNG Icons...")
    
    try:
        from PIL import Image, ImageDraw
        
        # Icons that might need PNG versions
        icons_needing_png = ["wizard", "load", "save", "export"]
        
        created_count = 0
        
        for icon_name in icons_needing_png:
            png_path = f"src/icons/{icon_name}.png"
            svg_path = f"src/icons/{icon_name}.svg"
            
            # Only create if PNG doesn't exist but SVG does
            if not os.path.exists(png_path) and os.path.exists(svg_path):
                try:
                    # Create a simple 24x24 icon
                    img = Image.new('RGBA', (24, 24), (0, 0, 0, 0))
                    draw = ImageDraw.Draw(img)
                    
                    # Simple icon based on name
                    if icon_name == "wizard":
                        # Wizard hat
                        draw.polygon([(12, 4), (8, 16), (16, 16)], fill=(128, 0, 128))
                        draw.ellipse([10, 6, 14, 10], fill=(255, 255, 0))
                    elif icon_name == "load":
                        # Folder
                        draw.rectangle([4, 8, 20, 18], fill=(255, 215, 0), outline=(0, 0, 0))
                        draw.rectangle([4, 6, 12, 10], fill=(255, 215, 0), outline=(0, 0, 0))
                    elif icon_name == "save":
                        # Disk
                        draw.rectangle([4, 4, 20, 20], fill=(100, 100, 100), outline=(0, 0, 0))
                        draw.rectangle([6, 6, 18, 12], fill=(0, 0, 0))
                        draw.rectangle([8, 14, 16, 18], fill=(200, 200, 200))
                    elif icon_name == "export":
                        # Arrow out of box
                        draw.rectangle([4, 10, 16, 20], fill=(100, 100, 100), outline=(0, 0, 0))
                        draw.polygon([(12, 4), (8, 8), (16, 8)], fill=(0, 128, 0))
                        draw.rectangle([11, 8, 13, 14], fill=(0, 128, 0))
                    
                    img.save(png_path, "PNG")
                    print(f"✅ Created: {png_path}")
                    created_count += 1
                    
                except Exception as e:
                    print(f"⚠️ Failed to create {png_path}: {e}")
        
        if created_count > 0:
            print(f"✅ Created {created_count} PNG icons")
        else:
            print("ℹ️ No PNG icons needed to be created")
        
        return True
        
    except ImportError:
        print("⚠️ PIL not available for creating PNG icons")
        return True  # Not a failure
    except Exception as e:
        print(f"❌ PNG icon creation failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 DeformViz 3D Icon Fix Test")
    print("=" * 40)
    
    # Run tests
    tests = [
        ("All Icons Exist", test_all_icons_exist),
        ("Icon Loading Function", test_icon_loading_function),
        ("Icon Loading Simulation", simulate_icon_loading),
        ("Create Missing PNG Icons", create_missing_png_icons)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("📋 TEST SUMMARY")
    print("=" * 40)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n📊 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ICON FIX SUCCESSFUL!")
        print("   ✅ All required icons exist")
        print("   ✅ Icon loading function fixed")
        print("   ✅ No more colored squares")
        print("   ✅ Original icons preserved")
        print("\n🚀 Restart DeformViz 3D - all icons should be visible!")
    else:
        print("\n⚠️ Some icon issues remain")
        print("💡 Try restarting DeformViz 3D to see if icons appear")
    
    print("\n" + "=" * 40)
    print("🔧 WHAT WAS FIXED:")
    print("=" * 40)
    print("❌ PROBLEM: PNG-first loading broke existing SVG icons")
    print("✅ SOLUTION: Original name first, then PNG/SVG variants")
    print("✅ RESULT: All existing icons work + new PNG icons available")
    print("✅ BENEFIT: Advanced Reports icon now visible")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
