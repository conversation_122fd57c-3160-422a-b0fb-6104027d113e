#!/bin/bash

# Test mesh loading with proper Qt environment
echo "🔍 Testing Mesh Loading with Qt Environment"

# Set Qt environment like our working startup script
export QT_PLUGIN_PATH="$CONDA_PREFIX/lib/python3.13/site-packages/PySide6/Qt/plugins"
export QT_QPA_PLATFORM_PLUGIN_PATH="$CONDA_PREFIX/lib/python3.13/site-packages/PySide6/Qt/plugins/platforms"
export QT_QPA_PLATFORM=minimal

# Disable GTK and system theme integration
export QT_QPA_PLATFORMTHEME=""
export QT_STYLE_OVERRIDE=""
export GTK_THEME=""

# Graphics settings for headless environment
export MESA_GL_VERSION_OVERRIDE=3.3
export LIBGL_ALWAYS_SOFTWARE=1

echo "🧪 Running mesh loading tests..."
python test_mesh_loading.py
