<?xml version="1.0" encoding="UTF-8"?>
<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="undoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF7675;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E17055;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Undo arrow curve -->
  <path d="M9 7 L4 12 L9 17" stroke="url(#undoGradient)" stroke-width="3" 
        stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  
  <!-- Arrow curve -->
  <path d="M4 12 Q8 8 12 8 Q16 8 18 12 Q20 16 18 20" stroke="url(#undoGradient)" 
        stroke-width="2.5" stroke-linecap="round" fill="none"/>
  
  <!-- Action states -->
  <g opacity="0.6">
    <!-- Before state -->
    <circle cx="18" cy="8" r="2" fill="#E17055" stroke="#FFFFFF" stroke-width="1"/>
    <text x="18" y="9" text-anchor="middle" fill="#FFFFFF" font-family="Arial" font-size="2">B</text>
    
    <!-- After state (faded) -->
    <circle cx="18" cy="16" r="2" fill="#DDD" stroke="#999" stroke-width="1"/>
    <text x="18" y="17" text-anchor="middle" fill="#666" font-family="Arial" font-size="2">A</text>
  </g>
  
  <!-- Undo symbol -->
  <circle cx="12" cy="4" r="2" fill="url(#undoGradient)" stroke="#FFFFFF" stroke-width="1"/>
  <path d="M11 4 Q12 3 13 4" stroke="#FFFFFF" stroke-width="1" fill="none"/>
  <path d="M12.5 3.5 L13 4 L12.5 4.5" stroke="#FFFFFF" stroke-width="0.8" fill="none" stroke-linecap="round"/>
  
  <!-- History indicator -->
  <g stroke="#E17055" stroke-width="1" opacity="0.5">
    <path d="M6 20 L8 20 M10 20 L12 20 M14 20 L16 20"/>
    <circle cx="7" cy="20" r="0.5" fill="#E17055"/>
    <circle cx="11" cy="20" r="0.5" fill="#E17055"/>
    <circle cx="15" cy="20" r="0.5" fill="#E17055"/>
  </g>
</svg>