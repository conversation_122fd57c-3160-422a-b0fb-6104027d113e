<?xml version="1.0" encoding="UTF-8"?>
<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="sliceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E17055;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#D63031;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  
  <!-- 3D object being sliced -->
  <g fill="#74B9FF" stroke="#0984E3" stroke-width="1">
    <!-- Left part -->
    <path d="M4 8 L10 6 L10 14 L4 16 Z" opacity="0.8"/>
    <path d="M4 8 L4 16 L10 14 L10 6 Z" fill="#A29BFE"/>
    
    <!-- Right part -->
    <path d="M14 6 L20 8 L20 16 L14 14 Z" opacity="0.8"/>
    <path d="M14 6 L14 14 L20 16 L20 8 Z" fill="#A29BFE"/>
  </g>
  
  <!-- Slicing plane -->
  <rect x="10" y="4" width="4" height="16" fill="url(#sliceGradient)" opacity="0.9"/>
  <path d="M12 4 L12 20" stroke="#FFFFFF" stroke-width="2" stroke-dasharray="2,2"/>
  
  <!-- Slice indicator -->
  <circle cx="12" cy="2" r="1.5" fill="#D63031"/>
  <path d="M10.5 2 L13.5 2" stroke="#FFFFFF" stroke-width="1" stroke-linecap="round"/>
  
  <!-- Cut lines -->
  <g stroke="#D63031" stroke-width="1.5" opacity="0.7">
    <path d="M8 10 L10 10"/>
    <path d="M14 10 L16 10"/>
    <path d="M8 12 L10 12"/>
    <path d="M14 12 L16 12"/>
  </g>
</svg>