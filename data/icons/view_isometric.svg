<?xml version="1.0" encoding="UTF-8"?>
<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="cubeTop" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#DDD6FE"/>
      <stop offset="100%" style="stop-color:#A29BFE"/>
    </linearGradient>
    <linearGradient id="cubeLeft" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#A29BFE"/>
      <stop offset="100%" style="stop-color:#6C5CE7"/>
    </linearGradient>
    <linearGradient id="cubeRight" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6C5CE7"/>
      <stop offset="100%" style="stop-color:#5A4FCF"/>
    </linearGradient>
  </defs>
  
  <!-- 3D Cube -->
  <path d="M6 8 L12 5 L18 8 L12 11 Z" fill="url(#cubeTop)" stroke="#5A4FCF" stroke-width="1"/>
  <path d="M6 8 L6 16 L12 19 L12 11 Z" fill="url(#cubeLeft)" stroke="#5A4FCF" stroke-width="1"/>
  <path d="M12 11 L12 19 L18 16 L18 8 Z" fill="url(#cubeRight)" stroke="#5A4FCF" stroke-width="1"/>
</svg>