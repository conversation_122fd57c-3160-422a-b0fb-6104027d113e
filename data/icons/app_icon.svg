<?xml version="1.0" encoding="UTF-8"?>
<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="meshGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E5BBA;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="heatGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#FF4444;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#FFAA00;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#44FF44;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="32" cy="32" r="30" fill="url(#meshGradient)" stroke="#1A365D" stroke-width="2"/>
  
  <!-- 3D mesh representation -->
  <g stroke="#FFFFFF" stroke-width="1.5" fill="none">
    <!-- Grid lines -->
    <path d="M12 20 L52 20 M12 28 L52 28 M12 36 L52 36 M12 44 L52 44"/>
    <path d="M16 16 L16 48 M24 16 L24 48 M32 16 L32 48 M40 16 L40 48 M48 16 L48 48"/>
  </g>
  
  <!-- Heatmap overlay -->
  <rect x="16" y="20" width="32" height="24" fill="url(#heatGradient)" opacity="0.6" rx="2"/>
  
  <!-- 3D effect lines -->
  <g stroke="#FFFFFF" stroke-width="1" fill="none" opacity="0.7">
    <path d="M20 16 L24 12 M28 16 L32 12 M36 16 L40 12 M44 16 L48 12"/>
    <path d="M52 24 L56 20 M52 32 L56 28 M52 40 L56 36"/>
  </g>
  
  <!-- Analysis symbol -->
  <circle cx="45" cy="19" r="6" fill="#FF6B35" stroke="#FFFFFF" stroke-width="1"/>
  <text x="45" y="23" text-anchor="middle" fill="white" font-family="Arial" font-size="8" font-weight="bold">3D</text>
</svg>