<?xml version="1.0" encoding="UTF-8"?>
<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="meshGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#27AE60;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#229954;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="processGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3498DB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2980B9;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Original mesh (rough/noisy) -->
  <g stroke="#E74C3C" stroke-width="1" fill="none" opacity="0.6">
    <!-- Irregular mesh lines -->
    <path d="M3 6 L7 5 L11 7 L15 6 L19 8"/>
    <path d="M3 10 L6 9 L10 11 L14 10 L18 12"/>
    <path d="M3 14 L8 13 L12 15 L16 14 L20 16"/>
    <path d="M3 18 L7 17 L11 19 L15 18 L19 20"/>
    <!-- Vertical lines -->
    <path d="M3 6 L3 18"/>
    <path d="M7 5 L6 9 L8 13 L7 17"/>
    <path d="M11 7 L10 11 L12 15 L11 19"/>
    <path d="M15 6 L14 10 L16 14 L15 18"/>
    <path d="M19 8 L18 12 L20 16 L19 20"/>
  </g>
  
  <!-- Processing arrow -->
  <circle cx="12" cy="12" r="8" fill="url(#processGradient)" opacity="0.9"/>
  <path d="M8 12 L16 12 M14 10 L16 12 L14 14" stroke="#FFFFFF" stroke-width="2" 
        stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  
  <!-- Processed mesh (clean/smooth) -->
  <g stroke="url(#meshGradient)" stroke-width="1.5" fill="none" opacity="0.8">
    <!-- Regular mesh lines -->
    <path d="M3 6 L21 6"/>
    <path d="M3 10 L21 10"/>
    <path d="M3 14 L21 14"/>
    <path d="M3 18 L21 18"/>
    <!-- Vertical lines -->
    <path d="M3 6 L3 18"/>
    <path d="M7 6 L7 18"/>
    <path d="M11 6 L11 18"/>
    <path d="M15 6 L15 18"/>
    <path d="M19 6 L19 18"/>
    <path d="M21 6 L21 18"/>
  </g>
  
  <!-- Processing tools indicators -->
  <g fill="#FFFFFF" stroke="#2980B9" stroke-width="1">
    <!-- Clean tool -->
    <circle cx="6" cy="3" r="1.5"/>
    <path d="M5 3 L7 3 M6 2 L6 4" stroke="#2980B9" stroke-width="1" stroke-linecap="round"/>
    
    <!-- Smooth tool -->
    <circle cx="12" cy="3" r="1.5"/>
    <path d="M10.5 3 Q12 2 13.5 3" stroke="#2980B9" stroke-width="1" fill="none"/>
    
    <!-- Repair tool -->
    <circle cx="18" cy="3" r="1.5"/>
    <path d="M17 2.5 L17.5 3.5 L19 2.5" stroke="#2980B9" stroke-width="1" fill="none" stroke-linecap="round"/>
  </g>
  
  <!-- Progress indicator -->
  <rect x="4" y="21" width="16" height="2" fill="#E9ECEF" rx="1"/>
  <rect x="4" y="21" width="12" height="2" fill="url(#processGradient)" rx="1"/>
</svg>