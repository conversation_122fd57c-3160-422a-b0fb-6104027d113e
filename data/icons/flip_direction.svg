<?xml version="1.0" encoding="UTF-8"?>
<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="flipGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B35;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E55A2B;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Reference plane -->
  <rect x="4" y="10" width="16" height="4" fill="#A29BFE" stroke="#6C5CE7" stroke-width="1" opacity="0.7"/>
  
  <!-- Direction arrows (flip) -->
  <g stroke="#FFFFFF" stroke-width="2" fill="url(#flipGradient)">
    <!-- Up arrow -->
    <path d="M8 8 L12 4 L16 8 L14 8 L14 10 L10 10 L10 8 Z"/>
    <!-- Down arrow -->
    <path d="M16 16 L12 20 L8 16 L10 16 L10 14 L14 14 L14 16 Z"/>
  </g>
  
  <!-- Flip indicator -->
  <circle cx="18" cy="6" r="3" fill="#FF6B35" stroke="#FFFFFF" stroke-width="1"/>
  <path d="M16.5 6 L19.5 6 M18 4.5 L18 7.5" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round"/>
  
  <!-- Curved arrow showing flip -->
  <path d="M6 6 Q12 2 18 6" stroke="#E55A2B" stroke-width="2" fill="none" stroke-linecap="round"/>
  <path d="M16 4 L18 6 L16 8" stroke="#E55A2B" stroke-width="1.5" fill="none" stroke-linecap="round"/>
</svg>