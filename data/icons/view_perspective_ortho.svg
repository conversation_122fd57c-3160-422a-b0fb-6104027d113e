<?xml version="1.0" encoding="UTF-8"?>
<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="projGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6C5CE7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A29BFE;stop-opacity:1" />
    </linearGradient>
  </defs>
  <!-- Perspective view (left side) -->
  <path d="M2 6 L10 4 L10 12 L2 14 Z" fill="url(#projGradient)" stroke="#5A4FCF" stroke-width="1"/>
  <path d="M10 4 L18 6 L18 14 L10 12 Z" fill="#A29BFE" stroke="#5A4FCF" stroke-width="1"/>
  <path d="M2 6 L10 4 L18 6 L10 8 Z" fill="#DDD6FE" stroke="#5A4FCF" stroke-width="1"/>
  
  <!-- Orthographic view (right side) -->
  <rect x="14" y="16" width="8" height="6" fill="#74B9FF" stroke="#0984E3" stroke-width="1"/>
  <rect x="16" y="14" width="8" height="6" fill="#A29BFE" stroke="#6C5CE7" stroke-width="1"/>
  <rect x="14" y="14" width="8" height="2" fill="#DDD6FE" stroke="#6C5CE7" stroke-width="1"/>
  
  <!-- Toggle indicator -->
  <circle cx="12" cy="12" r="2" fill="#FF7675" stroke="#E17055" stroke-width="1"/>
  <path d="M10 12 L14 12 M12 10 L12 14" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round"/>
</svg>