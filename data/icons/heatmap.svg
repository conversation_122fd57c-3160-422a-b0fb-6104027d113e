<?xml version="1.0" encoding="UTF-8"?>
<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heatmapGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#0000FF"/>
      <stop offset="25%" style="stop-color:#00FFFF"/>
      <stop offset="50%" style="stop-color:#00FF00"/>
      <stop offset="75%" style="stop-color:#FFFF00"/>
      <stop offset="100%" style="stop-color:#FF0000"/>
    </linearGradient>
    <radialGradient id="hotspot1" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FF0000;stop-opacity:1"/>
      <stop offset="100%" style="stop-color:#FF0000;stop-opacity:0"/>
    </radialGradient>
    <radialGradient id="hotspot2" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFFF00;stop-opacity:1"/>
      <stop offset="100%" style="stop-color:#FFFF00;stop-opacity:0"/>
    </radialGradient>
  </defs>
  
  <!-- Mesh grid -->
  <g stroke="#333" stroke-width="0.5" fill="none" opacity="0.3">
    <path d="M4 6 L20 6 M4 10 L20 10 M4 14 L20 14 M4 18 L20 18"/>
    <path d="M6 4 L6 20 M10 4 L10 20 M14 4 L14 20 M18 4 L18 20"/>
  </g>
  
  <!-- Heatmap overlay -->
  <rect x="6" y="6" width="12" height="12" fill="url(#heatmapGradient)" opacity="0.7"/>
  
  <!-- Hot spots -->
  <circle cx="14" cy="8" r="3" fill="url(#hotspot1)" opacity="0.8"/>
  <circle cx="10" cy="14" r="2" fill="url(#hotspot2)" opacity="0.8"/>
  
  <!-- Color scale legend -->
  <rect x="20" y="6" width="2" height="12" fill="url(#heatmapGradient)"/>
  <text x="21" y="5" text-anchor="middle" fill="#333" font-family="Arial" font-size="3">H</text>
  <text x="21" y="20" text-anchor="middle" fill="#333" font-family="Arial" font-size="3">L</text>
  
  <!-- Thermometer icon -->
  <circle cx="3" cy="3" r="2" fill="#E74C3C" stroke="#FFFFFF" stroke-width="1"/>
  <rect x="2.5" y="2" width="1" height="2" fill="#FFFFFF"/>
</svg>