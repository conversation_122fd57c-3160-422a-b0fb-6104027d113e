<?xml version="1.0" encoding="UTF-8"?>
<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="meshExportGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8E44AD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#9B59B6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 3D Mesh representation -->
  <g stroke="#74B9FF" stroke-width="1" fill="none">
    <!-- Mesh grid -->
    <path d="M4 8 L12 6 L20 8 L12 10 Z" fill="url(#meshExportGradient)" opacity="0.7"/>
    <path d="M4 8 L4 16 L12 18 L12 10 Z" fill="#A29BFE" opacity="0.8"/>
    <path d="M12 10 L12 18 L20 16 L20 8 Z" fill="#6C5CE7" opacity="0.8"/>
    
    <!-- Grid lines -->
    <path d="M4 8 L20 8 M4 12 L20 12 M4 16 L20 16"/>
    <path d="M8 6 L8 18 M12 6 L12 18 M16 6 L16 18"/>
  </g>
  
  <!-- Crop selection box -->
  <rect x="7" y="9" width="10" height="6" fill="none" stroke="#E74C3C" stroke-width="2" stroke-dasharray="2,2"/>
  
  <!-- Corner handles -->
  <g fill="#E74C3C">
    <rect x="6" y="8" width="2" height="2"/>
    <rect x="16" y="8" width="2" height="2"/>
    <rect x="6" y="14" width="2" height="2"/>
    <rect x="16" y="14" width="2" height="2"/>
  </g>
  
  <!-- Save/Export indicator -->
  <circle cx="19" cy="3" r="3" fill="#27AE60" stroke="#FFFFFF" stroke-width="1"/>
  <path d="M17.5 3 L20.5 3 M19 1.5 L19 4.5" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round"/>
  <rect x="18.5" y="4.5" width="1" height="1" fill="#FFFFFF"/>
  
  <!-- File format indicator -->
  <text x="12" y="21" text-anchor="middle" fill="#8E44AD" font-family="Arial" font-size="3" font-weight="bold">PLY</text>
</svg>