<?xml version="1.0" encoding="UTF-8"?>
<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="csvGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#27AE60;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#229954;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Document background -->
  <rect x="4" y="2" width="14" height="18" fill="#FFFFFF" stroke="#34495E" stroke-width="1" rx="1"/>
  
  <!-- CSV data rows -->
  <g stroke="#95A5A6" stroke-width="0.5">
    <path d="M6 6 L16 6 M6 8 L16 8 M6 10 L16 10 M6 12 L16 12 M6 14 L16 14 M6 16 L16 16"/>
    <path d="M9 4 L9 18 M12 4 L12 18"/>
  </g>
  
  <!-- CSV header -->
  <rect x="6" y="4" width="10" height="2" fill="#3498DB" opacity="0.3"/>
  <text x="11" y="5.5" text-anchor="middle" fill="#2C3E50" font-family="Arial" font-size="2" font-weight="bold">CSV</text>
  
  <!-- Data cells with deformation values -->
  <g fill="#E74C3C" font-family="Arial" font-size="1.5">
    <text x="7.5" y="7.5" text-anchor="middle">X</text>
    <text x="10.5" y="7.5" text-anchor="middle">Y</text>
    <text x="13.5" y="7.5" text-anchor="middle">Z</text>
    <text x="7.5" y="9.5" text-anchor="middle">1.2</text>
    <text x="10.5" y="9.5" text-anchor="middle">0.8</text>
    <text x="13.5" y="9.5" text-anchor="middle">-0.3</text>
  </g>
  
  <!-- Export arrow -->
  <circle cx="19" cy="5" r="3" fill="url(#csvGradient)" stroke="#FFFFFF" stroke-width="1"/>
  <path d="M17 5 L21 5 M19 3 L21 5 L19 7" stroke="#FFFFFF" stroke-width="1.5" 
        stroke-linecap="round" stroke-linejoin="round" fill="none"/>
</svg>