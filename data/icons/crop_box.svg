<?xml version="1.0" encoding="UTF-8"?>
<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="meshGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#74B9FF;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#0984E3;stop-opacity:0.6" />
    </linearGradient>
  </defs>
  
  <!-- Background mesh -->
  <g stroke="#DDD" stroke-width="0.5" fill="none" opacity="0.5">
    <path d="M2 6 L22 6 M2 10 L22 10 M2 14 L22 14 M2 18 L22 18"/>
    <path d="M6 2 L6 22 M10 2 L10 22 M14 2 L14 22 M18 2 L18 22"/>
  </g>
  
  <!-- Crop box -->
  <rect x="7" y="7" width="10" height="10" fill="url(#meshGradient)" stroke="#0984E3" stroke-width="2" stroke-dasharray="3,2"/>
  
  <!-- Corner handles -->
  <g fill="#FF6B35" stroke="#FFFFFF" stroke-width="1">
    <rect x="6" y="6" width="2" height="2"/>
    <rect x="16" y="6" width="2" height="2"/>
    <rect x="6" y="16" width="2" height="2"/>
    <rect x="16" y="16" width="2" height="2"/>
  </g>
  
  <!-- Crop scissors icon -->
  <circle cx="19" cy="5" r="3" fill="#27AE60" stroke="#FFFFFF" stroke-width="1"/>
  <g stroke="#FFFFFF" stroke-width="1" fill="none">
    <path d="M17.5 4 L18.5 5 L20.5 3"/>
    <path d="M17.5 6 L18.5 5 L20.5 7"/>
  </g>
</svg>