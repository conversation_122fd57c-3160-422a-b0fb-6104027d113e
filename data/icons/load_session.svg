<?xml version="1.0" encoding="UTF-8"?>
<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="loadSessionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6C5CE7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A29BFE;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main document -->
  <rect x="4" y="3" width="14" height="18" fill="#FFFFFF" stroke="#34495E" stroke-width="1" rx="1"/>
  
  <!-- Document fold -->
  <path d="M15 3 L15 6 L18 6" fill="none" stroke="#34495E" stroke-width="1"/>
  <path d="M15 3 L18 6 L18 21 L4 21 L4 3 Z" fill="none" stroke="#34495E" stroke-width="1"/>
  
  <!-- Session content layers -->
  <g opacity="0.7">
    <!-- Mesh layer -->
    <rect x="6" y="6" width="10" height="3" fill="#74B9FF" rx="0.5"/>
    <text x="11" y="8" text-anchor="middle" fill="#FFFFFF" font-family="Arial" font-size="2">Mesh</text>
    
    <!-- Analysis layer -->
    <rect x="6" y="10" width="10" height="3" fill="#00B894" rx="0.5"/>
    <text x="11" y="12" text-anchor="middle" fill="#FFFFFF" font-family="Arial" font-size="2">Analysis</text>
    
    <!-- View layer -->
    <rect x="6" y="14" width="10" height="3" fill="#FDCB6E" rx="0.5"/>
    <text x="11" y="16" text-anchor="middle" fill="#2D3436" font-family="Arial" font-size="2">View</text>
  </g>
  
  <!-- Load indicator -->
  <circle cx="19" cy="5" r="3" fill="url(#loadSessionGradient)" stroke="#FFFFFF" stroke-width="1"/>
  <path d="M21 5 L17 5 M19 3 L17 5 L19 7" stroke="#FFFFFF" stroke-width="1.5" 
        stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  
  <!-- Session file extension -->
  <text x="11" y="20" text-anchor="middle" fill="#6C5CE7" font-family="Arial" font-size="2.5" font-weight="bold">SESSION</text>
</svg>