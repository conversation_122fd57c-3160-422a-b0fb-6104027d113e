from PySide6.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QDoubleSpinBox, QFormLayout
from PySide6.QtCore import Qt

class CropBoxDialog(QDialog):
    def __init__(self, mesh_viewer, initial_center=None, parent=None):
        super().__init__(parent)
        # Ensure dialog is non-modal and does not block main window interaction
        self.setWindowModality(Qt.NonModal)
        self.setWindowFlag(Qt.Window)
        self.setAttribute(Qt.WA_DeleteOnClose, True)
        self._box_actor = None  # Initialize before any preview
        self.setWindowTitle("Crop Mesh with Box")
        self.setMinimumWidth(400)
        self.mesh_viewer = mesh_viewer
        layout = QVBoxLayout(self)
        layout.addWidget(QLabel("Adjust the crop box and press Crop to apply."))
        # Center controls
        form = QFormLayout()
        self.center_x = QDoubleSpinBox(); self.center_x.setRange(-1e4, 1e4); self.center_x.setDecimals(3)
        self.center_y = QDoubleSpinBox(); self.center_y.setRange(-1e4, 1e4); self.center_y.setDecimals(3)
        self.center_z = QDoubleSpinBox(); self.center_z.setRange(-1e4, 1e4); self.center_z.setDecimals(3)
        form.addRow("Center X:", self.center_x)
        form.addRow("Center Y:", self.center_y)
        form.addRow("Center Z:", self.center_z)
        # Size controls
        self.size_x = QDoubleSpinBox(); self.size_x.setRange(0.01, 1e4); self.size_x.setDecimals(3)
        self.size_y = QDoubleSpinBox(); self.size_y.setRange(0.01, 1e4); self.size_y.setDecimals(3)
        self.size_z = QDoubleSpinBox(); self.size_z.setRange(0.01, 1e4); self.size_z.setDecimals(3)
        form.addRow("Width (X):", self.size_x)
        form.addRow("Height (Y):", self.size_y)
        form.addRow("Depth (Z):", self.size_z)
        # Rotation controls
        self.rot_x = QDoubleSpinBox(); self.rot_x.setRange(-180, 180); self.rot_x.setDecimals(2)
        self.rot_y = QDoubleSpinBox(); self.rot_y.setRange(-180, 180); self.rot_y.setDecimals(2)
        self.rot_z = QDoubleSpinBox(); self.rot_z.setRange(-180, 180); self.rot_z.setDecimals(2)
        form.addRow("Rotate X (deg):", self.rot_x)
        form.addRow("Rotate Y (deg):", self.rot_y)
        form.addRow("Rotate Z (deg):", self.rot_z)
        layout.addLayout(form)
        # Buttons
        btns = QHBoxLayout()
        self.btn_crop = QPushButton("Crop")
        self.btn_cancel = QPushButton("Cancel")
        btns.addWidget(self.btn_crop)
        btns.addWidget(self.btn_cancel)
        layout.addLayout(btns)
        # Connect
        self.btn_crop.clicked.connect(self.crop_and_accept)
        self.btn_cancel.clicked.connect(self.reject)
        for spin in [self.center_x, self.center_y, self.center_z, self.size_x, self.size_y, self.size_z, self.rot_x, self.rot_y, self.rot_z]:
            spin.valueChanged.connect(self.update_box_preview)
        # Set initial values
        self._init_box_from_mesh(initial_center)

    def _init_box_from_mesh(self, initial_center):
        import numpy as np
        mesh = getattr(self.mesh_viewer, 'mesh', None)
        if mesh is not None:
            bounds = np.array(mesh.bounds).reshape(3, 2)
            center = bounds.mean(axis=1) if initial_center is None else np.array(initial_center)
            size = bounds[:, 1] - bounds[:, 0]
            self.center_x.setValue(center[0])
            self.center_y.setValue(center[1])
            self.center_z.setValue(center[2])
            self.size_x.setValue(size[0])
            self.size_y.setValue(size[1])
            self.size_z.setValue(size[2])
        else:
            self.center_x.setValue(0)
            self.center_y.setValue(0)
            self.center_z.setValue(0)
            self.size_x.setValue(1)
            self.size_y.setValue(1)
            self.size_z.setValue(1)
        self.rot_x.setValue(0)
        self.rot_y.setValue(0)
        self.rot_z.setValue(0)

    def update_box_preview(self):
        import numpy as np
        import pyvista as pv
        plotter = self.mesh_viewer.plotter
        # Remove previous box actor
        if self._box_actor:
            try:
                plotter.remove_actor(self._box_actor)
            except Exception:
                pass
            self._box_actor = None
        # Build box
        cx, cy, cz = self.center_x.value(), self.center_y.value(), self.center_z.value()
        sx, sy, sz = self.size_x.value(), self.size_y.value(), self.size_z.value()
        rx, ry, rz = np.deg2rad(self.rot_x.value()), np.deg2rad(self.rot_y.value()), np.deg2rad(self.rot_z.value())
        box = pv.Box(bounds=(-sx/2, sx/2, -sy/2, sy/2, -sz/2, sz/2))
        # Build transform
        def rotmat(rx, ry, rz):
            from scipy.spatial.transform import Rotation as R
            return R.from_euler('zyx', [rz, ry, rx]).as_matrix()
        Rmat = rotmat(rx, ry, rz)
        T = np.eye(4)
        T[:3, :3] = Rmat
        T[:3, 3] = [cx, cy, cz]
        box = box.transform(T, inplace=False)
        # Add to plotter
        self._box_actor = plotter.add_mesh(
            box, color="#00e6ff", opacity=0.25, style='surface', name="crop_box_preview", reset_camera=False,
            show_edges=True, edge_color="#0080a0", line_width=2
        )
        plotter.render()

    def crop_and_accept(self):
        import numpy as np
        import pyvista as pv
        mesh = getattr(self.mesh_viewer, 'mesh', None)
        if mesh is not None:
            cx, cy, cz = self.center_x.value(), self.center_y.value(), self.center_z.value()
            sx, sy, sz = self.size_x.value(), self.size_y.value(), self.size_z.value()
            rx, ry, rz = np.deg2rad(self.rot_x.value()), np.deg2rad(self.rot_y.value()), np.deg2rad(self.rot_z.value())
            box = pv.Box(bounds=(-sx/2, sx/2, -sy/2, sy/2, -sz/2, sz/2))
            def rotmat(rx, ry, rz):
                from scipy.spatial.transform import Rotation as R
                return R.from_euler('zyx', [rz, ry, rx]).as_matrix()
            Rmat = rotmat(rx, ry, rz)
            T = np.eye(4)
            T[:3, :3] = Rmat
            T[:3, 3] = [cx, cy, cz]
            box = box.transform(T, inplace=False)
            # Use select_enclosed_points and extract_points for robust cropping
            enclosed = mesh.select_enclosed_points(box, tolerance=1e-6)
            mask = enclosed.point_data['SelectedPoints'].view(np.bool_)
            cropped = mesh.extract_points(mask, adjacent_cells=True)
            # --- Transfer per-vertex color if missing ---
            color_keys = [k for k in mesh.point_data.keys() if k.lower() in ("rgb", "rgba", "colors", "colour", "vertex_colors")]
            # If cropped mesh is missing color, transfer from original using nearest neighbor
            if color_keys and all(k not in cropped.point_data for k in color_keys):
                try:
                    from scipy.spatial import cKDTree
                    src_points = np.asarray(mesh.points)
                    dst_points = np.asarray(cropped.points)
                    src_colors = np.asarray(mesh.point_data[color_keys[0]])
                    tree = cKDTree(src_points)
                    dists, idxs = tree.query(dst_points)
                    cropped.point_data[color_keys[0]] = src_colors[idxs]
                    print(f"[DEBUG] Transferred color array '{color_keys[0]}' to cropped mesh, shape: {cropped.point_data[color_keys[0]].shape}")
                except Exception as e:
                    print(f"[DEBUG] Failed to transfer color array: {e}")
            else:
                # Debug output
                for k in color_keys:
                    if k in cropped.point_data:
                        print(f"[DEBUG] Cropped mesh has color array '{k}', shape: {cropped.point_data[k].shape}")
            if cropped.n_points > 0:
                self.mesh_viewer.set_mesh(cropped)
        # Remove box preview
        plotter = self.mesh_viewer.plotter
        if self._box_actor:
            try:
                plotter.remove_actor(self._box_actor)
            except Exception:
                pass
            self._box_actor = None
        plotter.render()
        self.accept()

    def show(self):
        # Ensure dialog is shown as non-modal and does not block the main window
        self.setWindowModality(Qt.NonModal)
        self.setWindowFlag(Qt.Window)
        self.setAttribute(Qt.WA_DeleteOnClose, True)
        super().show()
