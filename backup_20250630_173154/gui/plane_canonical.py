from PySide6.QtWidgets import QDialog, QVBoxLayout, QLabel, QComboBox, QSlider, QDoubleSpinBox, QPushButton, QHBoxLayout, QGroupBox, QFormLayout
from PySide6.QtCore import Qt

class PlaneCanonicalDialog(QDialog):
    def __init__(self, mesh_viewer, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Define Canonical Plane")
        self.setMinimumWidth(350)
        self.mesh_viewer = mesh_viewer
        layout = QVBoxLayout(self)
        layout.addWidget(QLabel("Select a canonical plane and adjust its position/orientation."))
        # Plane orientation
        self.plane_combo = QComboBox()
        self.plane_combo.addItems(["XY", "YZ", "ZX"])
        layout.addWidget(self.plane_combo)
        # Offset (translation)
        offset_group = QGroupBox("Offset (Move Along Normal)")
        offset_layout = QFormLayout(offset_group)
        self.offset_slider = QSlider(Qt.Horizontal)
        self.offset_slider.setRange(-100, 100)
        self.offset_slider.setValue(0)
        self.offset_spin = QDoubleSpinBox()
        self.offset_spin.setRange(-100.0, 100.0)
        self.offset_spin.setValue(0.0)
        offset_layout.addRow("Offset:", self.offset_spin)
        layout.addWidget(offset_group)
        # Rotation
        rot_group = QGroupBox("Rotation (Degrees)")
        rot_layout = QFormLayout(rot_group)
        self.rot_x = QDoubleSpinBox(); self.rot_x.setRange(-180, 180); self.rot_x.setValue(0)
        self.rot_y = QDoubleSpinBox(); self.rot_y.setRange(-180, 180); self.rot_y.setValue(0)
        self.rot_z = QDoubleSpinBox(); self.rot_z.setRange(-180, 180); self.rot_z.setValue(0)
        rot_layout.addRow("X:", self.rot_x)
        rot_layout.addRow("Y:", self.rot_y)
        rot_layout.addRow("Z:", self.rot_z)
        layout.addWidget(rot_group)
        # Slab thickness
        slab_group = QGroupBox("Slab Thickness")
        slab_layout = QFormLayout(slab_group)
        self.slab_spin = QDoubleSpinBox()
        self.slab_spin.setRange(0.01, 100.0)
        self.slab_spin.setValue(1.0)
        slab_layout.addRow("Thickness:", self.slab_spin)
        layout.addWidget(slab_group)
        # Buttons
        btns = QHBoxLayout()
        self.btn_reset = QPushButton("Reset")
        self.btn_apply = QPushButton("Apply and Run Analysis")
        btns.addWidget(self.btn_reset)
        btns.addWidget(self.btn_apply)
        layout.addLayout(btns)

        # Internal state
        self.plane_params = None
        self.slab_thickness = None
        self.plane_actor = None
        # Connect signals
        self.btn_reset.clicked.connect(self.reset_params)
        self.btn_apply.clicked.connect(self.apply_and_accept)
        self.plane_combo.currentIndexChanged.connect(self.update_plane)
        self.offset_spin.valueChanged.connect(self.update_plane)
        self.rot_x.valueChanged.connect(self.update_plane)
        self.rot_y.valueChanged.connect(self.update_plane)
        self.rot_z.valueChanged.connect(self.update_plane)
        self.slab_spin.valueChanged.connect(self.update_plane)
        # Set initial plane
        self.update_plane()

    def reset_params(self):
        self.plane_combo.setCurrentIndex(0)
        self.offset_spin.setValue(0.0)
        self.rot_x.setValue(0)
        self.rot_y.setValue(0)
        self.rot_z.setValue(0)
        self.slab_spin.setValue(1.0)
        self.update_plane()

    def update_plane(self):
        import numpy as np
        bounds = np.array(self.mesh_viewer.mesh.bounds).reshape(3, 2)
        center = bounds.mean(axis=1)
        # Get canonical plane
        plane_type = self.plane_combo.currentText()
        if plane_type == "XY":
            normal = np.array([0,0,1])
        elif plane_type == "YZ":
            normal = np.array([1,0,0])
        elif plane_type == "ZX":
            normal = np.array([0,1,0])
        else:
            normal = np.array([0,0,1])
        origin = center.copy()
        # Apply offset
        origin = origin + normal * self.offset_spin.value()
        # Apply rotation (around center)
        from scipy.spatial.transform import Rotation as R
        rot = R.from_euler('xyz', [self.rot_x.value(), self.rot_y.value(), self.rot_z.value()], degrees=True)
        normal = rot.apply(normal)
        self.plane_params = {'origin': origin, 'normal': normal}
        self.slab_thickness = self.slab_spin.value()
        # --- Show plane in viewer ---
        self.show_plane_in_viewer(origin, normal)

    def show_plane_in_viewer(self, origin, normal):
        import pyvista as pv
        plotter = self.mesh_viewer.plotter
        # Remove previous plane actor if exists
        if self.plane_actor is not None:
            try:
                plotter.remove_actor(self.plane_actor)
            except Exception:
                pass
            self.plane_actor = None
        # Create a large enough plane
        mesh = self.mesh_viewer.mesh
        bounds = mesh.bounds
        size = max(bounds[1]-bounds[0], bounds[3]-bounds[2], bounds[5]-bounds[4]) * 1.2
        plane = pv.Plane(center=origin, direction=normal, i_size=size, j_size=size)
        self.plane_actor = plotter.add_mesh(plane, color='yellow', opacity=0.3, name='analysis_plane')
        plotter.render()

    def apply_and_accept(self):
        self.update_plane()
        self.accept()
