from PySide6.QtWidgets import Q<PERSON>ialog, QVBoxLayout, QDialogButtonBox, QComboBox, QLabel

class MeshDisplayModeDialog(QDialog):
    def __init__(self, current_mode="surface", parent=None):
        super().__init__(parent)
        self.setWindowTitle("Select Mesh Display Mode")
        layout = QVBoxLayout(self)
        layout.addWidget(QLabel("Choose mesh visualization mode:"))
        self.combo = QComboBox()
        self.combo.addItems([
            "Surface",
            "Surface + Edge",
            "Wireframe",
            "Points"
        ])
        self.combo.setCurrentText(self.mode_to_text(current_mode))
        layout.addWidget(self.combo)
        btns = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        btns.accepted.connect(self.accept)
        btns.rejected.connect(self.reject)
        layout.addWidget(btns)

    def selected_mode(self):
        text = self.combo.currentText()
        return self.text_to_mode(text)

    @staticmethod
    def mode_to_text(mode):
        return {
            "surface": "Surface",
            "surface+edge": "Surface + Edge",
            "wireframe": "Wireframe",
            "points": "Points"
        }.get(mode, "Surface")

    @staticmethod
    def text_to_mode(text):
        return {
            "Surface": "surface",
            "Surface + Edge": "surface+edge",
            "Wireframe": "wireframe",
            "Points": "points"
        }[text]
