#!/usr/bin/env python3
"""
Test the report icon fix
"""

import sys
import os

def test_icon_files_exist():
    """Test that report icon files exist"""
    print("📁 Testing Icon Files...")
    
    try:
        icon_files = [
            "src/icons/advanced_report.svg",
            "src/icons/advanced_report.png",
            "src/icons/advanced_report_48.png",
            "src/icons/report_simple.png"
        ]
        
        for icon_file in icon_files:
            if os.path.exists(icon_file):
                file_size = os.path.getsize(icon_file)
                print(f"✅ {icon_file} exists ({file_size} bytes)")
            else:
                print(f"❌ {icon_file} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Icon files test failed: {e}")
        return False

def test_icon_function_update():
    """Test that the icon function was updated"""
    print("\n🔧 Testing Icon Function Update...")
    
    try:
        # Read mainwindow.py and check for updated icon function
        with open("src/gui/mainwindow.py", 'r') as f:
            content = f.read()
        
        # Check for PNG-first loading
        if "f\"{base_name}.png\"" in content:
            print("✅ Icon function updated to prefer PNG")
        else:
            print("❌ Icon function not updated for PNG preference")
            return False
        
        # Check for SVG fallback
        if "f\"{base_name}.svg\"" in content:
            print("✅ Icon function has SVG fallback")
        else:
            print("❌ Icon function missing SVG fallback")
            return False
        
        # Check for null icon checking
        if "icon_obj.isNull()" in content:
            print("✅ Icon function checks for null icons")
        else:
            print("❌ Icon function missing null check")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Icon function test failed: {e}")
        return False

def test_advanced_report_button():
    """Test that the advanced report button is properly configured"""
    print("\n📊 Testing Advanced Report Button...")
    
    try:
        # Read mainwindow.py and check for advanced report button
        with open("src/gui/mainwindow.py", 'r') as f:
            content = f.read()
        
        # Check for button creation
        if 'icon("advanced_report.svg")' in content:
            print("✅ Advanced report button uses icon function")
        else:
            print("❌ Advanced report button not found")
            return False
        
        # Check for tooltip
        if "Comprehensive report generation" in content:
            print("✅ Advanced report button has tooltip")
        else:
            print("❌ Advanced report button missing tooltip")
            return False
        
        # Check for method connection
        if "show_advanced_reporting" in content:
            print("✅ Advanced report button connected to method")
        else:
            print("❌ Advanced report button not connected")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Advanced report button test failed: {e}")
        return False

def test_icon_loading_logic():
    """Test the icon loading logic"""
    print("\n🎨 Testing Icon Loading Logic...")
    
    try:
        # Simulate the icon loading logic
        name = "advanced_report.svg"
        base_name = name.replace('.svg', '').replace('.png', '')
        
        # Test path generation
        expected_paths = [
            f"src/icons/{base_name}.png",
            f"src/icons/{base_name}.svg",
            f"src/icons/{name}",
            f"data/icons/{name}",
            f"icons/{name}"
        ]
        
        print(f"✅ Base name extraction: '{name}' → '{base_name}'")
        print(f"✅ Generated {len(expected_paths)} search paths")
        
        # Check which paths exist
        existing_paths = []
        for path in expected_paths:
            if os.path.exists(path):
                existing_paths.append(path)
                print(f"✅ Found: {path}")
        
        if existing_paths:
            print(f"✅ Icon loading should work: {len(existing_paths)} valid paths")
            return True
        else:
            print("❌ No valid icon paths found")
            return False
        
    except Exception as e:
        print(f"❌ Icon loading logic test failed: {e}")
        return False

def show_icon_fix_summary():
    """Show summary of the icon fix"""
    print("\n" + "=" * 50)
    print("🎨 REPORT ICON FIX SUMMARY")
    print("=" * 50)
    
    print("\n❌ ORIGINAL PROBLEM:")
    print("   • Advanced Reports button had no visible icon")
    print("   • SVG icons sometimes don't display in Qt")
    print("   • Icon loading function didn't prefer PNG")
    
    print("\n✅ FIXES APPLIED:")
    print("   1. Created PNG versions of advanced_report icon:")
    print("      • advanced_report.png (24x24)")
    print("      • advanced_report_48.png (48x48)")
    print("      • report_simple.png (fallback)")
    print("   2. Updated icon loading function:")
    print("      • Prefers PNG over SVG for better compatibility")
    print("      • Checks for null icons before returning")
    print("      • Multiple fallback paths")
    print("   3. Enhanced icon search:")
    print("      • Tries base_name.png first")
    print("      • Falls back to base_name.svg")
    print("      • Then tries original name")
    
    print("\n🎯 RESULT:")
    print("   • Advanced Reports button should now show icon 📊")
    print("   • Better icon compatibility across Qt versions")
    print("   • Robust fallback system for missing icons")
    
    print("\n🚀 NEXT STEPS:")
    print("   1. Restart DeformViz 3D")
    print("   2. Look for 📊 Advanced Reports button in toolbar")
    print("   3. Icon should be visible and clickable")
    print("   4. If still missing, check console for icon loading messages")

def main():
    """Main test function"""
    print("🧪 DeformViz 3D Report Icon Fix Test")
    print("=" * 45)
    
    # Run tests
    tests = [
        ("Icon Files Exist", test_icon_files_exist),
        ("Icon Function Update", test_icon_function_update),
        ("Advanced Report Button", test_advanced_report_button),
        ("Icon Loading Logic", test_icon_loading_logic)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 45)
    print("📋 TEST SUMMARY")
    print("=" * 45)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n📊 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 REPORT ICON FIX SUCCESSFUL!")
        print("   ✅ PNG icons created and available")
        print("   ✅ Icon loading function updated")
        print("   ✅ Advanced report button properly configured")
        print("   ✅ Robust icon loading logic implemented")
        print("\n📊 The Advanced Reports icon should now be visible!")
    else:
        print("\n⚠️ Some icon issues remain")
    
    # Show fix summary
    show_icon_fix_summary()
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
