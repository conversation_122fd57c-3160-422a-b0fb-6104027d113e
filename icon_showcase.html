
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeformViz 3D - Icon Showcase</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #4A90E2;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
            font-size: 1.2em;
        }
        .app-icon-section {
            text-align: center;
            margin-bottom: 50px;
            padding: 30px;
            background: linear-gradient(135deg, #4A90E2, #2E5BBA);
            border-radius: 15px;
            color: white;
        }
        .app-icon {
            width: 128px;
            height: 128px;
            margin: 20px auto;
            display: block;
            filter: drop-shadow(0 10px 20px rgba(0,0,0,0.3));
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .icon-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid #e9ecef;
        }
        .icon-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .icon-card img {
            width: 48px;
            height: 48px;
            margin-bottom: 10px;
        }
        .icon-name {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        .icon-description {
            font-size: 0.9em;
            color: #6c757d;
        }
        .features {
            margin-top: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .features h3 {
            color: #4A90E2;
            margin-bottom: 15px;
        }
        .features ul {
            list-style-type: none;
            padding: 0;
        }
        .features li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }
        .features li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DeformViz 3D</h1>
        <p class="subtitle">3D Deformation Analysis Suite - Icon Showcase</p>
        
        <div class="app-icon-section">
            <h2>Application Icon</h2>
            <img src="data/icons/app_icon.svg" alt="DeformViz 3D App Icon" class="app-icon">
            <p>The main application icon representing 3D mesh analysis with heatmap visualization</p>
        </div>
        
        <h2>Main Toolbar Icons</h2>
        <div class="icon-grid">

            <div class="icon-card">
                <img src="data/icons/analyze.svg" alt="Analyze">
                <div class="icon-name">Analyze</div>
                <div class="icon-description">Perform deformation analysis</div>
            </div>

            <div class="icon-card">
                <img src="data/icons/export.svg" alt="Export">
                <div class="icon-name">Export</div>
                <div class="icon-description">Export results and data</div>
            </div>

            <div class="icon-card">
                <img src="data/icons/help.svg" alt="Help">
                <div class="icon-name">Help</div>
                <div class="icon-description">Show help and documentation</div>
            </div>

            <div class="icon-card">
                <img src="data/icons/load.svg" alt="Load Mesh">
                <div class="icon-name">Load Mesh</div>
                <div class="icon-description">Load 3D mesh files for analysis</div>
            </div>

            <div class="icon-card">
                <img src="data/icons/plane.svg" alt="Define Plane">
                <div class="icon-name">Define Plane</div>
                <div class="icon-description">Set reference plane for analysis</div>
            </div>

            <div class="icon-card">
                <img src="data/icons/save.svg" alt="Save Project">
                <div class="icon-name">Save Project</div>
                <div class="icon-description">Save current analysis project</div>
            </div>

            <div class="icon-card">
                <img src="data/icons/screenshot.svg" alt="Screenshot">
                <div class="icon-name">Screenshot</div>
                <div class="icon-description">Capture current view</div>
            </div>

            <div class="icon-card">
                <img src="data/icons/settings.svg" alt="Settings">
                <div class="icon-name">Settings</div>
                <div class="icon-description">Configure application settings</div>
            </div>

            <div class="icon-card">
                <img src="data/icons/visualize.svg" alt="Visualize">
                <div class="icon-name">Visualize</div>
                <div class="icon-description">Display heatmap visualization</div>
            </div>

        </div>
        
        <div class="features">
            <h3>🎨 Icon Design Features</h3>
            <ul>
                <li>Professional, modern design with consistent color scheme</li>
                <li>High contrast for excellent visibility</li>
                <li>Scalable SVG format for crisp display at any size</li>
                <li>Intuitive symbols that clearly represent their functions</li>
                <li>Cohesive visual language across all interface elements</li>
                <li>Optimized for both light and dark themes</li>
            </ul>
            
            <h3>🚀 Application Features</h3>
            <ul>
                <li>Advanced 3D mesh visualization and analysis</li>
                <li>Real-time deformation heatmap generation</li>
                <li>Interactive plane definition tools</li>
                <li>Statistical analysis and anomaly detection</li>
                <li>Professional export capabilities</li>
                <li>Comprehensive mesh processing tools</li>
            </ul>
        </div>
    </div>
</body>
</html>
