#!/usr/bin/env python3
"""
Test the enhanced mesh processing system
"""

import sys
import os

def test_mesh_processing_dialog():
    """Test that the enhanced mesh processing dialog has all new filters"""
    print("🔧 Testing Enhanced Mesh Processing Dialog...")
    
    try:
        from src.gui.mesh_processing import MeshProcessingDialog
        
        # Create dialog (without showing it)
        dialog = MeshProcessingDialog()
        
        # Check for original filters
        original_filters = [
            'clean_cb', 'components_cb', 'holes_cb', 'smooth_cb', 'decimate_cb'
        ]
        
        for filter_name in original_filters:
            if hasattr(dialog, filter_name):
                print(f"✅ Original filter found: {filter_name}")
            else:
                print(f"❌ Missing original filter: {filter_name}")
                return False
        
        # Check for new advanced filters
        advanced_filters = [
            'noise_cb', 'surface_cb', 'repair_cb', 'subdiv_cb', 'optimize_cb'
        ]
        
        for filter_name in advanced_filters:
            if hasattr(dialog, filter_name):
                print(f"✅ Advanced filter found: {filter_name}")
            else:
                print(f"❌ Missing advanced filter: {filter_name}")
                return False
        
        # Check for parameter controls
        parameter_controls = [
            'noise_strength_spin', 'subdiv_levels_spin'
        ]
        
        for control_name in parameter_controls:
            if hasattr(dialog, control_name):
                print(f"✅ Parameter control found: {control_name}")
            else:
                print(f"❌ Missing parameter control: {control_name}")
                return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Mesh processing dialog import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Mesh processing dialog test failed: {e}")
        return False

def test_get_selected_filters():
    """Test that get_selected_filters returns all new parameters"""
    print("\n⚙️ Testing get_selected_filters Method...")
    
    try:
        from src.gui.mesh_processing import MeshProcessingDialog
        
        dialog = MeshProcessingDialog()
        filters = dialog.get_selected_filters()
        
        # Check for original parameters
        original_params = [
            'clean', 'components', 'holes', 'smooth', 'decimate',
            'hole_size', 'smooth_method', 'smooth_iter', 'smooth_relax',
            'decimate_algorithm', 'decimate_target'
        ]
        
        for param in original_params:
            if param in filters:
                print(f"✅ Original parameter: {param}")
            else:
                print(f"❌ Missing original parameter: {param}")
                return False
        
        # Check for new advanced parameters
        advanced_params = [
            'noise_reduction', 'noise_strength', 'surface_reconstruct',
            'mesh_repair', 'subdivide', 'subdivision_levels', 'optimize'
        ]
        
        for param in advanced_params:
            if param in filters:
                print(f"✅ Advanced parameter: {param}")
            else:
                print(f"❌ Missing advanced parameter: {param}")
                return False
        
        print(f"📊 Total parameters: {len(filters)}")
        return True
        
    except Exception as e:
        print(f"❌ get_selected_filters test failed: {e}")
        return False

def test_decimation_fix():
    """Test that the decimation fix handles different mesh types"""
    print("\n🔧 Testing Decimation Fix...")
    
    try:
        # Check that the alternative mesh reduction method exists
        with open("src/gui/mainwindow.py", 'r') as f:
            content = f.read()
        
        # Check for decimation fix elements
        fix_elements = [
            "_alternative_mesh_reduction",
            "extract_surface",
            "PolyData",
            "alternative reduction method",
            "UnstructuredGrid to PolyData"
        ]
        
        for element in fix_elements:
            if element in content:
                print(f"✅ Decimation fix element found: {element}")
            else:
                print(f"❌ Missing decimation fix element: {element}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Decimation fix test failed: {e}")
        return False

def test_advanced_filter_processing():
    """Test that advanced filter processing logic exists"""
    print("\n🎨 Testing Advanced Filter Processing...")
    
    try:
        with open("src/gui/mainwindow.py", 'r') as f:
            content = f.read()
        
        # Check for advanced filter processing
        processing_elements = [
            "noise_reduction",
            "surface_reconstruct", 
            "mesh_repair",
            "subdivide",
            "optimize",
            "reconstruct_surface",
            "fill_holes",
            "subdivision_levels"
        ]
        
        for element in processing_elements:
            if element in content:
                print(f"✅ Processing element found: {element}")
            else:
                print(f"❌ Missing processing element: {element}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Advanced filter processing test failed: {e}")
        return False

def show_enhanced_mesh_processing_guide():
    """Show guide for using enhanced mesh processing"""
    print("\n" + "=" * 60)
    print("🔧 ENHANCED MESH PROCESSING GUIDE")
    print("=" * 60)
    
    print("\n🛠️ ORIGINAL FILTERS (IMPROVED):")
    print("   ✅ Clean Mesh - Remove duplicate points and degenerate faces")
    print("   ✅ Remove Small Components - Remove disconnected parts")
    print("   ✅ Fill Small Holes - Fill holes up to specified area")
    print("   ✅ Smooth Mesh - Laplacian, Taubin, or HC smoothing")
    print("   ✅ Decimate Mesh - Reduce faces with FIXED decimation")
    
    print("\n🚀 NEW ADVANCED FILTERS:")
    print("   🆕 Noise Reduction - Advanced multi-pass smoothing")
    print("      • Adjustable strength (0.01 = gentle, 1.0 = strong)")
    print("      • Perfect for noisy scanned data")
    
    print("   🆕 Surface Reconstruction - Convert point clouds to surfaces")
    print("      • Automatically detects point cloud data")
    print("      • Generates triangulated surface mesh")
    
    print("   🆕 Mesh Repair - Fix topology issues")
    print("      • Removes degenerate elements")
    print("      • Fills holes automatically")
    print("      • Cleans mesh thoroughly")
    
    print("   🆕 Subdivision - Increase mesh resolution")
    print("      • 1-3 subdivision levels")
    print("      • Smooths and refines geometry")
    print("      • Great for low-resolution meshes")
    
    print("   🆕 Mesh Optimization - Remove degeneracies")
    print("      • Removes duplicate points with tolerance")
    print("      • Eliminates degenerate cells")
    print("      • Optimizes mesh structure")
    
    print("\n🔧 DECIMATION FIX:")
    print("   ✅ Fixed 'UnstructuredGrid' has no attribute 'decimate' error")
    print("   ✅ Automatic conversion to PolyData when needed")
    print("   ✅ Alternative reduction methods for unsupported mesh types")
    print("   ✅ Robust fallback system")
    
    print("\n💡 USAGE RECOMMENDATIONS:")
    print("   📐 For CAD Models: Clean → Repair → Optimize")
    print("   🔬 For Scanned Data: Clean → Noise Reduction → Smooth")
    print("   ☁️ For Point Clouds: Surface Reconstruction → Clean → Smooth")
    print("   📉 For Large Meshes: Decimate → Clean → Optimize")
    print("   🎯 For Low-Res Meshes: Subdivide → Smooth → Optimize")
    
    print("\n⚡ PERFORMANCE TIPS:")
    print("   • Use Decimation first for very large meshes")
    print("   • Apply Noise Reduction before other filters")
    print("   • Use Subdivision sparingly (increases mesh size)")
    print("   • Always end with Optimization for best results")
    print("   • Preview changes before applying to large meshes")

def main():
    """Main test function"""
    print("🧪 DeformViz 3D Enhanced Mesh Processing Test")
    print("=" * 50)
    
    # Run tests
    tests = [
        ("Mesh Processing Dialog", test_mesh_processing_dialog),
        ("get_selected_filters Method", test_get_selected_filters),
        ("Decimation Fix", test_decimation_fix),
        ("Advanced Filter Processing", test_advanced_filter_processing)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n📊 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ENHANCED MESH PROCESSING READY!")
        print("   ✅ Decimation error fixed")
        print("   ✅ 5 new advanced filters added")
        print("   ✅ Robust mesh type handling")
        print("   ✅ Professional mesh processing capabilities")
        print("\n🔧 Process Mesh button now has enterprise-grade functionality!")
    else:
        print("\n⚠️ Some mesh processing features need attention")
    
    # Show guide
    show_enhanced_mesh_processing_guide()
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
