#!/usr/bin/env python3
"""
3D Heatmap Deformation Viewer
Main entry point for the application
"""

import sys
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.core.config import config
from src.utils.logging_config import setup_logging
from src.gui.mainwindow import run_app

def main():
    """Main application entry point"""
    # Setup NVIDIA environment if needed
    config.setup_nvidia_environment()

    # Setup logging
    log_file = Path("output") / "app.log"
    logger = setup_logging(log_file=str(log_file))

    logger.info(f"Starting {config.app_name} v{config.version}")

    try:
        run_app()
    except Exception as e:
        logger.error(f"Application error: {e}", exc_info=True)
        sys.exit(1)
    finally:
        logger.info("Application shutdown")
        config.save_settings()

if __name__ == "__main__":
    main()
