#!/usr/bin/env python3
"""
Test script to verify DeformViz 3D can start without AttributeError issues
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all imports work correctly"""
    try:
        print("Testing imports...")
        
        # Test core imports
        from src.gui.mainwindow import MainWindow
        print("✅ MainWindow import successful")
        
        from src.gui.analysis_wizard import AnalysisWizard
        print("✅ AnalysisWizard import successful")
        
        from src.gui.meshviewer import MeshViewerWidget
        print("✅ MeshViewerWidget import successful")
        
        print("✅ All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_class_instantiation():
    """Test that classes can be instantiated without errors"""
    try:
        print("\nTesting class instantiation...")
        
        # This will test if all the method connections work
        # We can't actually show the window in this environment, but we can test instantiation
        
        # Note: We can't actually instantiate MainWindow without QApplication
        # But we can test that the class definition is valid
        from src.gui.mainwindow import MainWindow
        
        # Check that all the methods referenced in connections exist
        required_methods = [
            'load_mesh_dialog',
            'save_session_dialog', 
            'load_session_dialog',
            'save_screenshot_dialog',  # This was the problematic one
            'export_deformation_csv',
            'show_analysis_wizard',
            'show_mesh_display_mode_dialog',
            'show_mesh_processing_dialog',
            'show_advanced_analysis_dialog',
            'show_plane_definition_choice',
            'show_crop_box_dialog',
            'show_section_with_plane_dialog',
            'show_heatmap_clicked',
            'show_vector_field_clicked',
            'show_colormap_dialog',
            'export_deformation_csv',
            'save_cropped_mesh',
            'quick_screenshot',
            'undo_last_operation',
            'toggle_projection_mode',
            'zoom_all',
            'zoom_window',
            'toggle_sidebar',
            'toggle_left_toolbar',
            'pick_background_color',
            'toggle_analysis_plane',
            'show_set_unit_dialog',
            'save_project_dialog',
            'show_help_dialog',
            # New help menu methods
            'show_quick_start_guide',
            'show_detailed_workflow',
            'show_wizard_help',
            'show_keyboard_shortcuts',
            'show_troubleshooting',
            'show_about_dialog',
            # New measurement and quality control methods
            'show_measurement_tools',
            'show_quality_control',
            '_on_measurement_added'
        ]
        
        print("Checking required methods exist...")
        for method_name in required_methods:
            if hasattr(MainWindow, method_name):
                print(f"✅ {method_name}")
            else:
                print(f"❌ Missing method: {method_name}")
                return False
        
        print("✅ All required methods exist!")
        return True
        
    except Exception as e:
        print(f"❌ Class instantiation error: {e}")
        return False

def test_wizard_methods():
    """Test that wizard-related methods exist"""
    try:
        print("\nTesting wizard methods...")
        
        from src.gui.mainwindow import MainWindow
        
        wizard_methods = [
            '_wizard_load_mesh',
            '_wizard_define_plane', 
            '_wizard_run_analysis'
        ]
        
        for method_name in wizard_methods:
            if hasattr(MainWindow, method_name):
                print(f"✅ {method_name}")
            else:
                print(f"❌ Missing wizard method: {method_name}")
                return False
        
        print("✅ All wizard methods exist!")
        return True
        
    except Exception as e:
        print(f"❌ Wizard methods error: {e}")
        return False

def main():
    """Run all tests"""
    print("🔍 DeformViz 3D Startup Test")
    print("=" * 50)
    
    success = True
    
    # Test imports
    if not test_imports():
        success = False
    
    # Test class instantiation
    if not test_class_instantiation():
        success = False
        
    # Test wizard methods
    if not test_wizard_methods():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ DeformViz 3D should start without AttributeError issues")
        print("✅ All method connections are valid")
        print("✅ Analysis Wizard integration is complete")
        print("\n💡 Note: Qt platform issues in this environment are expected")
        print("   The application should work fine in a proper desktop environment")
    else:
        print("❌ SOME TESTS FAILED!")
        print("   Please check the error messages above")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
