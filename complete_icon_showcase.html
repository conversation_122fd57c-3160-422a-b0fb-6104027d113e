
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeformViz 3D - Complete Icon Showcase</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #4A90E2;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
            font-size: 1.2em;
        }
        .app-icon-section {
            text-align: center;
            margin-bottom: 50px;
            padding: 30px;
            background: linear-gradient(135deg, #4A90E2, #2E5BBA);
            border-radius: 15px;
            color: white;
        }
        .app-icon {
            width: 128px;
            height: 128px;
            margin: 20px auto;
            display: block;
            filter: drop-shadow(0 10px 20px rgba(0,0,0,0.3));
        }
        .icon-section {
            margin-bottom: 40px;
        }
        .icon-section h2 {
            color: #4A90E2;
            border-bottom: 2px solid #4A90E2;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .icon-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid #e9ecef;
        }
        .icon-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
        }
        .icon-card img {
            width: 40px;
            height: 40px;
            margin-bottom: 8px;
        }
        .icon-name {
            font-weight: bold;
            color: #495057;
            margin-bottom: 3px;
            font-size: 0.9em;
        }
        .icon-description {
            font-size: 0.8em;
            color: #6c757d;
        }
        .features {
            margin-top: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .features h3 {
            color: #4A90E2;
            margin-bottom: 15px;
        }
        .features ul {
            list-style-type: none;
            padding: 0;
        }
        .features li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }
        .features li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        .toolbar-layout {
            display: flex;
            gap: 30px;
            margin-top: 30px;
        }
        .toolbar-demo {
            flex: 1;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #e9ecef;
        }
        .toolbar-demo h3 {
            color: #495057;
            margin-bottom: 15px;
            text-align: center;
        }
        .vertical-toolbar {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: center;
        }
        .horizontal-toolbar {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }
        .toolbar-icon {
            width: 32px;
            height: 32px;
            padding: 8px;
            background: white;
            border-radius: 5px;
            border: 1px solid #ddd;
            transition: all 0.2s ease;
        }
        .toolbar-icon:hover {
            background: #e3f2fd;
            border-color: #4A90E2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DeformViz 3D</h1>
        <p class="subtitle">3D Deformation Analysis Suite - Complete Icon Collection</p>
        
        <div class="app-icon-section">
            <h2>Application Icon</h2>
            <img src="data/icons/app_icon.svg" alt="DeformViz 3D App Icon" class="app-icon">
            <p>The main application icon representing 3D mesh analysis with heatmap visualization</p>
        </div>
        
        <div class="toolbar-layout">
            <div class="toolbar-demo">
                <h3>🔧 Main Toolbar (Top)</h3>
                <div class="horizontal-toolbar">
                    <img src="data/icons/load.svg" class="toolbar-icon" title="Load Mesh">
                    <img src="data/icons/save.svg" class="toolbar-icon" title="Save Project">
                    <img src="data/icons/settings.svg" class="toolbar-icon" title="Settings">
                    <img src="data/icons/analyze.svg" class="toolbar-icon" title="Analyze">
                    <img src="data/icons/plane.svg" class="toolbar-icon" title="Define Plane">
                    <img src="data/icons/visualize.svg" class="toolbar-icon" title="Visualize">
                    <img src="data/icons/export.svg" class="toolbar-icon" title="Export">
                    <img src="data/icons/screenshot.svg" class="toolbar-icon" title="Screenshot">
                    <img src="data/icons/help.svg" class="toolbar-icon" title="Help">
                </div>
            </div>
            
            <div class="toolbar-demo">
                <h3>🎮 View Controls (Left)</h3>
                <div class="vertical-toolbar">
                    <img src="data/icons/view_perspective_ortho.svg" class="toolbar-icon" title="Perspective/Ortho">
                    <div style="height: 5px; width: 20px; background: #ddd; margin: 5px 0;"></div>
                    <img src="data/icons/view_top.svg" class="toolbar-icon" title="Top View">
                    <img src="data/icons/view_bottom.svg" class="toolbar-icon" title="Bottom View">
                    <img src="data/icons/view_front.svg" class="toolbar-icon" title="Front View">
                    <img src="data/icons/view_back.svg" class="toolbar-icon" title="Back View">
                    <img src="data/icons/view_left.svg" class="toolbar-icon" title="Left View">
                    <img src="data/icons/view_right.svg" class="toolbar-icon" title="Right View">
                    <div style="height: 5px; width: 20px; background: #ddd; margin: 5px 0;"></div>
                    <img src="data/icons/zoom_all.svg" class="toolbar-icon" title="Zoom All">
                    <img src="data/icons/zoom_window.svg" class="toolbar-icon" title="Zoom Window">
                </div>
            </div>
        </div>
        
        <div class="icon-section">
            <h2>Main Toolbar Icons</h2>
            <div class="icon-grid">
                <div class="icon-card">
                    <img src="data/icons/load.svg" alt="Load">
                    <div class="icon-name">Load Mesh</div>
                    <div class="icon-description">Load 3D mesh files for analysis</div>
                </div>
                <div class="icon-card">
                    <img src="data/icons/save.svg" alt="Save">
                    <div class="icon-name">Save Project</div>
                    <div class="icon-description">Save current analysis project</div>
                </div>
                <div class="icon-card">
                    <img src="data/icons/analyze.svg" alt="Analyze">
                    <div class="icon-name">Analyze</div>
                    <div class="icon-description">Perform deformation analysis</div>
                </div>
                <div class="icon-card">
                    <img src="data/icons/visualize.svg" alt="Visualize">
                    <div class="icon-name">Visualize</div>
                    <div class="icon-description">Display heatmap visualization</div>
                </div>
                <div class="icon-card">
                    <img src="data/icons/plane.svg" alt="Plane">
                    <div class="icon-name">Define Plane</div>
                    <div class="icon-description">Set reference plane for analysis</div>
                </div>
                <div class="icon-card">
                    <img src="data/icons/settings.svg" alt="Settings">
                    <div class="icon-name">Settings</div>
                    <div class="icon-description">Configure application settings</div>
                </div>
                <div class="icon-card">
                    <img src="data/icons/export.svg" alt="Export">
                    <div class="icon-name">Export</div>
                    <div class="icon-description">Export results and data</div>
                </div>
                <div class="icon-card">
                    <img src="data/icons/screenshot.svg" alt="Screenshot">
                    <div class="icon-name">Screenshot</div>
                    <div class="icon-description">Capture current view</div>
                </div>
                <div class="icon-card">
                    <img src="data/icons/help.svg" alt="Help">
                    <div class="icon-name">Help</div>
                    <div class="icon-description">Show help and documentation</div>
                </div>
            </div>
        </div>
        
        <div class="icon-section">
            <h2>View Control Icons</h2>
            <div class="icon-grid">
                <div class="icon-card">
                    <img src="data/icons/view_perspective_ortho.svg" alt="Projection">
                    <div class="icon-name">Perspective/Ortho</div>
                    <div class="icon-description">Toggle projection mode</div>
                </div>
                <div class="icon-card">
                    <img src="data/icons/view_top.svg" alt="Top">
                    <div class="icon-name">Top View</div>
                    <div class="icon-description">View from above</div>
                </div>
                <div class="icon-card">
                    <img src="data/icons/view_bottom.svg" alt="Bottom">
                    <div class="icon-name">Bottom View</div>
                    <div class="icon-description">View from below</div>
                </div>
                <div class="icon-card">
                    <img src="data/icons/view_front.svg" alt="Front">
                    <div class="icon-name">Front View</div>
                    <div class="icon-description">View from front</div>
                </div>
                <div class="icon-card">
                    <img src="data/icons/view_back.svg" alt="Back">
                    <div class="icon-name">Back View</div>
                    <div class="icon-description">View from back</div>
                </div>
                <div class="icon-card">
                    <img src="data/icons/view_left.svg" alt="Left">
                    <div class="icon-name">Left View</div>
                    <div class="icon-description">View from left side</div>
                </div>
                <div class="icon-card">
                    <img src="data/icons/view_right.svg" alt="Right">
                    <div class="icon-name">Right View</div>
                    <div class="icon-description">View from right side</div>
                </div>
                <div class="icon-card">
                    <img src="data/icons/zoom_all.svg" alt="Zoom All">
                    <div class="icon-name">Zoom All</div>
                    <div class="icon-description">Fit all objects in view</div>
                </div>
                <div class="icon-card">
                    <img src="data/icons/zoom_window.svg" alt="Zoom Window">
                    <div class="icon-name">Zoom Window</div>
                    <div class="icon-description">Zoom to selected region</div>
                </div>
            </div>
        </div>
        
        <div class="features">
            <h3>🎨 Complete Icon Design System</h3>
            <ul>
                <li>Professional, modern design with consistent color schemes</li>
                <li>High contrast for excellent visibility in all lighting conditions</li>
                <li>Scalable SVG format for crisp display at any resolution</li>
                <li>Intuitive symbols that clearly represent their functions</li>
                <li>Cohesive visual language across all interface elements</li>
                <li>Color-coded categories: Blue (main actions), Green (views), Orange (analysis)</li>
                <li>Optimized for both light and dark themes</li>
                <li>Accessibility-compliant contrast ratios</li>
            </ul>
            
            <h3>🚀 DeformViz 3D Features</h3>
            <ul>
                <li>Advanced 3D mesh visualization and analysis</li>
                <li>Real-time deformation heatmap generation</li>
                <li>Interactive plane definition tools</li>
                <li>Statistical analysis and anomaly detection</li>
                <li>Professional export capabilities</li>
                <li>Comprehensive mesh processing tools</li>
                <li>Multiple view modes and camera controls</li>
                <li>Intuitive user interface with professional icons</li>
            </ul>
        </div>
    </div>
</body>
</html>
